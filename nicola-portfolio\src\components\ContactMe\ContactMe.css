.contact-container {
    margin: 4rem 0;
    position: relative;
}

.contact-container h5 {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 3rem;
}

.contact-content {
    display: flex; 
    flex-direction: row; 
    gap: 4rem; 
    justify-content: center; 
    align-items: center; 
}

.contact-info-card {
    padding: 1rem;
    transition: background-color 0.3s ease;
    border-radius: 8px;
    width: 400px; 
    box-sizing: border-box;
}

.contact-info-card:hover {
    background-color: var(--secondary-color);
    cursor: pointer;
}

@media (max-width: 768px) {
    .contact-content {
        flex-direction: column; 
        gap: 1.5rem;
    }

    .contact-container h5 {
        font-size: 1.3rem;
        margin-bottom: 0rem;
    }
}

@media (max-width: 480px) {
  .contact-container {
    margin: 2rem 0;
  }
  
  .contact-info-card {
    width: 100%;
    padding: 0.5rem;
  }
}
