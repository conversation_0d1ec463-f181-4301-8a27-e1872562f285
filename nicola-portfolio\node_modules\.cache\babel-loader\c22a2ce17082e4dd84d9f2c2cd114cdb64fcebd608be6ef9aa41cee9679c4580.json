{"ast": null, "code": "import { BufferGeometry, Float32BufferAttribute } from \"three\";\nimport { ConvexHull } from \"../math/ConvexHull.js\";\nclass ConvexGeometry extends BufferGeometry {\n  constructor(points = []) {\n    super();\n    const vertices = [];\n    const normals = [];\n    const convexHull = new ConvexHull().setFromPoints(points);\n    const faces = convexHull.faces;\n    for (let i = 0; i < faces.length; i++) {\n      const face = faces[i];\n      let edge = face.edge;\n      do {\n        const point = edge.head().point;\n        vertices.push(point.x, point.y, point.z);\n        normals.push(face.normal.x, face.normal.y, face.normal.z);\n        edge = edge.next;\n      } while (edge !== face.edge);\n    }\n    this.setAttribute(\"position\", new Float32BufferAttribute(vertices, 3));\n    this.setAttribute(\"normal\", new Float32BufferAttribute(normals, 3));\n  }\n}\nexport { ConvexGeometry };", "map": {"version": 3, "names": ["ConvexGeometry", "BufferGeometry", "constructor", "points", "vertices", "normals", "convexHull", "ConvexHull", "setFromPoints", "faces", "i", "length", "face", "edge", "point", "head", "push", "x", "y", "z", "normal", "next", "setAttribute", "Float32BufferAttribute"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\geometries\\ConvexGeometry.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute } from 'three'\nimport { ConvexHull } from '../math/ConvexHull'\n\nclass ConvexGeometry extends BufferGeometry {\n  constructor(points = []) {\n    super()\n\n    // buffers\n\n    const vertices = []\n    const normals = []\n\n    const convexHull = new ConvexHull().setFromPoints(points)\n\n    // generate vertices and normals\n\n    const faces = convexHull.faces\n\n    for (let i = 0; i < faces.length; i++) {\n      const face = faces[i]\n      let edge = face.edge\n\n      // we move along a doubly-connected edge list to access all face points (see HalfEdge docs)\n\n      do {\n        const point = edge.head().point\n\n        vertices.push(point.x, point.y, point.z)\n        normals.push(face.normal.x, face.normal.y, face.normal.z)\n\n        edge = edge.next\n      } while (edge !== face.edge)\n    }\n\n    // build geometry\n\n    this.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n    this.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n  }\n}\n\nexport { ConvexGeometry }\n"], "mappings": ";;AAGA,MAAMA,cAAA,SAAuBC,cAAA,CAAe;EAC1CC,YAAYC,MAAA,GAAS,IAAI;IACvB,MAAO;IAIP,MAAMC,QAAA,GAAW,EAAE;IACnB,MAAMC,OAAA,GAAU,EAAE;IAElB,MAAMC,UAAA,GAAa,IAAIC,UAAA,GAAaC,aAAA,CAAcL,MAAM;IAIxD,MAAMM,KAAA,GAAQH,UAAA,CAAWG,KAAA;IAEzB,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAID,KAAA,CAAME,MAAA,EAAQD,CAAA,IAAK;MACrC,MAAME,IAAA,GAAOH,KAAA,CAAMC,CAAC;MACpB,IAAIG,IAAA,GAAOD,IAAA,CAAKC,IAAA;MAIhB,GAAG;QACD,MAAMC,KAAA,GAAQD,IAAA,CAAKE,IAAA,CAAI,EAAGD,KAAA;QAE1BV,QAAA,CAASY,IAAA,CAAKF,KAAA,CAAMG,CAAA,EAAGH,KAAA,CAAMI,CAAA,EAAGJ,KAAA,CAAMK,CAAC;QACvCd,OAAA,CAAQW,IAAA,CAAKJ,IAAA,CAAKQ,MAAA,CAAOH,CAAA,EAAGL,IAAA,CAAKQ,MAAA,CAAOF,CAAA,EAAGN,IAAA,CAAKQ,MAAA,CAAOD,CAAC;QAExDN,IAAA,GAAOA,IAAA,CAAKQ,IAAA;MACpB,SAAeR,IAAA,KAASD,IAAA,CAAKC,IAAA;IACxB;IAID,KAAKS,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBnB,QAAA,EAAU,CAAC,CAAC;IACrE,KAAKkB,YAAA,CAAa,UAAU,IAAIC,sBAAA,CAAuBlB,OAAA,EAAS,CAAC,CAAC;EACnE;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}