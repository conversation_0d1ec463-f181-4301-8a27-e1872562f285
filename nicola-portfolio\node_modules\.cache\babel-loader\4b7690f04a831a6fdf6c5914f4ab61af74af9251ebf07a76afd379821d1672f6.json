{"ast": null, "code": "import { PerspectiveC<PERSON>ra, Vector3, Quaternion } from \"three\";\nclass PeppersGhostEffect {\n  constructor(renderer) {\n    const scope = this;\n    scope.cameraDistance = 15;\n    scope.reflectFromAbove = false;\n    let _halfWidth, _width, _height;\n    const _cameraF = new PerspectiveCamera();\n    const _cameraB = new PerspectiveCamera();\n    const _cameraL = new PerspectiveCamera();\n    const _cameraR = new PerspectiveCamera();\n    const _position = new Vector3();\n    const _quaternion = new Quaternion();\n    const _scale = new Vector3();\n    renderer.autoClear = false;\n    this.setSize = function (width, height) {\n      _halfWidth = width / 2;\n      if (width < height) {\n        _width = width / 3;\n        _height = width / 3;\n      } else {\n        _width = height / 3;\n        _height = height / 3;\n      }\n      renderer.setSize(width, height);\n    };\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld();\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld();\n      camera.matrixWorld.decompose(_position, _quaternion, _scale);\n      _cameraF.position.copy(_position);\n      _cameraF.quaternion.copy(_quaternion);\n      _cameraF.translateZ(scope.cameraDistance);\n      _cameraF.lookAt(scene.position);\n      _cameraB.position.copy(_position);\n      _cameraB.quaternion.copy(_quaternion);\n      _cameraB.translateZ(-scope.cameraDistance);\n      _cameraB.lookAt(scene.position);\n      _cameraB.rotation.z += 180 * (Math.PI / 180);\n      _cameraL.position.copy(_position);\n      _cameraL.quaternion.copy(_quaternion);\n      _cameraL.translateX(-scope.cameraDistance);\n      _cameraL.lookAt(scene.position);\n      _cameraL.rotation.x += 90 * (Math.PI / 180);\n      _cameraR.position.copy(_position);\n      _cameraR.quaternion.copy(_quaternion);\n      _cameraR.translateX(scope.cameraDistance);\n      _cameraR.lookAt(scene.position);\n      _cameraR.rotation.x += 90 * (Math.PI / 180);\n      renderer.clear();\n      renderer.setScissorTest(true);\n      renderer.setScissor(_halfWidth - _width / 2, _height * 2, _width, _height);\n      renderer.setViewport(_halfWidth - _width / 2, _height * 2, _width, _height);\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraB);\n      } else {\n        renderer.render(scene, _cameraF);\n      }\n      renderer.setScissor(_halfWidth - _width / 2, 0, _width, _height);\n      renderer.setViewport(_halfWidth - _width / 2, 0, _width, _height);\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraF);\n      } else {\n        renderer.render(scene, _cameraB);\n      }\n      renderer.setScissor(_halfWidth - _width / 2 - _width, _height, _width, _height);\n      renderer.setViewport(_halfWidth - _width / 2 - _width, _height, _width, _height);\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraR);\n      } else {\n        renderer.render(scene, _cameraL);\n      }\n      renderer.setScissor(_halfWidth + _width / 2, _height, _width, _height);\n      renderer.setViewport(_halfWidth + _width / 2, _height, _width, _height);\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraL);\n      } else {\n        renderer.render(scene, _cameraR);\n      }\n      renderer.setScissorTest(false);\n    };\n  }\n}\nexport { PeppersGhostEffect };", "map": {"version": 3, "names": ["PeppersGhostEffect", "constructor", "renderer", "scope", "cameraDistance", "reflectFromAbove", "_halfWidth", "_width", "_height", "_cameraF", "PerspectiveCamera", "_cameraB", "_cameraL", "_cameraR", "_position", "Vector3", "_quaternion", "Quaternion", "_scale", "autoClear", "setSize", "width", "height", "render", "scene", "camera", "matrixWorldAutoUpdate", "updateMatrixWorld", "parent", "matrixWorld", "decompose", "position", "copy", "quaternion", "translateZ", "lookAt", "rotation", "z", "Math", "PI", "translateX", "x", "clear", "setScissorTest", "set<PERSON><PERSON>sor", "setViewport"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\effects\\PeppersGhostEffect.js"], "sourcesContent": ["import { Perspective<PERSON>amera, Quaternion, Vector3 } from 'three'\n\n/**\n * peppers ghost effect based on http://www.instructables.com/id/Reflective-Prism/?ALLSTEPS\n */\n\nclass PeppersGhostEffect {\n  constructor(renderer) {\n    const scope = this\n\n    scope.cameraDistance = 15\n    scope.reflectFromAbove = false\n\n    // Internals\n    let _halfWidth, _width, _height\n\n    const _cameraF = new PerspectiveCamera() //front\n    const _cameraB = new PerspectiveCamera() //back\n    const _cameraL = new PerspectiveCamera() //left\n    const _cameraR = new PerspectiveCamera() //right\n\n    const _position = new Vector3()\n    const _quaternion = new Quaternion()\n    const _scale = new Vector3()\n\n    // Initialization\n    renderer.autoClear = false\n\n    this.setSize = function (width, height) {\n      _halfWidth = width / 2\n      if (width < height) {\n        _width = width / 3\n        _height = width / 3\n      } else {\n        _width = height / 3\n        _height = height / 3\n      }\n\n      renderer.setSize(width, height)\n    }\n\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      camera.matrixWorld.decompose(_position, _quaternion, _scale)\n\n      // front\n      _cameraF.position.copy(_position)\n      _cameraF.quaternion.copy(_quaternion)\n      _cameraF.translateZ(scope.cameraDistance)\n      _cameraF.lookAt(scene.position)\n\n      // back\n      _cameraB.position.copy(_position)\n      _cameraB.quaternion.copy(_quaternion)\n      _cameraB.translateZ(-scope.cameraDistance)\n      _cameraB.lookAt(scene.position)\n      _cameraB.rotation.z += 180 * (Math.PI / 180)\n\n      // left\n      _cameraL.position.copy(_position)\n      _cameraL.quaternion.copy(_quaternion)\n      _cameraL.translateX(-scope.cameraDistance)\n      _cameraL.lookAt(scene.position)\n      _cameraL.rotation.x += 90 * (Math.PI / 180)\n\n      // right\n      _cameraR.position.copy(_position)\n      _cameraR.quaternion.copy(_quaternion)\n      _cameraR.translateX(scope.cameraDistance)\n      _cameraR.lookAt(scene.position)\n      _cameraR.rotation.x += 90 * (Math.PI / 180)\n\n      renderer.clear()\n      renderer.setScissorTest(true)\n\n      renderer.setScissor(_halfWidth - _width / 2, _height * 2, _width, _height)\n      renderer.setViewport(_halfWidth - _width / 2, _height * 2, _width, _height)\n\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraB)\n      } else {\n        renderer.render(scene, _cameraF)\n      }\n\n      renderer.setScissor(_halfWidth - _width / 2, 0, _width, _height)\n      renderer.setViewport(_halfWidth - _width / 2, 0, _width, _height)\n\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraF)\n      } else {\n        renderer.render(scene, _cameraB)\n      }\n\n      renderer.setScissor(_halfWidth - _width / 2 - _width, _height, _width, _height)\n      renderer.setViewport(_halfWidth - _width / 2 - _width, _height, _width, _height)\n\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraR)\n      } else {\n        renderer.render(scene, _cameraL)\n      }\n\n      renderer.setScissor(_halfWidth + _width / 2, _height, _width, _height)\n      renderer.setViewport(_halfWidth + _width / 2, _height, _width, _height)\n\n      if (scope.reflectFromAbove) {\n        renderer.render(scene, _cameraL)\n      } else {\n        renderer.render(scene, _cameraR)\n      }\n\n      renderer.setScissorTest(false)\n    }\n  }\n}\n\nexport { PeppersGhostEffect }\n"], "mappings": ";AAMA,MAAMA,kBAAA,CAAmB;EACvBC,YAAYC,QAAA,EAAU;IACpB,MAAMC,KAAA,GAAQ;IAEdA,KAAA,CAAMC,cAAA,GAAiB;IACvBD,KAAA,CAAME,gBAAA,GAAmB;IAGzB,IAAIC,UAAA,EAAYC,MAAA,EAAQC,OAAA;IAExB,MAAMC,QAAA,GAAW,IAAIC,iBAAA,CAAmB;IACxC,MAAMC,QAAA,GAAW,IAAID,iBAAA,CAAmB;IACxC,MAAME,QAAA,GAAW,IAAIF,iBAAA,CAAmB;IACxC,MAAMG,QAAA,GAAW,IAAIH,iBAAA,CAAmB;IAExC,MAAMI,SAAA,GAAY,IAAIC,OAAA,CAAS;IAC/B,MAAMC,WAAA,GAAc,IAAIC,UAAA,CAAY;IACpC,MAAMC,MAAA,GAAS,IAAIH,OAAA,CAAS;IAG5Bb,QAAA,CAASiB,SAAA,GAAY;IAErB,KAAKC,OAAA,GAAU,UAAUC,KAAA,EAAOC,MAAA,EAAQ;MACtChB,UAAA,GAAae,KAAA,GAAQ;MACrB,IAAIA,KAAA,GAAQC,MAAA,EAAQ;QAClBf,MAAA,GAASc,KAAA,GAAQ;QACjBb,OAAA,GAAUa,KAAA,GAAQ;MAC1B,OAAa;QACLd,MAAA,GAASe,MAAA,GAAS;QAClBd,OAAA,GAAUc,MAAA,GAAS;MACpB;MAEDpB,QAAA,CAASkB,OAAA,CAAQC,KAAA,EAAOC,MAAM;IAC/B;IAED,KAAKC,MAAA,GAAS,UAAUC,KAAA,EAAOC,MAAA,EAAQ;MACrC,IAAID,KAAA,CAAME,qBAAA,KAA0B,MAAMF,KAAA,CAAMG,iBAAA,CAAmB;MAEnE,IAAIF,MAAA,CAAOG,MAAA,KAAW,QAAQH,MAAA,CAAOC,qBAAA,KAA0B,MAAMD,MAAA,CAAOE,iBAAA,CAAmB;MAE/FF,MAAA,CAAOI,WAAA,CAAYC,SAAA,CAAUhB,SAAA,EAAWE,WAAA,EAAaE,MAAM;MAG3DT,QAAA,CAASsB,QAAA,CAASC,IAAA,CAAKlB,SAAS;MAChCL,QAAA,CAASwB,UAAA,CAAWD,IAAA,CAAKhB,WAAW;MACpCP,QAAA,CAASyB,UAAA,CAAW/B,KAAA,CAAMC,cAAc;MACxCK,QAAA,CAAS0B,MAAA,CAAOX,KAAA,CAAMO,QAAQ;MAG9BpB,QAAA,CAASoB,QAAA,CAASC,IAAA,CAAKlB,SAAS;MAChCH,QAAA,CAASsB,UAAA,CAAWD,IAAA,CAAKhB,WAAW;MACpCL,QAAA,CAASuB,UAAA,CAAW,CAAC/B,KAAA,CAAMC,cAAc;MACzCO,QAAA,CAASwB,MAAA,CAAOX,KAAA,CAAMO,QAAQ;MAC9BpB,QAAA,CAASyB,QAAA,CAASC,CAAA,IAAK,OAAOC,IAAA,CAAKC,EAAA,GAAK;MAGxC3B,QAAA,CAASmB,QAAA,CAASC,IAAA,CAAKlB,SAAS;MAChCF,QAAA,CAASqB,UAAA,CAAWD,IAAA,CAAKhB,WAAW;MACpCJ,QAAA,CAAS4B,UAAA,CAAW,CAACrC,KAAA,CAAMC,cAAc;MACzCQ,QAAA,CAASuB,MAAA,CAAOX,KAAA,CAAMO,QAAQ;MAC9BnB,QAAA,CAASwB,QAAA,CAASK,CAAA,IAAK,MAAMH,IAAA,CAAKC,EAAA,GAAK;MAGvC1B,QAAA,CAASkB,QAAA,CAASC,IAAA,CAAKlB,SAAS;MAChCD,QAAA,CAASoB,UAAA,CAAWD,IAAA,CAAKhB,WAAW;MACpCH,QAAA,CAAS2B,UAAA,CAAWrC,KAAA,CAAMC,cAAc;MACxCS,QAAA,CAASsB,MAAA,CAAOX,KAAA,CAAMO,QAAQ;MAC9BlB,QAAA,CAASuB,QAAA,CAASK,CAAA,IAAK,MAAMH,IAAA,CAAKC,EAAA,GAAK;MAEvCrC,QAAA,CAASwC,KAAA,CAAO;MAChBxC,QAAA,CAASyC,cAAA,CAAe,IAAI;MAE5BzC,QAAA,CAAS0C,UAAA,CAAWtC,UAAA,GAAaC,MAAA,GAAS,GAAGC,OAAA,GAAU,GAAGD,MAAA,EAAQC,OAAO;MACzEN,QAAA,CAAS2C,WAAA,CAAYvC,UAAA,GAAaC,MAAA,GAAS,GAAGC,OAAA,GAAU,GAAGD,MAAA,EAAQC,OAAO;MAE1E,IAAIL,KAAA,CAAME,gBAAA,EAAkB;QAC1BH,QAAA,CAASqB,MAAA,CAAOC,KAAA,EAAOb,QAAQ;MACvC,OAAa;QACLT,QAAA,CAASqB,MAAA,CAAOC,KAAA,EAAOf,QAAQ;MAChC;MAEDP,QAAA,CAAS0C,UAAA,CAAWtC,UAAA,GAAaC,MAAA,GAAS,GAAG,GAAGA,MAAA,EAAQC,OAAO;MAC/DN,QAAA,CAAS2C,WAAA,CAAYvC,UAAA,GAAaC,MAAA,GAAS,GAAG,GAAGA,MAAA,EAAQC,OAAO;MAEhE,IAAIL,KAAA,CAAME,gBAAA,EAAkB;QAC1BH,QAAA,CAASqB,MAAA,CAAOC,KAAA,EAAOf,QAAQ;MACvC,OAAa;QACLP,QAAA,CAASqB,MAAA,CAAOC,KAAA,EAAOb,QAAQ;MAChC;MAEDT,QAAA,CAAS0C,UAAA,CAAWtC,UAAA,GAAaC,MAAA,GAAS,IAAIA,MAAA,EAAQC,OAAA,EAASD,MAAA,EAAQC,OAAO;MAC9EN,QAAA,CAAS2C,WAAA,CAAYvC,UAAA,GAAaC,MAAA,GAAS,IAAIA,MAAA,EAAQC,OAAA,EAASD,MAAA,EAAQC,OAAO;MAE/E,IAAIL,KAAA,CAAME,gBAAA,EAAkB;QAC1BH,QAAA,CAASqB,MAAA,CAAOC,KAAA,EAAOX,QAAQ;MACvC,OAAa;QACLX,QAAA,CAASqB,MAAA,CAAOC,KAAA,EAAOZ,QAAQ;MAChC;MAEDV,QAAA,CAAS0C,UAAA,CAAWtC,UAAA,GAAaC,MAAA,GAAS,GAAGC,OAAA,EAASD,MAAA,EAAQC,OAAO;MACrEN,QAAA,CAAS2C,WAAA,CAAYvC,UAAA,GAAaC,MAAA,GAAS,GAAGC,OAAA,EAASD,MAAA,EAAQC,OAAO;MAEtE,IAAIL,KAAA,CAAME,gBAAA,EAAkB;QAC1BH,QAAA,CAASqB,MAAA,CAAOC,KAAA,EAAOZ,QAAQ;MACvC,OAAa;QACLV,QAAA,CAASqB,MAAA,CAAOC,KAAA,EAAOX,QAAQ;MAChC;MAEDX,QAAA,CAASyC,cAAA,CAAe,KAAK;IAC9B;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}