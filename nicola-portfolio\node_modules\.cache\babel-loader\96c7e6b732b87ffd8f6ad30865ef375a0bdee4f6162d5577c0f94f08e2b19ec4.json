{"ast": null, "code": "/**\n * This function performs intersection tests similar to Ray.intersectBox in three.js,\n * with the difference that the box values are read from an array to improve performance.\n */\nexport function intersectRay(nodeIndex32, array, ray, near, far) {\n  let tmin, tmax, tymin, tymax, tzmin, tzmax;\n  const invdirx = 1 / ray.direction.x,\n    invdiry = 1 / ray.direction.y,\n    invdirz = 1 / ray.direction.z;\n  const ox = ray.origin.x;\n  const oy = ray.origin.y;\n  const oz = ray.origin.z;\n  let minx = array[nodeIndex32];\n  let maxx = array[nodeIndex32 + 3];\n  let miny = array[nodeIndex32 + 1];\n  let maxy = array[nodeIndex32 + 3 + 1];\n  let minz = array[nodeIndex32 + 2];\n  let maxz = array[nodeIndex32 + 3 + 2];\n  if (invdirx >= 0) {\n    tmin = (minx - ox) * invdirx;\n    tmax = (maxx - ox) * invdirx;\n  } else {\n    tmin = (maxx - ox) * invdirx;\n    tmax = (minx - ox) * invdirx;\n  }\n  if (invdiry >= 0) {\n    tymin = (miny - oy) * invdiry;\n    tymax = (maxy - oy) * invdiry;\n  } else {\n    tymin = (maxy - oy) * invdiry;\n    tymax = (miny - oy) * invdiry;\n  }\n  if (tmin > tymax || tymin > tmax) return false;\n  if (tymin > tmin || isNaN(tmin)) tmin = tymin;\n  if (tymax < tmax || isNaN(tmax)) tmax = tymax;\n  if (invdirz >= 0) {\n    tzmin = (minz - oz) * invdirz;\n    tzmax = (maxz - oz) * invdirz;\n  } else {\n    tzmin = (maxz - oz) * invdirz;\n    tzmax = (minz - oz) * invdirz;\n  }\n  if (tmin > tzmax || tzmin > tmax) return false;\n  if (tzmin > tmin || tmin !== tmin) tmin = tzmin;\n  if (tzmax < tmax || tmax !== tmax) tmax = tzmax;\n\n  //return point closest to the ray (positive side)\n\n  return tmin <= far && tmax >= near;\n}", "map": {"version": 3, "names": ["intersectRay", "nodeIndex32", "array", "ray", "near", "far", "tmin", "tmax", "tymin", "tymax", "tzmin", "tzmax", "invdirx", "direction", "x", "invdiry", "y", "invdirz", "z", "ox", "origin", "oy", "oz", "minx", "maxx", "miny", "maxy", "minz", "maxz", "isNaN"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/node_modules/three-mesh-bvh/src/core/utils/intersectUtils.js"], "sourcesContent": ["/**\n * This function performs intersection tests similar to Ray.intersectBox in three.js,\n * with the difference that the box values are read from an array to improve performance.\n */\nexport function intersectRay( nodeIndex32, array, ray, near, far ) {\n\n\tlet tmin, tmax, tymin, tymax, tzmin, tzmax;\n\n\tconst invdirx = 1 / ray.direction.x,\n\t\tinvdiry = 1 / ray.direction.y,\n\t\tinvdirz = 1 / ray.direction.z;\n\n\tconst ox = ray.origin.x;\n\tconst oy = ray.origin.y;\n\tconst oz = ray.origin.z;\n\n\tlet minx = array[ nodeIndex32 ];\n\tlet maxx = array[ nodeIndex32 + 3 ];\n\n\tlet miny = array[ nodeIndex32 + 1 ];\n\tlet maxy = array[ nodeIndex32 + 3 + 1 ];\n\n\tlet minz = array[ nodeIndex32 + 2 ];\n\tlet maxz = array[ nodeIndex32 + 3 + 2 ];\n\n\tif ( invdirx >= 0 ) {\n\n\t\ttmin = ( minx - ox ) * invdirx;\n\t\ttmax = ( maxx - ox ) * invdirx;\n\n\t} else {\n\n\t\ttmin = ( maxx - ox ) * invdirx;\n\t\ttmax = ( minx - ox ) * invdirx;\n\n\t}\n\n\tif ( invdiry >= 0 ) {\n\n\t\ttymin = ( miny - oy ) * invdiry;\n\t\ttymax = ( maxy - oy ) * invdiry;\n\n\t} else {\n\n\t\ttymin = ( maxy - oy ) * invdiry;\n\t\ttymax = ( miny - oy ) * invdiry;\n\n\t}\n\n\tif ( ( tmin > tymax ) || ( tymin > tmax ) ) return false;\n\n\tif ( tymin > tmin || isNaN( tmin ) ) tmin = tymin;\n\n\tif ( tymax < tmax || isNaN( tmax ) ) tmax = tymax;\n\n\tif ( invdirz >= 0 ) {\n\n\t\ttzmin = ( minz - oz ) * invdirz;\n\t\ttzmax = ( maxz - oz ) * invdirz;\n\n\t} else {\n\n\t\ttzmin = ( maxz - oz ) * invdirz;\n\t\ttzmax = ( minz - oz ) * invdirz;\n\n\t}\n\n\tif ( ( tmin > tzmax ) || ( tzmin > tmax ) ) return false;\n\n\tif ( tzmin > tmin || tmin !== tmin ) tmin = tzmin;\n\n\tif ( tzmax < tmax || tmax !== tmax ) tmax = tzmax;\n\n\t//return point closest to the ray (positive side)\n\n\treturn tmin <= far && tmax >= near;\n\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,YAAYA,CAAEC,WAAW,EAAEC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAG;EAElE,IAAIC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK;EAE1C,MAAMC,OAAO,GAAG,CAAC,GAAGT,GAAG,CAACU,SAAS,CAACC,CAAC;IAClCC,OAAO,GAAG,CAAC,GAAGZ,GAAG,CAACU,SAAS,CAACG,CAAC;IAC7BC,OAAO,GAAG,CAAC,GAAGd,GAAG,CAACU,SAAS,CAACK,CAAC;EAE9B,MAAMC,EAAE,GAAGhB,GAAG,CAACiB,MAAM,CAACN,CAAC;EACvB,MAAMO,EAAE,GAAGlB,GAAG,CAACiB,MAAM,CAACJ,CAAC;EACvB,MAAMM,EAAE,GAAGnB,GAAG,CAACiB,MAAM,CAACF,CAAC;EAEvB,IAAIK,IAAI,GAAGrB,KAAK,CAAED,WAAW,CAAE;EAC/B,IAAIuB,IAAI,GAAGtB,KAAK,CAAED,WAAW,GAAG,CAAC,CAAE;EAEnC,IAAIwB,IAAI,GAAGvB,KAAK,CAAED,WAAW,GAAG,CAAC,CAAE;EACnC,IAAIyB,IAAI,GAAGxB,KAAK,CAAED,WAAW,GAAG,CAAC,GAAG,CAAC,CAAE;EAEvC,IAAI0B,IAAI,GAAGzB,KAAK,CAAED,WAAW,GAAG,CAAC,CAAE;EACnC,IAAI2B,IAAI,GAAG1B,KAAK,CAAED,WAAW,GAAG,CAAC,GAAG,CAAC,CAAE;EAEvC,IAAKW,OAAO,IAAI,CAAC,EAAG;IAEnBN,IAAI,GAAG,CAAEiB,IAAI,GAAGJ,EAAE,IAAKP,OAAO;IAC9BL,IAAI,GAAG,CAAEiB,IAAI,GAAGL,EAAE,IAAKP,OAAO;EAE/B,CAAC,MAAM;IAENN,IAAI,GAAG,CAAEkB,IAAI,GAAGL,EAAE,IAAKP,OAAO;IAC9BL,IAAI,GAAG,CAAEgB,IAAI,GAAGJ,EAAE,IAAKP,OAAO;EAE/B;EAEA,IAAKG,OAAO,IAAI,CAAC,EAAG;IAEnBP,KAAK,GAAG,CAAEiB,IAAI,GAAGJ,EAAE,IAAKN,OAAO;IAC/BN,KAAK,GAAG,CAAEiB,IAAI,GAAGL,EAAE,IAAKN,OAAO;EAEhC,CAAC,MAAM;IAENP,KAAK,GAAG,CAAEkB,IAAI,GAAGL,EAAE,IAAKN,OAAO;IAC/BN,KAAK,GAAG,CAAEgB,IAAI,GAAGJ,EAAE,IAAKN,OAAO;EAEhC;EAEA,IAAOT,IAAI,GAAGG,KAAK,IAAQD,KAAK,GAAGD,IAAM,EAAG,OAAO,KAAK;EAExD,IAAKC,KAAK,GAAGF,IAAI,IAAIuB,KAAK,CAAEvB,IAAK,CAAC,EAAGA,IAAI,GAAGE,KAAK;EAEjD,IAAKC,KAAK,GAAGF,IAAI,IAAIsB,KAAK,CAAEtB,IAAK,CAAC,EAAGA,IAAI,GAAGE,KAAK;EAEjD,IAAKQ,OAAO,IAAI,CAAC,EAAG;IAEnBP,KAAK,GAAG,CAAEiB,IAAI,GAAGL,EAAE,IAAKL,OAAO;IAC/BN,KAAK,GAAG,CAAEiB,IAAI,GAAGN,EAAE,IAAKL,OAAO;EAEhC,CAAC,MAAM;IAENP,KAAK,GAAG,CAAEkB,IAAI,GAAGN,EAAE,IAAKL,OAAO;IAC/BN,KAAK,GAAG,CAAEgB,IAAI,GAAGL,EAAE,IAAKL,OAAO;EAEhC;EAEA,IAAOX,IAAI,GAAGK,KAAK,IAAQD,KAAK,GAAGH,IAAM,EAAG,OAAO,KAAK;EAExD,IAAKG,KAAK,GAAGJ,IAAI,IAAIA,IAAI,KAAKA,IAAI,EAAGA,IAAI,GAAGI,KAAK;EAEjD,IAAKC,KAAK,GAAGJ,IAAI,IAAIA,IAAI,KAAKA,IAAI,EAAGA,IAAI,GAAGI,KAAK;;EAEjD;;EAEA,OAAOL,IAAI,IAAID,GAAG,IAAIE,IAAI,IAAIH,IAAI;AAEnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}