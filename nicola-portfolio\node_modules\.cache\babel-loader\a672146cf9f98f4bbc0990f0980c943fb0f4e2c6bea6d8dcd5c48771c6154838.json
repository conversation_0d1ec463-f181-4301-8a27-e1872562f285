{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nimport { Loader, RGBAFormat, RGBA_ASTC_4x4_Format, RGBA_BPTC_Format, RGBA_ETC2_EAC_Format, RGBA_PVRTC_4BPPV1_Format, RGBA_S3TC_DXT5_Format, RGB_ETC1_Format, RGB_ETC2_Format, RGB_PVRTC_4BPPV1_Format, RGB_S3TC_DXT1_Format, FileLoader, UnsignedByteType, CompressedTexture, LinearFilter, LinearMipmapLinearFilter, FloatType, HalfFloatType, DataTexture, RGFormat, RedFormat, RGBA_ASTC_6x6_Format } from \"three\";\nimport { WorkerPool } from \"../utils/WorkerPool.js\";\nimport { KHR_DF_FLAG_ALPHA_PREMULTIPLIED, read, VK_FORMAT_UNDEFINED, KHR_SUPERCOMPRESSION_ZSTD, KHR_SUPERCOMPRESSION_NONE, KHR_DF_PRIMARIES_BT709, KHR_DF_TRANSFER_SRGB, KHR_DF_PRIMARIES_DISPLAYP3, KHR_DF_PRIMARIES_UNSPECIFIED, VK_FORMAT_R32G32B32A32_SFLOAT, VK_FORMAT_R16G16B16A16_SFLOAT, VK_FORMAT_R8G8B8A8_UNORM, VK_FORMAT_R8G8B8A8_SRGB, VK_FORMAT_R32G32_SFLOAT, VK_FORMAT_R16G16_SFLOAT, VK_FORMAT_R8G8_UNORM, VK_FORMAT_R8G8_SRGB, VK_FORMAT_R32_SFLOAT, VK_FORMAT_R16_SFLOAT, VK_FORMAT_R8_SRGB, VK_FORMAT_R8_UNORM, VK_FORMAT_ASTC_6x6_SRGB_BLOCK, VK_FORMAT_ASTC_6x6_UNORM_BLOCK } from \"../libs/ktx-parse.js\";\nimport { ZSTDDecoder } from \"../libs/zstddec.js\";\nimport { CompressedCubeTexture } from \"../_polyfill/CompressedCubeTexture.js\";\nimport { CompressedArrayTexture } from \"../_polyfill/CompressedArrayTexture.js\";\nimport { Data3DTexture } from \"../_polyfill/Data3DTexture.js\";\nconst LinearEncoding = 3e3;\nconst sRGBEncoding = 3001;\nconst NoColorSpace = \"\";\nconst DisplayP3ColorSpace = \"display-p3\";\nconst LinearDisplayP3ColorSpace = \"display-p3-linear\";\nconst LinearSRGBColorSpace = \"srgb-linear\";\nconst SRGBColorSpace = \"srgb\";\nconst _taskCache = /* @__PURE__ */new WeakMap();\nlet _activeLoaders = 0;\nlet _zstd;\nconst KTX2Loader = /* @__PURE__ */(() => {\n  const _KTX2Loader = class extends Loader {\n    constructor(manager) {\n      super(manager);\n      this.transcoderPath = \"\";\n      this.transcoderBinary = null;\n      this.transcoderPending = null;\n      this.workerPool = new WorkerPool();\n      this.workerSourceURL = \"\";\n      this.workerConfig = null;\n      if (typeof MSC_TRANSCODER !== \"undefined\") {\n        console.warn('THREE.KTX2Loader: Please update to latest \"basis_transcoder\". \"msc_basis_transcoder\" is no longer supported in three.js r125+.');\n      }\n    }\n    setTranscoderPath(path) {\n      this.transcoderPath = path;\n      return this;\n    }\n    setWorkerLimit(num) {\n      this.workerPool.setWorkerLimit(num);\n      return this;\n    }\n    detectSupport(renderer) {\n      this.workerConfig = {\n        astcSupported: renderer.extensions.has(\"WEBGL_compressed_texture_astc\"),\n        etc1Supported: renderer.extensions.has(\"WEBGL_compressed_texture_etc1\"),\n        etc2Supported: renderer.extensions.has(\"WEBGL_compressed_texture_etc\"),\n        dxtSupported: renderer.extensions.has(\"WEBGL_compressed_texture_s3tc\"),\n        bptcSupported: renderer.extensions.has(\"EXT_texture_compression_bptc\"),\n        pvrtcSupported: renderer.extensions.has(\"WEBGL_compressed_texture_pvrtc\") || renderer.extensions.has(\"WEBKIT_WEBGL_compressed_texture_pvrtc\")\n      };\n      if (renderer.capabilities.isWebGL2) {\n        this.workerConfig.etc1Supported = false;\n      }\n      return this;\n    }\n    init() {\n      if (!this.transcoderPending) {\n        const jsLoader = new FileLoader(this.manager);\n        jsLoader.setPath(this.transcoderPath);\n        jsLoader.setWithCredentials(this.withCredentials);\n        const jsContent = jsLoader.loadAsync(\"basis_transcoder.js\");\n        const binaryLoader = new FileLoader(this.manager);\n        binaryLoader.setPath(this.transcoderPath);\n        binaryLoader.setResponseType(\"arraybuffer\");\n        binaryLoader.setWithCredentials(this.withCredentials);\n        const binaryContent = binaryLoader.loadAsync(\"basis_transcoder.wasm\");\n        this.transcoderPending = Promise.all([jsContent, binaryContent]).then(([jsContent2, binaryContent2]) => {\n          const fn = _KTX2Loader.BasisWorker.toString();\n          const body = [\"/* constants */\", \"let _EngineFormat = \" + JSON.stringify(_KTX2Loader.EngineFormat), \"let _TranscoderFormat = \" + JSON.stringify(_KTX2Loader.TranscoderFormat), \"let _BasisFormat = \" + JSON.stringify(_KTX2Loader.BasisFormat), \"/* basis_transcoder.js */\", jsContent2, \"/* worker */\", fn.substring(fn.indexOf(\"{\") + 1, fn.lastIndexOf(\"}\"))].join(\"\\n\");\n          this.workerSourceURL = URL.createObjectURL(new Blob([body]));\n          this.transcoderBinary = binaryContent2;\n          this.workerPool.setWorkerCreator(() => {\n            const worker = new Worker(this.workerSourceURL);\n            const transcoderBinary = this.transcoderBinary.slice(0);\n            worker.postMessage({\n              type: \"init\",\n              config: this.workerConfig,\n              transcoderBinary\n            }, [transcoderBinary]);\n            return worker;\n          });\n        });\n        if (_activeLoaders > 0) {\n          console.warn(\"THREE.KTX2Loader: Multiple active KTX2 loaders may cause performance issues. Use a single KTX2Loader instance, or call .dispose() on old instances.\");\n        }\n        _activeLoaders++;\n      }\n      return this.transcoderPending;\n    }\n    load(url, onLoad, onProgress, onError) {\n      if (this.workerConfig === null) {\n        throw new Error(\"THREE.KTX2Loader: Missing initialization with `.detectSupport( renderer )`.\");\n      }\n      const loader = new FileLoader(this.manager);\n      loader.setResponseType(\"arraybuffer\");\n      loader.setWithCredentials(this.withCredentials);\n      loader.load(url, buffer => {\n        if (_taskCache.has(buffer)) {\n          const cachedTask = _taskCache.get(buffer);\n          return cachedTask.promise.then(onLoad).catch(onError);\n        }\n        this._createTexture(buffer).then(texture => onLoad ? onLoad(texture) : null).catch(onError);\n      }, onProgress, onError);\n    }\n    _createTextureFrom(transcodeResult, container) {\n      const {\n        faces,\n        width,\n        height,\n        format,\n        type,\n        error,\n        dfdFlags\n      } = transcodeResult;\n      if (type === \"error\") return Promise.reject(error);\n      let texture;\n      if (container.faceCount === 6) {\n        texture = new CompressedCubeTexture(faces, format, UnsignedByteType);\n      } else {\n        const mipmaps = faces[0].mipmaps;\n        texture = container.layerCount > 1 ? new CompressedArrayTexture(mipmaps, width, height, container.layerCount, format, UnsignedByteType) : new CompressedTexture(mipmaps, width, height, format, UnsignedByteType);\n      }\n      texture.minFilter = faces[0].mipmaps.length === 1 ? LinearFilter : LinearMipmapLinearFilter;\n      texture.magFilter = LinearFilter;\n      texture.generateMipmaps = false;\n      texture.needsUpdate = true;\n      const colorSpace = parseColorSpace(container);\n      if (\"colorSpace\" in texture) texture.colorSpace = colorSpace;else texture.encoding = colorSpace === SRGBColorSpace ? sRGBEncoding : LinearEncoding;\n      texture.premultiplyAlpha = !!(dfdFlags & KHR_DF_FLAG_ALPHA_PREMULTIPLIED);\n      return texture;\n    }\n    /**\n     * @param {ArrayBuffer} buffer\n     * @param {object?} config\n     * @return {Promise<CompressedTexture|CompressedArrayTexture|DataTexture|Data3DTexture>}\n     */\n    async _createTexture(buffer, config = {}) {\n      const container = read(new Uint8Array(buffer));\n      if (container.vkFormat !== VK_FORMAT_UNDEFINED) {\n        return createRawTexture(container);\n      }\n      const taskConfig = config;\n      const texturePending = this.init().then(() => {\n        return this.workerPool.postMessage({\n          type: \"transcode\",\n          buffer,\n          taskConfig\n        }, [buffer]);\n      }).then(e => this._createTextureFrom(e.data, container));\n      _taskCache.set(buffer, {\n        promise: texturePending\n      });\n      return texturePending;\n    }\n    dispose() {\n      this.workerPool.dispose();\n      if (this.workerSourceURL) URL.revokeObjectURL(this.workerSourceURL);\n      _activeLoaders--;\n      return this;\n    }\n  };\n  let KTX2Loader2 = _KTX2Loader;\n  /* CONSTANTS */\n  __publicField(KTX2Loader2, \"BasisFormat\", {\n    ETC1S: 0,\n    UASTC_4x4: 1\n  });\n  __publicField(KTX2Loader2, \"TranscoderFormat\", {\n    ETC1: 0,\n    ETC2: 1,\n    BC1: 2,\n    BC3: 3,\n    BC4: 4,\n    BC5: 5,\n    BC7_M6_OPAQUE_ONLY: 6,\n    BC7_M5: 7,\n    PVRTC1_4_RGB: 8,\n    PVRTC1_4_RGBA: 9,\n    ASTC_4x4: 10,\n    ATC_RGB: 11,\n    ATC_RGBA_INTERPOLATED_ALPHA: 12,\n    RGBA32: 13,\n    RGB565: 14,\n    BGR565: 15,\n    RGBA4444: 16\n  });\n  __publicField(KTX2Loader2, \"EngineFormat\", {\n    RGBAFormat,\n    RGBA_ASTC_4x4_Format,\n    RGBA_BPTC_Format,\n    RGBA_ETC2_EAC_Format,\n    RGBA_PVRTC_4BPPV1_Format,\n    RGBA_S3TC_DXT5_Format,\n    RGB_ETC1_Format,\n    RGB_ETC2_Format,\n    RGB_PVRTC_4BPPV1_Format,\n    RGB_S3TC_DXT1_Format\n  });\n  /* WEB WORKER */\n  __publicField(KTX2Loader2, \"BasisWorker\", function () {\n    let config;\n    let transcoderPending;\n    let BasisModule;\n    const EngineFormat = _EngineFormat;\n    const TranscoderFormat = _TranscoderFormat;\n    const BasisFormat = _BasisFormat;\n    self.addEventListener(\"message\", function (e) {\n      const message = e.data;\n      switch (message.type) {\n        case \"init\":\n          config = message.config;\n          init(message.transcoderBinary);\n          break;\n        case \"transcode\":\n          transcoderPending.then(() => {\n            try {\n              const {\n                faces,\n                buffers,\n                width,\n                height,\n                hasAlpha,\n                format,\n                dfdFlags\n              } = transcode(message.buffer);\n              self.postMessage({\n                type: \"transcode\",\n                id: message.id,\n                faces,\n                width,\n                height,\n                hasAlpha,\n                format,\n                dfdFlags\n              }, buffers);\n            } catch (error) {\n              console.error(error);\n              self.postMessage({\n                type: \"error\",\n                id: message.id,\n                error: error.message\n              });\n            }\n          });\n          break;\n      }\n    });\n    function init(wasmBinary) {\n      transcoderPending = new Promise(resolve => {\n        BasisModule = {\n          wasmBinary,\n          onRuntimeInitialized: resolve\n        };\n        BASIS(BasisModule);\n      }).then(() => {\n        BasisModule.initializeBasis();\n        if (BasisModule.KTX2File === void 0) {\n          console.warn(\"THREE.KTX2Loader: Please update Basis Universal transcoder.\");\n        }\n      });\n    }\n    function transcode(buffer) {\n      const ktx2File = new BasisModule.KTX2File(new Uint8Array(buffer));\n      function cleanup() {\n        ktx2File.close();\n        ktx2File.delete();\n      }\n      if (!ktx2File.isValid()) {\n        cleanup();\n        throw new Error(\"THREE.KTX2Loader:\tInvalid or unsupported .ktx2 file\");\n      }\n      const basisFormat = ktx2File.isUASTC() ? BasisFormat.UASTC_4x4 : BasisFormat.ETC1S;\n      const width = ktx2File.getWidth();\n      const height = ktx2File.getHeight();\n      const layerCount = ktx2File.getLayers() || 1;\n      const levelCount = ktx2File.getLevels();\n      const faceCount = ktx2File.getFaces();\n      const hasAlpha = ktx2File.getHasAlpha();\n      const dfdFlags = ktx2File.getDFDFlags();\n      const {\n        transcoderFormat,\n        engineFormat\n      } = getTranscoderFormat(basisFormat, width, height, hasAlpha);\n      if (!width || !height || !levelCount) {\n        cleanup();\n        throw new Error(\"THREE.KTX2Loader:\tInvalid texture\");\n      }\n      if (!ktx2File.startTranscoding()) {\n        cleanup();\n        throw new Error(\"THREE.KTX2Loader: .startTranscoding failed\");\n      }\n      const faces = [];\n      const buffers = [];\n      for (let face = 0; face < faceCount; face++) {\n        const mipmaps = [];\n        for (let mip = 0; mip < levelCount; mip++) {\n          const layerMips = [];\n          let mipWidth, mipHeight;\n          for (let layer = 0; layer < layerCount; layer++) {\n            const levelInfo = ktx2File.getImageLevelInfo(mip, layer, face);\n            if (face === 0 && mip === 0 && layer === 0 && (levelInfo.origWidth % 4 !== 0 || levelInfo.origHeight % 4 !== 0)) {\n              console.warn(\"THREE.KTX2Loader: ETC1S and UASTC textures should use multiple-of-four dimensions.\");\n            }\n            if (levelCount > 1) {\n              mipWidth = levelInfo.origWidth;\n              mipHeight = levelInfo.origHeight;\n            } else {\n              mipWidth = levelInfo.width;\n              mipHeight = levelInfo.height;\n            }\n            const dst = new Uint8Array(ktx2File.getImageTranscodedSizeInBytes(mip, layer, 0, transcoderFormat));\n            const status = ktx2File.transcodeImage(dst, mip, layer, face, transcoderFormat, 0, -1, -1);\n            if (!status) {\n              cleanup();\n              throw new Error(\"THREE.KTX2Loader: .transcodeImage failed.\");\n            }\n            layerMips.push(dst);\n          }\n          const mipData = concat(layerMips);\n          mipmaps.push({\n            data: mipData,\n            width: mipWidth,\n            height: mipHeight\n          });\n          buffers.push(mipData.buffer);\n        }\n        faces.push({\n          mipmaps,\n          width,\n          height,\n          format: engineFormat\n        });\n      }\n      cleanup();\n      return {\n        faces,\n        buffers,\n        width,\n        height,\n        hasAlpha,\n        format: engineFormat,\n        dfdFlags\n      };\n    }\n    const FORMAT_OPTIONS = [{\n      if: \"astcSupported\",\n      basisFormat: [BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.ASTC_4x4, TranscoderFormat.ASTC_4x4],\n      engineFormat: [EngineFormat.RGBA_ASTC_4x4_Format, EngineFormat.RGBA_ASTC_4x4_Format],\n      priorityETC1S: Infinity,\n      priorityUASTC: 1,\n      needsPowerOfTwo: false\n    }, {\n      if: \"bptcSupported\",\n      basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.BC7_M5, TranscoderFormat.BC7_M5],\n      engineFormat: [EngineFormat.RGBA_BPTC_Format, EngineFormat.RGBA_BPTC_Format],\n      priorityETC1S: 3,\n      priorityUASTC: 2,\n      needsPowerOfTwo: false\n    }, {\n      if: \"dxtSupported\",\n      basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.BC1, TranscoderFormat.BC3],\n      engineFormat: [EngineFormat.RGB_S3TC_DXT1_Format, EngineFormat.RGBA_S3TC_DXT5_Format],\n      priorityETC1S: 4,\n      priorityUASTC: 5,\n      needsPowerOfTwo: false\n    }, {\n      if: \"etc2Supported\",\n      basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.ETC1, TranscoderFormat.ETC2],\n      engineFormat: [EngineFormat.RGB_ETC2_Format, EngineFormat.RGBA_ETC2_EAC_Format],\n      priorityETC1S: 1,\n      priorityUASTC: 3,\n      needsPowerOfTwo: false\n    }, {\n      if: \"etc1Supported\",\n      basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.ETC1],\n      engineFormat: [EngineFormat.RGB_ETC1_Format],\n      priorityETC1S: 2,\n      priorityUASTC: 4,\n      needsPowerOfTwo: false\n    }, {\n      if: \"pvrtcSupported\",\n      basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n      transcoderFormat: [TranscoderFormat.PVRTC1_4_RGB, TranscoderFormat.PVRTC1_4_RGBA],\n      engineFormat: [EngineFormat.RGB_PVRTC_4BPPV1_Format, EngineFormat.RGBA_PVRTC_4BPPV1_Format],\n      priorityETC1S: 5,\n      priorityUASTC: 6,\n      needsPowerOfTwo: true\n    }];\n    const ETC1S_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n      return a.priorityETC1S - b.priorityETC1S;\n    });\n    const UASTC_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n      return a.priorityUASTC - b.priorityUASTC;\n    });\n    function getTranscoderFormat(basisFormat, width, height, hasAlpha) {\n      let transcoderFormat;\n      let engineFormat;\n      const options = basisFormat === BasisFormat.ETC1S ? ETC1S_OPTIONS : UASTC_OPTIONS;\n      for (let i = 0; i < options.length; i++) {\n        const opt = options[i];\n        if (!config[opt.if]) continue;\n        if (!opt.basisFormat.includes(basisFormat)) continue;\n        if (hasAlpha && opt.transcoderFormat.length < 2) continue;\n        if (opt.needsPowerOfTwo && !(isPowerOfTwo(width) && isPowerOfTwo(height))) continue;\n        transcoderFormat = opt.transcoderFormat[hasAlpha ? 1 : 0];\n        engineFormat = opt.engineFormat[hasAlpha ? 1 : 0];\n        return {\n          transcoderFormat,\n          engineFormat\n        };\n      }\n      console.warn(\"THREE.KTX2Loader: No suitable compressed texture format found. Decoding to RGBA32.\");\n      transcoderFormat = TranscoderFormat.RGBA32;\n      engineFormat = EngineFormat.RGBAFormat;\n      return {\n        transcoderFormat,\n        engineFormat\n      };\n    }\n    function isPowerOfTwo(value) {\n      if (value <= 2) return true;\n      return (value & value - 1) === 0 && value !== 0;\n    }\n    function concat(arrays) {\n      if (arrays.length === 1) return arrays[0];\n      let totalByteLength = 0;\n      for (let i = 0; i < arrays.length; i++) {\n        const array = arrays[i];\n        totalByteLength += array.byteLength;\n      }\n      const result = new Uint8Array(totalByteLength);\n      let byteOffset = 0;\n      for (let i = 0; i < arrays.length; i++) {\n        const array = arrays[i];\n        result.set(array, byteOffset);\n        byteOffset += array.byteLength;\n      }\n      return result;\n    }\n  });\n  return KTX2Loader2;\n})();\nconst UNCOMPRESSED_FORMATS = /* @__PURE__ */new Set([RGBAFormat, RGFormat, RedFormat]);\nconst FORMAT_MAP = {\n  [VK_FORMAT_R32G32B32A32_SFLOAT]: RGBAFormat,\n  [VK_FORMAT_R16G16B16A16_SFLOAT]: RGBAFormat,\n  [VK_FORMAT_R8G8B8A8_UNORM]: RGBAFormat,\n  [VK_FORMAT_R8G8B8A8_SRGB]: RGBAFormat,\n  [VK_FORMAT_R32G32_SFLOAT]: RGFormat,\n  [VK_FORMAT_R16G16_SFLOAT]: RGFormat,\n  [VK_FORMAT_R8G8_UNORM]: RGFormat,\n  [VK_FORMAT_R8G8_SRGB]: RGFormat,\n  [VK_FORMAT_R32_SFLOAT]: RedFormat,\n  [VK_FORMAT_R16_SFLOAT]: RedFormat,\n  [VK_FORMAT_R8_SRGB]: RedFormat,\n  [VK_FORMAT_R8_UNORM]: RedFormat,\n  [VK_FORMAT_ASTC_6x6_SRGB_BLOCK]: RGBA_ASTC_6x6_Format,\n  [VK_FORMAT_ASTC_6x6_UNORM_BLOCK]: RGBA_ASTC_6x6_Format\n};\nconst TYPE_MAP = {\n  [VK_FORMAT_R32G32B32A32_SFLOAT]: FloatType,\n  [VK_FORMAT_R16G16B16A16_SFLOAT]: HalfFloatType,\n  [VK_FORMAT_R8G8B8A8_UNORM]: UnsignedByteType,\n  [VK_FORMAT_R8G8B8A8_SRGB]: UnsignedByteType,\n  [VK_FORMAT_R32G32_SFLOAT]: FloatType,\n  [VK_FORMAT_R16G16_SFLOAT]: HalfFloatType,\n  [VK_FORMAT_R8G8_UNORM]: UnsignedByteType,\n  [VK_FORMAT_R8G8_SRGB]: UnsignedByteType,\n  [VK_FORMAT_R32_SFLOAT]: FloatType,\n  [VK_FORMAT_R16_SFLOAT]: HalfFloatType,\n  [VK_FORMAT_R8_SRGB]: UnsignedByteType,\n  [VK_FORMAT_R8_UNORM]: UnsignedByteType,\n  [VK_FORMAT_ASTC_6x6_SRGB_BLOCK]: UnsignedByteType,\n  [VK_FORMAT_ASTC_6x6_UNORM_BLOCK]: UnsignedByteType\n};\nasync function createRawTexture(container) {\n  const {\n    vkFormat\n  } = container;\n  if (FORMAT_MAP[vkFormat] === void 0) {\n    throw new Error(\"THREE.KTX2Loader: Unsupported vkFormat.\");\n  }\n  let zstd;\n  if (container.supercompressionScheme === KHR_SUPERCOMPRESSION_ZSTD) {\n    if (!_zstd) {\n      _zstd = new Promise(async resolve => {\n        const zstd2 = new ZSTDDecoder();\n        await zstd2.init();\n        resolve(zstd2);\n      });\n    }\n    zstd = await _zstd;\n  }\n  const mipmaps = [];\n  for (let levelIndex = 0; levelIndex < container.levels.length; levelIndex++) {\n    const levelWidth = Math.max(1, container.pixelWidth >> levelIndex);\n    const levelHeight = Math.max(1, container.pixelHeight >> levelIndex);\n    const levelDepth = container.pixelDepth ? Math.max(1, container.pixelDepth >> levelIndex) : 0;\n    const level = container.levels[levelIndex];\n    let levelData;\n    if (container.supercompressionScheme === KHR_SUPERCOMPRESSION_NONE) {\n      levelData = level.levelData;\n    } else if (container.supercompressionScheme === KHR_SUPERCOMPRESSION_ZSTD) {\n      levelData = zstd.decode(level.levelData, level.uncompressedByteLength);\n    } else {\n      throw new Error(\"THREE.KTX2Loader: Unsupported supercompressionScheme.\");\n    }\n    let data;\n    if (TYPE_MAP[vkFormat] === FloatType) {\n      data = new Float32Array(levelData.buffer, levelData.byteOffset, levelData.byteLength / Float32Array.BYTES_PER_ELEMENT);\n    } else if (TYPE_MAP[vkFormat] === HalfFloatType) {\n      data = new Uint16Array(levelData.buffer, levelData.byteOffset, levelData.byteLength / Uint16Array.BYTES_PER_ELEMENT);\n    } else {\n      data = levelData;\n    }\n    mipmaps.push({\n      data,\n      width: levelWidth,\n      height: levelHeight,\n      depth: levelDepth\n    });\n  }\n  let texture;\n  if (UNCOMPRESSED_FORMATS.has(FORMAT_MAP[vkFormat])) {\n    texture = container.pixelDepth === 0 ? new DataTexture(mipmaps[0].data, container.pixelWidth, container.pixelHeight) : new Data3DTexture(mipmaps[0].data, container.pixelWidth, container.pixelHeight, container.pixelDepth);\n  } else {\n    if (container.pixelDepth > 0) throw new Error(\"THREE.KTX2Loader: Unsupported pixelDepth.\");\n    texture = new CompressedTexture(mipmaps, container.pixelWidth, container.pixelHeight);\n  }\n  texture.mipmaps = mipmaps;\n  texture.type = TYPE_MAP[vkFormat];\n  texture.format = FORMAT_MAP[vkFormat];\n  texture.needsUpdate = true;\n  const colorSpace = parseColorSpace(container);\n  if (\"colorSpace\" in texture) texture.colorSpace = colorSpace;else texture.encoding = colorSpace === SRGBColorSpace ? sRGBEncoding : LinearEncoding;\n  return Promise.resolve(texture);\n}\nfunction parseColorSpace(container) {\n  const dfd = container.dataFormatDescriptor[0];\n  if (dfd.colorPrimaries === KHR_DF_PRIMARIES_BT709) {\n    return dfd.transferFunction === KHR_DF_TRANSFER_SRGB ? SRGBColorSpace : LinearSRGBColorSpace;\n  } else if (dfd.colorPrimaries === KHR_DF_PRIMARIES_DISPLAYP3) {\n    return dfd.transferFunction === KHR_DF_TRANSFER_SRGB ? DisplayP3ColorSpace : LinearDisplayP3ColorSpace;\n  } else if (dfd.colorPrimaries === KHR_DF_PRIMARIES_UNSPECIFIED) {\n    return NoColorSpace;\n  } else {\n    console.warn(`THREE.KTX2Loader: Unsupported color primaries, \"${dfd.colorPrimaries}\"`);\n    return NoColorSpace;\n  }\n}\nexport { KTX2Loader };", "map": {"version": 3, "names": ["LinearEncoding", "sRGBEncoding", "NoColorSpace", "DisplayP3ColorSpace", "LinearDisplayP3ColorSpace", "LinearSRGBColorSpace", "SRGBColorSpace", "_taskCache", "WeakMap", "_activeLoaders", "_zstd", "KTX2Loader", "_KTX2Loader", "Loader", "constructor", "manager", "transcoderPath", "transcoderBinary", "transcoderPending", "workerPool", "WorkerPool", "workerSourceURL", "workerConfig", "MSC_TRANSCODER", "console", "warn", "setTranscoderPath", "path", "setWorkerLimit", "num", "detectSupport", "renderer", "astcSupported", "extensions", "has", "etc1Supported", "etc2Supported", "dxtSupported", "bptcSupported", "pvrtcSupported", "capabilities", "isWebGL2", "init", "j<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "setWithCredentials", "withCredentials", "js<PERSON><PERSON><PERSON>", "loadAsync", "binaryLoader", "setResponseType", "binaryContent", "Promise", "all", "then", "jsContent2", "binaryContent2", "fn", "BasisWorker", "toString", "body", "JSON", "stringify", "EngineFormat", "TranscoderFormat", "BasisFormat", "substring", "indexOf", "lastIndexOf", "join", "URL", "createObjectURL", "Blob", "setWorkerCreator", "worker", "Worker", "slice", "postMessage", "type", "config", "load", "url", "onLoad", "onProgress", "onError", "Error", "loader", "buffer", "cachedTask", "get", "promise", "catch", "_createTexture", "texture", "_createTextureFrom", "transcodeResult", "container", "faces", "width", "height", "format", "error", "dfdFlags", "reject", "faceCount", "CompressedCubeTexture", "UnsignedByteType", "mipmaps", "layerCount", "CompressedArrayTexture", "CompressedTexture", "minFilter", "length", "LinearFilter", "LinearMipmapLinearFilter", "magFilter", "generateMipmaps", "needsUpdate", "colorSpace", "parseColorSpace", "encoding", "premultiplyAlpha", "KHR_DF_FLAG_ALPHA_PREMULTIPLIED", "read", "Uint8Array", "vkFormat", "VK_FORMAT_UNDEFINED", "createRawTexture", "taskConfig", "texturePending", "e", "data", "set", "dispose", "revokeObjectURL", "KTX2Loader2", "__publicField", "ETC1S", "UASTC_4x4", "ETC1", "ETC2", "BC1", "BC3", "BC4", "BC5", "BC7_M6_OPAQUE_ONLY", "BC7_M5", "PVRTC1_4_RGB", "PVRTC1_4_RGBA", "ASTC_4x4", "ATC_RGB", "ATC_RGBA_INTERPOLATED_ALPHA", "RGBA32", "RGB565", "BGR565", "RGBA4444", "RGBAFormat", "RGBA_ASTC_4x4_Format", "RGBA_BPTC_Format", "RGBA_ETC2_EAC_Format", "RGBA_PVRTC_4BPPV1_Format", "RGBA_S3TC_DXT5_Format", "RGB_ETC1_Format", "RGB_ETC2_Format", "RGB_PVRTC_4BPPV1_Format", "RGB_S3TC_DXT1_Format", "BasisModule", "_EngineFormat", "_TranscoderFormat", "_BasisFormat", "self", "addEventListener", "message", "buffers", "has<PERSON><PERSON><PERSON>", "transcode", "id", "wasmBinary", "resolve", "onRuntimeInitialized", "BASIS", "initializeBasis", "KTX2File", "ktx2File", "cleanup", "close", "delete", "<PERSON><PERSON><PERSON><PERSON>", "basisFormat", "isUASTC", "getWidth", "getHeight", "getLayers", "levelCount", "getLevels", "getFaces", "getHasAlpha", "getDFDFlags", "transcoderFormat", "engineFormat", "getTranscoderFormat", "startTranscoding", "face", "mip", "layerMips", "mip<PERSON><PERSON><PERSON>", "mipHeight", "layer", "levelInfo", "getImageLevelInfo", "origWidth", "origHeight", "dst", "getImageTranscodedSizeInBytes", "status", "transcodeImage", "push", "mipData", "concat", "FORMAT_OPTIONS", "if", "priorityETC1S", "Infinity", "priorityUASTC", "needsPowerOfTwo", "ETC1S_OPTIONS", "sort", "a", "b", "UASTC_OPTIONS", "options", "i", "opt", "includes", "isPowerOfTwo", "value", "arrays", "totalByteLength", "array", "byteLength", "result", "byteOffset", "UNCOMPRESSED_FORMATS", "Set", "RGFormat", "RedFormat", "FORMAT_MAP", "VK_FORMAT_R32G32B32A32_SFLOAT", "VK_FORMAT_R16G16B16A16_SFLOAT", "VK_FORMAT_R8G8B8A8_UNORM", "VK_FORMAT_R8G8B8A8_SRGB", "VK_FORMAT_R32G32_SFLOAT", "VK_FORMAT_R16G16_SFLOAT", "VK_FORMAT_R8G8_UNORM", "VK_FORMAT_R8G8_SRGB", "VK_FORMAT_R32_SFLOAT", "VK_FORMAT_R16_SFLOAT", "VK_FORMAT_R8_SRGB", "VK_FORMAT_R8_UNORM", "VK_FORMAT_ASTC_6x6_SRGB_BLOCK", "RGBA_ASTC_6x6_Format", "VK_FORMAT_ASTC_6x6_UNORM_BLOCK", "TYPE_MAP", "FloatType", "HalfFloatType", "zstd", "supercompressionScheme", "KHR_SUPERCOMPRESSION_ZSTD", "zstd2", "ZSTDDecoder", "levelIndex", "levels", "levelWidth", "Math", "max", "pixelWidth", "levelHeight", "pixelHeight", "<PERSON><PERSON><PERSON><PERSON>", "pixelDepth", "level", "levelData", "KHR_SUPERCOMPRESSION_NONE", "decode", "uncompressedByteLength", "Float32Array", "BYTES_PER_ELEMENT", "Uint16Array", "depth", "DataTexture", "Data3DTexture", "dfd", "dataFormatDescriptor", "colorPrimaries", "KHR_DF_PRIMARIES_BT709", "transferFunction", "KHR_DF_TRANSFER_SRGB", "KHR_DF_PRIMARIES_DISPLAYP3", "KHR_DF_PRIMARIES_UNSPECIFIED"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\loaders\\KTX2Loader.js"], "sourcesContent": ["/**\n * Loader for KTX 2.0 GPU Texture containers.\n *\n * KTX 2.0 is a container format for various GPU texture formats. The loader\n * supports Basis Universal GPU textures, which can be quickly transcoded to\n * a wide variety of GPU texture compression formats, as well as some\n * uncompressed DataTexture and Data3DTexture formats.\n *\n * References:\n * - KTX: http://github.khronos.org/KTX-Specification/\n * - DFD: https://www.khronos.org/registry/DataFormat/specs/1.3/dataformat.1.3.html#basicdescriptor\n */\n\nimport {\n  CompressedTexture,\n  DataTexture,\n  FileLoader,\n  FloatType,\n  HalfFloatType,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  Loader,\n  RedFormat,\n  RGB_ETC1_Format,\n  RGB_ETC2_Format,\n  RGB_PVRTC_4BPPV1_Format,\n  RGB_S3TC_DXT1_Format,\n  RGBA_ASTC_4x4_Format,\n  RGBA_ASTC_6x6_Format,\n  RGBA_BPTC_Format,\n  RGBA_ETC2_EAC_Format,\n  RGBA_PVRTC_4BPPV1_Format,\n  RGBA_S3TC_DXT5_Format,\n  RGBAFormat,\n  RGFormat,\n  UnsignedByteType,\n} from 'three'\nimport { WorkerPool } from '../utils/WorkerPool'\nimport {\n  read,\n  KHR_DF_FLAG_ALPHA_PREMULTIPLIED,\n  KHR_DF_TRANSFER_SRGB,\n  KHR_SUPERCOMPRESSION_NONE,\n  KHR_SUPERCOMPRESSION_ZSTD,\n  VK_FORMAT_UNDEFINED,\n  VK_FORMAT_R16_SFLOAT,\n  VK_FORMAT_R16G16_SFLOAT,\n  VK_FORMAT_R16G16B16A16_SFLOAT,\n  VK_FORMAT_R32_SFLOAT,\n  VK_FORMAT_R32G32_SFLOAT,\n  VK_FORMAT_R32G32B32A32_SFLOAT,\n  VK_FORMAT_R8_SRGB,\n  VK_FORMAT_R8_UNORM,\n  VK_FORMAT_R8G8_SRGB,\n  VK_FORMAT_R8G8_UNORM,\n  VK_FORMAT_R8G8B8A8_SRGB,\n  VK_FORMAT_R8G8B8A8_UNORM,\n  VK_FORMAT_ASTC_6x6_SRGB_BLOCK,\n  VK_FORMAT_ASTC_6x6_UNORM_BLOCK,\n  KHR_DF_PRIMARIES_UNSPECIFIED,\n  KHR_DF_PRIMARIES_BT709,\n  KHR_DF_PRIMARIES_DISPLAYP3,\n} from '../libs/ktx-parse'\nimport { ZSTDDecoder } from '../libs/zstddec'\nimport { CompressedCubeTexture } from '../_polyfill/CompressedCubeTexture'\nimport { CompressedArrayTexture } from '../_polyfill/CompressedArrayTexture'\nimport { Data3DTexture } from '../_polyfill/Data3DTexture'\n\nconst LinearEncoding = 3000\nconst sRGBEncoding = 3001\n\nconst NoColorSpace = ''\nconst DisplayP3ColorSpace = 'display-p3'\nconst LinearDisplayP3ColorSpace = 'display-p3-linear'\nconst LinearSRGBColorSpace = 'srgb-linear'\nconst SRGBColorSpace = 'srgb'\n\nconst _taskCache = new WeakMap()\n\nlet _activeLoaders = 0\n\nlet _zstd\n\nconst KTX2Loader = /* @__PURE__ */ (() => {\n  class KTX2Loader extends Loader {\n    /* CONSTANTS */\n\n    static BasisFormat = {\n      ETC1S: 0,\n      UASTC_4x4: 1,\n    }\n\n    static TranscoderFormat = {\n      ETC1: 0,\n      ETC2: 1,\n      BC1: 2,\n      BC3: 3,\n      BC4: 4,\n      BC5: 5,\n      BC7_M6_OPAQUE_ONLY: 6,\n      BC7_M5: 7,\n      PVRTC1_4_RGB: 8,\n      PVRTC1_4_RGBA: 9,\n      ASTC_4x4: 10,\n      ATC_RGB: 11,\n      ATC_RGBA_INTERPOLATED_ALPHA: 12,\n      RGBA32: 13,\n      RGB565: 14,\n      BGR565: 15,\n      RGBA4444: 16,\n    }\n\n    static EngineFormat = {\n      RGBAFormat: RGBAFormat,\n      RGBA_ASTC_4x4_Format: RGBA_ASTC_4x4_Format,\n      RGBA_BPTC_Format: RGBA_BPTC_Format,\n      RGBA_ETC2_EAC_Format: RGBA_ETC2_EAC_Format,\n      RGBA_PVRTC_4BPPV1_Format: RGBA_PVRTC_4BPPV1_Format,\n      RGBA_S3TC_DXT5_Format: RGBA_S3TC_DXT5_Format,\n      RGB_ETC1_Format: RGB_ETC1_Format,\n      RGB_ETC2_Format: RGB_ETC2_Format,\n      RGB_PVRTC_4BPPV1_Format: RGB_PVRTC_4BPPV1_Format,\n      RGB_S3TC_DXT1_Format: RGB_S3TC_DXT1_Format,\n    }\n\n    /* WEB WORKER */\n\n    static BasisWorker = function () {\n      let config\n      let transcoderPending\n      let BasisModule\n\n      /** @type KTX2Loader.EngineFormat */\n      const EngineFormat = _EngineFormat\n      /** @type KTX2Loader.TranscoderFormat */\n      const TranscoderFormat = _TranscoderFormat\n      /** @type KTX2Loader.BasisFormat */\n      const BasisFormat = _BasisFormat\n\n      self.addEventListener('message', function (e) {\n        const message = e.data\n\n        switch (message.type) {\n          case 'init':\n            config = message.config\n            init(message.transcoderBinary)\n            break\n\n          case 'transcode':\n            transcoderPending.then(() => {\n              try {\n                const { faces, buffers, width, height, hasAlpha, format, dfdFlags } = transcode(message.buffer)\n\n                self.postMessage(\n                  { type: 'transcode', id: message.id, faces, width, height, hasAlpha, format, dfdFlags },\n                  buffers,\n                )\n              } catch (error) {\n                console.error(error)\n\n                self.postMessage({ type: 'error', id: message.id, error: error.message })\n              }\n            })\n            break\n        }\n      })\n\n      function init(wasmBinary) {\n        transcoderPending = new Promise((resolve) => {\n          BasisModule = { wasmBinary, onRuntimeInitialized: resolve }\n          BASIS(BasisModule)\n        }).then(() => {\n          BasisModule.initializeBasis()\n\n          if (BasisModule.KTX2File === undefined) {\n            console.warn('THREE.KTX2Loader: Please update Basis Universal transcoder.')\n          }\n        })\n      }\n\n      function transcode(buffer) {\n        const ktx2File = new BasisModule.KTX2File(new Uint8Array(buffer))\n\n        function cleanup() {\n          ktx2File.close()\n          ktx2File.delete()\n        }\n\n        if (!ktx2File.isValid()) {\n          cleanup()\n          throw new Error('THREE.KTX2Loader:\tInvalid or unsupported .ktx2 file')\n        }\n\n        const basisFormat = ktx2File.isUASTC() ? BasisFormat.UASTC_4x4 : BasisFormat.ETC1S\n        const width = ktx2File.getWidth()\n        const height = ktx2File.getHeight()\n        const layerCount = ktx2File.getLayers() || 1\n        const levelCount = ktx2File.getLevels()\n        const faceCount = ktx2File.getFaces()\n        const hasAlpha = ktx2File.getHasAlpha()\n        const dfdFlags = ktx2File.getDFDFlags()\n\n        const { transcoderFormat, engineFormat } = getTranscoderFormat(basisFormat, width, height, hasAlpha)\n\n        if (!width || !height || !levelCount) {\n          cleanup()\n          throw new Error('THREE.KTX2Loader:\tInvalid texture')\n        }\n\n        if (!ktx2File.startTranscoding()) {\n          cleanup()\n          throw new Error('THREE.KTX2Loader: .startTranscoding failed')\n        }\n\n        const faces = []\n        const buffers = []\n\n        for (let face = 0; face < faceCount; face++) {\n          const mipmaps = []\n\n          for (let mip = 0; mip < levelCount; mip++) {\n            const layerMips = []\n\n            let mipWidth, mipHeight\n\n            for (let layer = 0; layer < layerCount; layer++) {\n              const levelInfo = ktx2File.getImageLevelInfo(mip, layer, face)\n\n              if (\n                face === 0 &&\n                mip === 0 &&\n                layer === 0 &&\n                (levelInfo.origWidth % 4 !== 0 || levelInfo.origHeight % 4 !== 0)\n              ) {\n                console.warn('THREE.KTX2Loader: ETC1S and UASTC textures should use multiple-of-four dimensions.')\n              }\n\n              if (levelCount > 1) {\n                mipWidth = levelInfo.origWidth\n                mipHeight = levelInfo.origHeight\n              } else {\n                // Handles non-multiple-of-four dimensions in textures without mipmaps. Textures with\n                // mipmaps must use multiple-of-four dimensions, for some texture formats and APIs.\n                // See mrdoob/three.js#25908.\n                mipWidth = levelInfo.width\n                mipHeight = levelInfo.height\n              }\n\n              const dst = new Uint8Array(ktx2File.getImageTranscodedSizeInBytes(mip, layer, 0, transcoderFormat))\n              const status = ktx2File.transcodeImage(dst, mip, layer, face, transcoderFormat, 0, -1, -1)\n\n              if (!status) {\n                cleanup()\n                throw new Error('THREE.KTX2Loader: .transcodeImage failed.')\n              }\n\n              layerMips.push(dst)\n            }\n\n            const mipData = concat(layerMips)\n\n            mipmaps.push({ data: mipData, width: mipWidth, height: mipHeight })\n            buffers.push(mipData.buffer)\n          }\n\n          faces.push({ mipmaps, width, height, format: engineFormat })\n        }\n\n        cleanup()\n\n        return { faces, buffers, width, height, hasAlpha, format: engineFormat, dfdFlags }\n      }\n\n      //\n\n      // Optimal choice of a transcoder target format depends on the Basis format (ETC1S or UASTC),\n      // device capabilities, and texture dimensions. The list below ranks the formats separately\n      // for ETC1S and UASTC.\n      //\n      // In some cases, transcoding UASTC to RGBA32 might be preferred for higher quality (at\n      // significant memory cost) compared to ETC1/2, BC1/3, and PVRTC. The transcoder currently\n      // chooses RGBA32 only as a last resort and does not expose that option to the caller.\n      const FORMAT_OPTIONS = [\n        {\n          if: 'astcSupported',\n          basisFormat: [BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ASTC_4x4, TranscoderFormat.ASTC_4x4],\n          engineFormat: [EngineFormat.RGBA_ASTC_4x4_Format, EngineFormat.RGBA_ASTC_4x4_Format],\n          priorityETC1S: Infinity,\n          priorityUASTC: 1,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'bptcSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.BC7_M5, TranscoderFormat.BC7_M5],\n          engineFormat: [EngineFormat.RGBA_BPTC_Format, EngineFormat.RGBA_BPTC_Format],\n          priorityETC1S: 3,\n          priorityUASTC: 2,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'dxtSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.BC1, TranscoderFormat.BC3],\n          engineFormat: [EngineFormat.RGB_S3TC_DXT1_Format, EngineFormat.RGBA_S3TC_DXT5_Format],\n          priorityETC1S: 4,\n          priorityUASTC: 5,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'etc2Supported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ETC1, TranscoderFormat.ETC2],\n          engineFormat: [EngineFormat.RGB_ETC2_Format, EngineFormat.RGBA_ETC2_EAC_Format],\n          priorityETC1S: 1,\n          priorityUASTC: 3,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'etc1Supported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ETC1],\n          engineFormat: [EngineFormat.RGB_ETC1_Format],\n          priorityETC1S: 2,\n          priorityUASTC: 4,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'pvrtcSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.PVRTC1_4_RGB, TranscoderFormat.PVRTC1_4_RGBA],\n          engineFormat: [EngineFormat.RGB_PVRTC_4BPPV1_Format, EngineFormat.RGBA_PVRTC_4BPPV1_Format],\n          priorityETC1S: 5,\n          priorityUASTC: 6,\n          needsPowerOfTwo: true,\n        },\n      ]\n\n      const ETC1S_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n        return a.priorityETC1S - b.priorityETC1S\n      })\n      const UASTC_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n        return a.priorityUASTC - b.priorityUASTC\n      })\n\n      function getTranscoderFormat(basisFormat, width, height, hasAlpha) {\n        let transcoderFormat\n        let engineFormat\n\n        const options = basisFormat === BasisFormat.ETC1S ? ETC1S_OPTIONS : UASTC_OPTIONS\n\n        for (let i = 0; i < options.length; i++) {\n          const opt = options[i]\n\n          if (!config[opt.if]) continue\n          if (!opt.basisFormat.includes(basisFormat)) continue\n          if (hasAlpha && opt.transcoderFormat.length < 2) continue\n          if (opt.needsPowerOfTwo && !(isPowerOfTwo(width) && isPowerOfTwo(height))) continue\n\n          transcoderFormat = opt.transcoderFormat[hasAlpha ? 1 : 0]\n          engineFormat = opt.engineFormat[hasAlpha ? 1 : 0]\n\n          return { transcoderFormat, engineFormat }\n        }\n\n        console.warn('THREE.KTX2Loader: No suitable compressed texture format found. Decoding to RGBA32.')\n\n        transcoderFormat = TranscoderFormat.RGBA32\n        engineFormat = EngineFormat.RGBAFormat\n\n        return { transcoderFormat, engineFormat }\n      }\n\n      function isPowerOfTwo(value) {\n        if (value <= 2) return true\n\n        return (value & (value - 1)) === 0 && value !== 0\n      }\n\n      /** Concatenates N byte arrays. */\n      function concat(arrays) {\n        if (arrays.length === 1) return arrays[0]\n\n        let totalByteLength = 0\n\n        for (let i = 0; i < arrays.length; i++) {\n          const array = arrays[i]\n          totalByteLength += array.byteLength\n        }\n\n        const result = new Uint8Array(totalByteLength)\n\n        let byteOffset = 0\n\n        for (let i = 0; i < arrays.length; i++) {\n          const array = arrays[i]\n          result.set(array, byteOffset)\n\n          byteOffset += array.byteLength\n        }\n\n        return result\n      }\n    }\n\n    constructor(manager) {\n      super(manager)\n\n      this.transcoderPath = ''\n      this.transcoderBinary = null\n      this.transcoderPending = null\n\n      this.workerPool = new WorkerPool()\n      this.workerSourceURL = ''\n      this.workerConfig = null\n\n      if (typeof MSC_TRANSCODER !== 'undefined') {\n        console.warn(\n          'THREE.KTX2Loader: Please update to latest \"basis_transcoder\".' +\n            ' \"msc_basis_transcoder\" is no longer supported in three.js r125+.',\n        )\n      }\n    }\n\n    setTranscoderPath(path) {\n      this.transcoderPath = path\n\n      return this\n    }\n\n    setWorkerLimit(num) {\n      this.workerPool.setWorkerLimit(num)\n\n      return this\n    }\n\n    detectSupport(renderer) {\n      this.workerConfig = {\n        astcSupported: renderer.extensions.has('WEBGL_compressed_texture_astc'),\n        etc1Supported: renderer.extensions.has('WEBGL_compressed_texture_etc1'),\n        etc2Supported: renderer.extensions.has('WEBGL_compressed_texture_etc'),\n        dxtSupported: renderer.extensions.has('WEBGL_compressed_texture_s3tc'),\n        bptcSupported: renderer.extensions.has('EXT_texture_compression_bptc'),\n        pvrtcSupported:\n          renderer.extensions.has('WEBGL_compressed_texture_pvrtc') ||\n          renderer.extensions.has('WEBKIT_WEBGL_compressed_texture_pvrtc'),\n      }\n\n      if (renderer.capabilities.isWebGL2) {\n        // https://github.com/mrdoob/three.js/pull/22928\n        this.workerConfig.etc1Supported = false\n      }\n\n      return this\n    }\n\n    init() {\n      if (!this.transcoderPending) {\n        // Load transcoder wrapper.\n        const jsLoader = new FileLoader(this.manager)\n        jsLoader.setPath(this.transcoderPath)\n        jsLoader.setWithCredentials(this.withCredentials)\n        const jsContent = jsLoader.loadAsync('basis_transcoder.js')\n\n        // Load transcoder WASM binary.\n        const binaryLoader = new FileLoader(this.manager)\n        binaryLoader.setPath(this.transcoderPath)\n        binaryLoader.setResponseType('arraybuffer')\n        binaryLoader.setWithCredentials(this.withCredentials)\n        const binaryContent = binaryLoader.loadAsync('basis_transcoder.wasm')\n\n        this.transcoderPending = Promise.all([jsContent, binaryContent]).then(([jsContent, binaryContent]) => {\n          const fn = KTX2Loader.BasisWorker.toString()\n\n          const body = [\n            '/* constants */',\n            'let _EngineFormat = ' + JSON.stringify(KTX2Loader.EngineFormat),\n            'let _TranscoderFormat = ' + JSON.stringify(KTX2Loader.TranscoderFormat),\n            'let _BasisFormat = ' + JSON.stringify(KTX2Loader.BasisFormat),\n            '/* basis_transcoder.js */',\n            jsContent,\n            '/* worker */',\n            fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}')),\n          ].join('\\n')\n\n          this.workerSourceURL = URL.createObjectURL(new Blob([body]))\n          this.transcoderBinary = binaryContent\n\n          this.workerPool.setWorkerCreator(() => {\n            const worker = new Worker(this.workerSourceURL)\n            const transcoderBinary = this.transcoderBinary.slice(0)\n\n            worker.postMessage({ type: 'init', config: this.workerConfig, transcoderBinary }, [transcoderBinary])\n\n            return worker\n          })\n        })\n\n        if (_activeLoaders > 0) {\n          // Each instance loads a transcoder and allocates workers, increasing network and memory cost.\n\n          console.warn(\n            'THREE.KTX2Loader: Multiple active KTX2 loaders may cause performance issues.' +\n              ' Use a single KTX2Loader instance, or call .dispose() on old instances.',\n          )\n        }\n\n        _activeLoaders++\n      }\n\n      return this.transcoderPending\n    }\n\n    load(url, onLoad, onProgress, onError) {\n      if (this.workerConfig === null) {\n        throw new Error('THREE.KTX2Loader: Missing initialization with `.detectSupport( renderer )`.')\n      }\n\n      const loader = new FileLoader(this.manager)\n\n      loader.setResponseType('arraybuffer')\n      loader.setWithCredentials(this.withCredentials)\n\n      loader.load(\n        url,\n        (buffer) => {\n          // Check for an existing task using this buffer. A transferred buffer cannot be transferred\n          // again from this thread.\n          if (_taskCache.has(buffer)) {\n            const cachedTask = _taskCache.get(buffer)\n\n            return cachedTask.promise.then(onLoad).catch(onError)\n          }\n\n          this._createTexture(buffer)\n            .then((texture) => (onLoad ? onLoad(texture) : null))\n            .catch(onError)\n        },\n        onProgress,\n        onError,\n      )\n    }\n\n    _createTextureFrom(transcodeResult, container) {\n      const { faces, width, height, format, type, error, dfdFlags } = transcodeResult\n\n      if (type === 'error') return Promise.reject(error)\n\n      let texture\n\n      if (container.faceCount === 6) {\n        texture = new CompressedCubeTexture(faces, format, UnsignedByteType)\n      } else {\n        const mipmaps = faces[0].mipmaps\n\n        texture =\n          container.layerCount > 1\n            ? new CompressedArrayTexture(mipmaps, width, height, container.layerCount, format, UnsignedByteType)\n            : new CompressedTexture(mipmaps, width, height, format, UnsignedByteType)\n      }\n\n      texture.minFilter = faces[0].mipmaps.length === 1 ? LinearFilter : LinearMipmapLinearFilter\n      texture.magFilter = LinearFilter\n      texture.generateMipmaps = false\n      texture.needsUpdate = true\n\n      const colorSpace = parseColorSpace(container)\n      if ('colorSpace' in texture) texture.colorSpace = colorSpace\n      else texture.encoding = colorSpace === SRGBColorSpace ? sRGBEncoding : LinearEncoding\n\n      texture.premultiplyAlpha = !!(dfdFlags & KHR_DF_FLAG_ALPHA_PREMULTIPLIED)\n\n      return texture\n    }\n\n    /**\n     * @param {ArrayBuffer} buffer\n     * @param {object?} config\n     * @return {Promise<CompressedTexture|CompressedArrayTexture|DataTexture|Data3DTexture>}\n     */\n    async _createTexture(buffer, config = {}) {\n      const container = read(new Uint8Array(buffer))\n\n      if (container.vkFormat !== VK_FORMAT_UNDEFINED) {\n        return createRawTexture(container)\n      }\n\n      //\n\n      const taskConfig = config\n      const texturePending = this.init()\n        .then(() => {\n          return this.workerPool.postMessage({ type: 'transcode', buffer, taskConfig: taskConfig }, [buffer])\n        })\n        .then((e) => this._createTextureFrom(e.data, container))\n\n      // Cache the task result.\n      _taskCache.set(buffer, { promise: texturePending })\n\n      return texturePending\n    }\n\n    dispose() {\n      this.workerPool.dispose()\n      if (this.workerSourceURL) URL.revokeObjectURL(this.workerSourceURL)\n\n      _activeLoaders--\n\n      return this\n    }\n  }\n\n  return KTX2Loader\n})()\n\n//\n// Parsing for non-Basis textures. These textures are may have supercompression\n// like Zstd, but they do not require transcoding.\n\nconst UNCOMPRESSED_FORMATS = new Set([RGBAFormat, RGFormat, RedFormat])\n\nconst FORMAT_MAP = {\n  [VK_FORMAT_R32G32B32A32_SFLOAT]: RGBAFormat,\n  [VK_FORMAT_R16G16B16A16_SFLOAT]: RGBAFormat,\n  [VK_FORMAT_R8G8B8A8_UNORM]: RGBAFormat,\n  [VK_FORMAT_R8G8B8A8_SRGB]: RGBAFormat,\n\n  [VK_FORMAT_R32G32_SFLOAT]: RGFormat,\n  [VK_FORMAT_R16G16_SFLOAT]: RGFormat,\n  [VK_FORMAT_R8G8_UNORM]: RGFormat,\n  [VK_FORMAT_R8G8_SRGB]: RGFormat,\n\n  [VK_FORMAT_R32_SFLOAT]: RedFormat,\n  [VK_FORMAT_R16_SFLOAT]: RedFormat,\n  [VK_FORMAT_R8_SRGB]: RedFormat,\n  [VK_FORMAT_R8_UNORM]: RedFormat,\n\n  [VK_FORMAT_ASTC_6x6_SRGB_BLOCK]: RGBA_ASTC_6x6_Format,\n  [VK_FORMAT_ASTC_6x6_UNORM_BLOCK]: RGBA_ASTC_6x6_Format,\n}\n\nconst TYPE_MAP = {\n  [VK_FORMAT_R32G32B32A32_SFLOAT]: FloatType,\n  [VK_FORMAT_R16G16B16A16_SFLOAT]: HalfFloatType,\n  [VK_FORMAT_R8G8B8A8_UNORM]: UnsignedByteType,\n  [VK_FORMAT_R8G8B8A8_SRGB]: UnsignedByteType,\n\n  [VK_FORMAT_R32G32_SFLOAT]: FloatType,\n  [VK_FORMAT_R16G16_SFLOAT]: HalfFloatType,\n  [VK_FORMAT_R8G8_UNORM]: UnsignedByteType,\n  [VK_FORMAT_R8G8_SRGB]: UnsignedByteType,\n\n  [VK_FORMAT_R32_SFLOAT]: FloatType,\n  [VK_FORMAT_R16_SFLOAT]: HalfFloatType,\n  [VK_FORMAT_R8_SRGB]: UnsignedByteType,\n  [VK_FORMAT_R8_UNORM]: UnsignedByteType,\n\n  [VK_FORMAT_ASTC_6x6_SRGB_BLOCK]: UnsignedByteType,\n  [VK_FORMAT_ASTC_6x6_UNORM_BLOCK]: UnsignedByteType,\n}\n\nasync function createRawTexture(container) {\n  const { vkFormat } = container\n\n  if (FORMAT_MAP[vkFormat] === undefined) {\n    throw new Error('THREE.KTX2Loader: Unsupported vkFormat.')\n  }\n\n  //\n\n  let zstd\n\n  if (container.supercompressionScheme === KHR_SUPERCOMPRESSION_ZSTD) {\n    if (!_zstd) {\n      _zstd = new Promise(async (resolve) => {\n        const zstd = new ZSTDDecoder()\n        await zstd.init()\n        resolve(zstd)\n      })\n    }\n\n    zstd = await _zstd\n  }\n\n  //\n\n  const mipmaps = []\n\n  for (let levelIndex = 0; levelIndex < container.levels.length; levelIndex++) {\n    const levelWidth = Math.max(1, container.pixelWidth >> levelIndex)\n    const levelHeight = Math.max(1, container.pixelHeight >> levelIndex)\n    const levelDepth = container.pixelDepth ? Math.max(1, container.pixelDepth >> levelIndex) : 0\n\n    const level = container.levels[levelIndex]\n\n    let levelData\n\n    if (container.supercompressionScheme === KHR_SUPERCOMPRESSION_NONE) {\n      levelData = level.levelData\n    } else if (container.supercompressionScheme === KHR_SUPERCOMPRESSION_ZSTD) {\n      levelData = zstd.decode(level.levelData, level.uncompressedByteLength)\n    } else {\n      throw new Error('THREE.KTX2Loader: Unsupported supercompressionScheme.')\n    }\n\n    let data\n\n    if (TYPE_MAP[vkFormat] === FloatType) {\n      data = new Float32Array(\n        levelData.buffer,\n        levelData.byteOffset,\n        levelData.byteLength / Float32Array.BYTES_PER_ELEMENT,\n      )\n    } else if (TYPE_MAP[vkFormat] === HalfFloatType) {\n      data = new Uint16Array(\n        levelData.buffer,\n        levelData.byteOffset,\n        levelData.byteLength / Uint16Array.BYTES_PER_ELEMENT,\n      )\n    } else {\n      data = levelData\n    }\n\n    mipmaps.push({\n      data: data,\n      width: levelWidth,\n      height: levelHeight,\n      depth: levelDepth,\n    })\n  }\n\n  let texture\n\n  if (UNCOMPRESSED_FORMATS.has(FORMAT_MAP[vkFormat])) {\n    texture =\n      container.pixelDepth === 0\n        ? new DataTexture(mipmaps[0].data, container.pixelWidth, container.pixelHeight)\n        : new Data3DTexture(mipmaps[0].data, container.pixelWidth, container.pixelHeight, container.pixelDepth)\n  } else {\n    if (container.pixelDepth > 0) throw new Error('THREE.KTX2Loader: Unsupported pixelDepth.')\n\n    texture = new CompressedTexture(mipmaps, container.pixelWidth, container.pixelHeight)\n  }\n\n  texture.mipmaps = mipmaps\n\n  texture.type = TYPE_MAP[vkFormat]\n  texture.format = FORMAT_MAP[vkFormat]\n  texture.needsUpdate = true\n\n  const colorSpace = parseColorSpace(container)\n  if ('colorSpace' in texture) texture.colorSpace = colorSpace\n  else texture.encoding = colorSpace === SRGBColorSpace ? sRGBEncoding : LinearEncoding\n\n  //\n\n  return Promise.resolve(texture)\n}\n\nfunction parseColorSpace(container) {\n  const dfd = container.dataFormatDescriptor[0]\n\n  if (dfd.colorPrimaries === KHR_DF_PRIMARIES_BT709) {\n    return dfd.transferFunction === KHR_DF_TRANSFER_SRGB ? SRGBColorSpace : LinearSRGBColorSpace\n  } else if (dfd.colorPrimaries === KHR_DF_PRIMARIES_DISPLAYP3) {\n    return dfd.transferFunction === KHR_DF_TRANSFER_SRGB ? DisplayP3ColorSpace : LinearDisplayP3ColorSpace\n  } else if (dfd.colorPrimaries === KHR_DF_PRIMARIES_UNSPECIFIED) {\n    return NoColorSpace\n  } else {\n    console.warn(`THREE.KTX2Loader: Unsupported color primaries, \"${dfd.colorPrimaries}\"`)\n    return NoColorSpace\n  }\n}\n\nexport { KTX2Loader }\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAoEA,MAAMA,cAAA,GAAiB;AACvB,MAAMC,YAAA,GAAe;AAErB,MAAMC,YAAA,GAAe;AACrB,MAAMC,mBAAA,GAAsB;AAC5B,MAAMC,yBAAA,GAA4B;AAClC,MAAMC,oBAAA,GAAuB;AAC7B,MAAMC,cAAA,GAAiB;AAEvB,MAAMC,UAAA,GAAa,mBAAIC,OAAA,CAAS;AAEhC,IAAIC,cAAA,GAAiB;AAErB,IAAIC,KAAA;AAEC,MAACC,UAAA,GAA8B,sBAAM;EACxC,MAAMC,WAAA,GAAN,cAAyBC,MAAA,CAAO;IAkU9BC,YAAYC,OAAA,EAAS;MACnB,MAAMA,OAAO;MAEb,KAAKC,cAAA,GAAiB;MACtB,KAAKC,gBAAA,GAAmB;MACxB,KAAKC,iBAAA,GAAoB;MAEzB,KAAKC,UAAA,GAAa,IAAIC,UAAA,CAAY;MAClC,KAAKC,eAAA,GAAkB;MACvB,KAAKC,YAAA,GAAe;MAEpB,IAAI,OAAOC,cAAA,KAAmB,aAAa;QACzCC,OAAA,CAAQC,IAAA,CACN,gIAED;MACF;IACF;IAEDC,kBAAkBC,IAAA,EAAM;MACtB,KAAKX,cAAA,GAAiBW,IAAA;MAEtB,OAAO;IACR;IAEDC,eAAeC,GAAA,EAAK;MAClB,KAAKV,UAAA,CAAWS,cAAA,CAAeC,GAAG;MAElC,OAAO;IACR;IAEDC,cAAcC,QAAA,EAAU;MACtB,KAAKT,YAAA,GAAe;QAClBU,aAAA,EAAeD,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,+BAA+B;QACtEC,aAAA,EAAeJ,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,+BAA+B;QACtEE,aAAA,EAAeL,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,8BAA8B;QACrEG,YAAA,EAAcN,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,+BAA+B;QACrEI,aAAA,EAAeP,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,8BAA8B;QACrEK,cAAA,EACER,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,gCAAgC,KACxDH,QAAA,CAASE,UAAA,CAAWC,GAAA,CAAI,uCAAuC;MAClE;MAED,IAAIH,QAAA,CAASS,YAAA,CAAaC,QAAA,EAAU;QAElC,KAAKnB,YAAA,CAAaa,aAAA,GAAgB;MACnC;MAED,OAAO;IACR;IAEDO,KAAA,EAAO;MACL,IAAI,CAAC,KAAKxB,iBAAA,EAAmB;QAE3B,MAAMyB,QAAA,GAAW,IAAIC,UAAA,CAAW,KAAK7B,OAAO;QAC5C4B,QAAA,CAASE,OAAA,CAAQ,KAAK7B,cAAc;QACpC2B,QAAA,CAASG,kBAAA,CAAmB,KAAKC,eAAe;QAChD,MAAMC,SAAA,GAAYL,QAAA,CAASM,SAAA,CAAU,qBAAqB;QAG1D,MAAMC,YAAA,GAAe,IAAIN,UAAA,CAAW,KAAK7B,OAAO;QAChDmC,YAAA,CAAaL,OAAA,CAAQ,KAAK7B,cAAc;QACxCkC,YAAA,CAAaC,eAAA,CAAgB,aAAa;QAC1CD,YAAA,CAAaJ,kBAAA,CAAmB,KAAKC,eAAe;QACpD,MAAMK,aAAA,GAAgBF,YAAA,CAAaD,SAAA,CAAU,uBAAuB;QAEpE,KAAK/B,iBAAA,GAAoBmC,OAAA,CAAQC,GAAA,CAAI,CAACN,SAAA,EAAWI,aAAa,CAAC,EAAEG,IAAA,CAAK,CAAC,CAACC,UAAA,EAAWC,cAAa,MAAM;UACpG,MAAMC,EAAA,GAAK9C,WAAA,CAAW+C,WAAA,CAAYC,QAAA,CAAU;UAE5C,MAAMC,IAAA,GAAO,CACX,mBACA,yBAAyBC,IAAA,CAAKC,SAAA,CAAUnD,WAAA,CAAWoD,YAAY,GAC/D,6BAA6BF,IAAA,CAAKC,SAAA,CAAUnD,WAAA,CAAWqD,gBAAgB,GACvE,wBAAwBH,IAAA,CAAKC,SAAA,CAAUnD,WAAA,CAAWsD,WAAW,GAC7D,6BACAV,UAAA,EACA,gBACAE,EAAA,CAAGS,SAAA,CAAUT,EAAA,CAAGU,OAAA,CAAQ,GAAG,IAAI,GAAGV,EAAA,CAAGW,WAAA,CAAY,GAAG,CAAC,EACjE,CAAYC,IAAA,CAAK,IAAI;UAEX,KAAKjD,eAAA,GAAkBkD,GAAA,CAAIC,eAAA,CAAgB,IAAIC,IAAA,CAAK,CAACZ,IAAI,CAAC,CAAC;UAC3D,KAAK5C,gBAAA,GAAmBwC,cAAA;UAExB,KAAKtC,UAAA,CAAWuD,gBAAA,CAAiB,MAAM;YACrC,MAAMC,MAAA,GAAS,IAAIC,MAAA,CAAO,KAAKvD,eAAe;YAC9C,MAAMJ,gBAAA,GAAmB,KAAKA,gBAAA,CAAiB4D,KAAA,CAAM,CAAC;YAEtDF,MAAA,CAAOG,WAAA,CAAY;cAAEC,IAAA,EAAM;cAAQC,MAAA,EAAQ,KAAK1D,YAAA;cAAcL;YAAA,GAAoB,CAACA,gBAAgB,CAAC;YAEpG,OAAO0D,MAAA;UACnB,CAAW;QACX,CAAS;QAED,IAAIlE,cAAA,GAAiB,GAAG;UAGtBe,OAAA,CAAQC,IAAA,CACN,qJAED;QACF;QAEDhB,cAAA;MACD;MAED,OAAO,KAAKS,iBAAA;IACb;IAED+D,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;MACrC,IAAI,KAAK/D,YAAA,KAAiB,MAAM;QAC9B,MAAM,IAAIgE,KAAA,CAAM,6EAA6E;MAC9F;MAED,MAAMC,MAAA,GAAS,IAAI3C,UAAA,CAAW,KAAK7B,OAAO;MAE1CwE,MAAA,CAAOpC,eAAA,CAAgB,aAAa;MACpCoC,MAAA,CAAOzC,kBAAA,CAAmB,KAAKC,eAAe;MAE9CwC,MAAA,CAAON,IAAA,CACLC,GAAA,EACCM,MAAA,IAAW;QAGV,IAAIjF,UAAA,CAAW2B,GAAA,CAAIsD,MAAM,GAAG;UAC1B,MAAMC,UAAA,GAAalF,UAAA,CAAWmF,GAAA,CAAIF,MAAM;UAExC,OAAOC,UAAA,CAAWE,OAAA,CAAQpC,IAAA,CAAK4B,MAAM,EAAES,KAAA,CAAMP,OAAO;QACrD;QAED,KAAKQ,cAAA,CAAeL,MAAM,EACvBjC,IAAA,CAAMuC,OAAA,IAAaX,MAAA,GAASA,MAAA,CAAOW,OAAO,IAAI,IAAK,EACnDF,KAAA,CAAMP,OAAO;MACjB,GACDD,UAAA,EACAC,OACD;IACF;IAEDU,mBAAmBC,eAAA,EAAiBC,SAAA,EAAW;MAC7C,MAAM;QAAEC,KAAA;QAAOC,KAAA;QAAOC,MAAA;QAAQC,MAAA;QAAQtB,IAAA;QAAMuB,KAAA;QAAOC;MAAQ,IAAKP,eAAA;MAEhE,IAAIjB,IAAA,KAAS,SAAS,OAAO1B,OAAA,CAAQmD,MAAA,CAAOF,KAAK;MAEjD,IAAIR,OAAA;MAEJ,IAAIG,SAAA,CAAUQ,SAAA,KAAc,GAAG;QAC7BX,OAAA,GAAU,IAAIY,qBAAA,CAAsBR,KAAA,EAAOG,MAAA,EAAQM,gBAAgB;MAC3E,OAAa;QACL,MAAMC,OAAA,GAAUV,KAAA,CAAM,CAAC,EAAEU,OAAA;QAEzBd,OAAA,GACEG,SAAA,CAAUY,UAAA,GAAa,IACnB,IAAIC,sBAAA,CAAuBF,OAAA,EAAST,KAAA,EAAOC,MAAA,EAAQH,SAAA,CAAUY,UAAA,EAAYR,MAAA,EAAQM,gBAAgB,IACjG,IAAII,iBAAA,CAAkBH,OAAA,EAAST,KAAA,EAAOC,MAAA,EAAQC,MAAA,EAAQM,gBAAgB;MAC7E;MAEDb,OAAA,CAAQkB,SAAA,GAAYd,KAAA,CAAM,CAAC,EAAEU,OAAA,CAAQK,MAAA,KAAW,IAAIC,YAAA,GAAeC,wBAAA;MACnErB,OAAA,CAAQsB,SAAA,GAAYF,YAAA;MACpBpB,OAAA,CAAQuB,eAAA,GAAkB;MAC1BvB,OAAA,CAAQwB,WAAA,GAAc;MAEtB,MAAMC,UAAA,GAAaC,eAAA,CAAgBvB,SAAS;MAC5C,IAAI,gBAAgBH,OAAA,EAASA,OAAA,CAAQyB,UAAA,GAAaA,UAAA,MAC7CzB,OAAA,CAAQ2B,QAAA,GAAWF,UAAA,KAAejH,cAAA,GAAiBL,YAAA,GAAeD,cAAA;MAEvE8F,OAAA,CAAQ4B,gBAAA,GAAmB,CAAC,EAAEnB,QAAA,GAAWoB,+BAAA;MAEzC,OAAO7B,OAAA;IACR;IAAA;AAAA;AAAA;AAAA;AAAA;IAOD,MAAMD,eAAeL,MAAA,EAAQR,MAAA,GAAS,IAAI;MACxC,MAAMiB,SAAA,GAAY2B,IAAA,CAAK,IAAIC,UAAA,CAAWrC,MAAM,CAAC;MAE7C,IAAIS,SAAA,CAAU6B,QAAA,KAAaC,mBAAA,EAAqB;QAC9C,OAAOC,gBAAA,CAAiB/B,SAAS;MAClC;MAID,MAAMgC,UAAA,GAAajD,MAAA;MACnB,MAAMkD,cAAA,GAAiB,KAAKxF,IAAA,CAAM,EAC/Ba,IAAA,CAAK,MAAM;QACV,OAAO,KAAKpC,UAAA,CAAW2D,WAAA,CAAY;UAAEC,IAAA,EAAM;UAAaS,MAAA;UAAQyC;QAAA,GAA0B,CAACzC,MAAM,CAAC;MAC5G,CAAS,EACAjC,IAAA,CAAM4E,CAAA,IAAM,KAAKpC,kBAAA,CAAmBoC,CAAA,CAAEC,IAAA,EAAMnC,SAAS,CAAC;MAGzD1F,UAAA,CAAW8H,GAAA,CAAI7C,MAAA,EAAQ;QAAEG,OAAA,EAASuC;MAAc,CAAE;MAElD,OAAOA,cAAA;IACR;IAEDI,QAAA,EAAU;MACR,KAAKnH,UAAA,CAAWmH,OAAA,CAAS;MACzB,IAAI,KAAKjH,eAAA,EAAiBkD,GAAA,CAAIgE,eAAA,CAAgB,KAAKlH,eAAe;MAElEZ,cAAA;MAEA,OAAO;IACR;EACF;EA/gBD,IAAM+H,WAAA,GAAN5H,WAAA;EAGE;EAAA6H,aAAA,CAHID,WAAA,EAGG,eAAc;IACnBE,KAAA,EAAO;IACPC,SAAA,EAAW;EACZ;EAEDF,aAAA,CARID,WAAA,EAQG,oBAAmB;IACxBI,IAAA,EAAM;IACNC,IAAA,EAAM;IACNC,GAAA,EAAK;IACLC,GAAA,EAAK;IACLC,GAAA,EAAK;IACLC,GAAA,EAAK;IACLC,kBAAA,EAAoB;IACpBC,MAAA,EAAQ;IACRC,YAAA,EAAc;IACdC,aAAA,EAAe;IACfC,QAAA,EAAU;IACVC,OAAA,EAAS;IACTC,2BAAA,EAA6B;IAC7BC,MAAA,EAAQ;IACRC,MAAA,EAAQ;IACRC,MAAA,EAAQ;IACRC,QAAA,EAAU;EACX;EAEDnB,aAAA,CA5BID,WAAA,EA4BG,gBAAe;IACpBqB,UAAA;IACAC,oBAAA;IACAC,gBAAA;IACAC,oBAAA;IACAC,wBAAA;IACAC,qBAAA;IACAC,eAAA;IACAC,eAAA;IACAC,uBAAA;IACAC;EACD;EAID;EAAA7B,aAAA,CA3CID,WAAA,EA2CG,eAAc,YAAY;IAC/B,IAAIxD,MAAA;IACJ,IAAI9D,iBAAA;IACJ,IAAIqJ,WAAA;IAGJ,MAAMvG,YAAA,GAAewG,aAAA;IAErB,MAAMvG,gBAAA,GAAmBwG,iBAAA;IAEzB,MAAMvG,WAAA,GAAcwG,YAAA;IAEpBC,IAAA,CAAKC,gBAAA,CAAiB,WAAW,UAAUzC,CAAA,EAAG;MAC5C,MAAM0C,OAAA,GAAU1C,CAAA,CAAEC,IAAA;MAElB,QAAQyC,OAAA,CAAQ9F,IAAA;QACd,KAAK;UACHC,MAAA,GAAS6F,OAAA,CAAQ7F,MAAA;UACjBtC,IAAA,CAAKmI,OAAA,CAAQ5J,gBAAgB;UAC7B;QAEF,KAAK;UACHC,iBAAA,CAAkBqC,IAAA,CAAK,MAAM;YAC3B,IAAI;cACF,MAAM;gBAAE2C,KAAA;gBAAO4E,OAAA;gBAAS3E,KAAA;gBAAOC,MAAA;gBAAQ2E,QAAA;gBAAU1E,MAAA;gBAAQE;cAAU,IAAGyE,SAAA,CAAUH,OAAA,CAAQrF,MAAM;cAE9FmF,IAAA,CAAK7F,WAAA,CACH;gBAAEC,IAAA,EAAM;gBAAakG,EAAA,EAAIJ,OAAA,CAAQI,EAAA;gBAAI/E,KAAA;gBAAOC,KAAA;gBAAOC,MAAA;gBAAQ2E,QAAA;gBAAU1E,MAAA;gBAAQE;cAAU,GACvFuE,OACD;YACF,SAAQxE,KAAA,EAAP;cACA9E,OAAA,CAAQ8E,KAAA,CAAMA,KAAK;cAEnBqE,IAAA,CAAK7F,WAAA,CAAY;gBAAEC,IAAA,EAAM;gBAASkG,EAAA,EAAIJ,OAAA,CAAQI,EAAA;gBAAI3E,KAAA,EAAOA,KAAA,CAAMuE;cAAO,CAAE;YACzE;UACf,CAAa;UACD;MACH;IACT,CAAO;IAED,SAASnI,KAAKwI,UAAA,EAAY;MACxBhK,iBAAA,GAAoB,IAAImC,OAAA,CAAS8H,OAAA,IAAY;QAC3CZ,WAAA,GAAc;UAAEW,UAAA;UAAYE,oBAAA,EAAsBD;QAAS;QAC3DE,KAAA,CAAMd,WAAW;MAC3B,CAAS,EAAEhH,IAAA,CAAK,MAAM;QACZgH,WAAA,CAAYe,eAAA,CAAiB;QAE7B,IAAIf,WAAA,CAAYgB,QAAA,KAAa,QAAW;UACtC/J,OAAA,CAAQC,IAAA,CAAK,6DAA6D;QAC3E;MACX,CAAS;IACF;IAED,SAASuJ,UAAUxF,MAAA,EAAQ;MACzB,MAAMgG,QAAA,GAAW,IAAIjB,WAAA,CAAYgB,QAAA,CAAS,IAAI1D,UAAA,CAAWrC,MAAM,CAAC;MAEhE,SAASiG,QAAA,EAAU;QACjBD,QAAA,CAASE,KAAA,CAAO;QAChBF,QAAA,CAASG,MAAA,CAAQ;MAClB;MAED,IAAI,CAACH,QAAA,CAASI,OAAA,IAAW;QACvBH,OAAA,CAAS;QACT,MAAM,IAAInG,KAAA,CAAM,qDAAqD;MACtE;MAED,MAAMuG,WAAA,GAAcL,QAAA,CAASM,OAAA,CAAO,IAAK5H,WAAA,CAAYyE,SAAA,GAAYzE,WAAA,CAAYwE,KAAA;MAC7E,MAAMvC,KAAA,GAAQqF,QAAA,CAASO,QAAA,CAAU;MACjC,MAAM3F,MAAA,GAASoF,QAAA,CAASQ,SAAA,CAAW;MACnC,MAAMnF,UAAA,GAAa2E,QAAA,CAASS,SAAA,CAAS,KAAM;MAC3C,MAAMC,UAAA,GAAaV,QAAA,CAASW,SAAA,CAAW;MACvC,MAAM1F,SAAA,GAAY+E,QAAA,CAASY,QAAA,CAAU;MACrC,MAAMrB,QAAA,GAAWS,QAAA,CAASa,WAAA,CAAa;MACvC,MAAM9F,QAAA,GAAWiF,QAAA,CAASc,WAAA,CAAa;MAEvC,MAAM;QAAEC,gBAAA;QAAkBC;MAAA,IAAiBC,mBAAA,CAAoBZ,WAAA,EAAa1F,KAAA,EAAOC,MAAA,EAAQ2E,QAAQ;MAEnG,IAAI,CAAC5E,KAAA,IAAS,CAACC,MAAA,IAAU,CAAC8F,UAAA,EAAY;QACpCT,OAAA,CAAS;QACT,MAAM,IAAInG,KAAA,CAAM,mCAAmC;MACpD;MAED,IAAI,CAACkG,QAAA,CAASkB,gBAAA,IAAoB;QAChCjB,OAAA,CAAS;QACT,MAAM,IAAInG,KAAA,CAAM,4CAA4C;MAC7D;MAED,MAAMY,KAAA,GAAQ,EAAE;MAChB,MAAM4E,OAAA,GAAU,EAAE;MAElB,SAAS6B,IAAA,GAAO,GAAGA,IAAA,GAAOlG,SAAA,EAAWkG,IAAA,IAAQ;QAC3C,MAAM/F,OAAA,GAAU,EAAE;QAElB,SAASgG,GAAA,GAAM,GAAGA,GAAA,GAAMV,UAAA,EAAYU,GAAA,IAAO;UACzC,MAAMC,SAAA,GAAY,EAAE;UAEpB,IAAIC,QAAA,EAAUC,SAAA;UAEd,SAASC,KAAA,GAAQ,GAAGA,KAAA,GAAQnG,UAAA,EAAYmG,KAAA,IAAS;YAC/C,MAAMC,SAAA,GAAYzB,QAAA,CAAS0B,iBAAA,CAAkBN,GAAA,EAAKI,KAAA,EAAOL,IAAI;YAE7D,IACEA,IAAA,KAAS,KACTC,GAAA,KAAQ,KACRI,KAAA,KAAU,MACTC,SAAA,CAAUE,SAAA,GAAY,MAAM,KAAKF,SAAA,CAAUG,UAAA,GAAa,MAAM,IAC/D;cACA5L,OAAA,CAAQC,IAAA,CAAK,oFAAoF;YAClG;YAED,IAAIyK,UAAA,GAAa,GAAG;cAClBY,QAAA,GAAWG,SAAA,CAAUE,SAAA;cACrBJ,SAAA,GAAYE,SAAA,CAAUG,UAAA;YACtC,OAAqB;cAILN,QAAA,GAAWG,SAAA,CAAU9G,KAAA;cACrB4G,SAAA,GAAYE,SAAA,CAAU7G,MAAA;YACvB;YAED,MAAMiH,GAAA,GAAM,IAAIxF,UAAA,CAAW2D,QAAA,CAAS8B,6BAAA,CAA8BV,GAAA,EAAKI,KAAA,EAAO,GAAGT,gBAAgB,CAAC;YAClG,MAAMgB,MAAA,GAAS/B,QAAA,CAASgC,cAAA,CAAeH,GAAA,EAAKT,GAAA,EAAKI,KAAA,EAAOL,IAAA,EAAMJ,gBAAA,EAAkB,GAAG,IAAI,EAAE;YAEzF,IAAI,CAACgB,MAAA,EAAQ;cACX9B,OAAA,CAAS;cACT,MAAM,IAAInG,KAAA,CAAM,2CAA2C;YAC5D;YAEDuH,SAAA,CAAUY,IAAA,CAAKJ,GAAG;UACnB;UAED,MAAMK,OAAA,GAAUC,MAAA,CAAOd,SAAS;UAEhCjG,OAAA,CAAQ6G,IAAA,CAAK;YAAErF,IAAA,EAAMsF,OAAA;YAASvH,KAAA,EAAO2G,QAAA;YAAU1G,MAAA,EAAQ2G;UAAA,CAAW;UAClEjC,OAAA,CAAQ2C,IAAA,CAAKC,OAAA,CAAQlI,MAAM;QAC5B;QAEDU,KAAA,CAAMuH,IAAA,CAAK;UAAE7G,OAAA;UAAST,KAAA;UAAOC,MAAA;UAAQC,MAAA,EAAQmG;QAAA,CAAc;MAC5D;MAEDf,OAAA,CAAS;MAET,OAAO;QAAEvF,KAAA;QAAO4E,OAAA;QAAS3E,KAAA;QAAOC,MAAA;QAAQ2E,QAAA;QAAU1E,MAAA,EAAQmG,YAAA;QAAcjG;MAAU;IACnF;IAWD,MAAMqH,cAAA,GAAiB,CACrB;MACEC,EAAA,EAAI;MACJhC,WAAA,EAAa,CAAC3H,WAAA,CAAYyE,SAAS;MACnC4D,gBAAA,EAAkB,CAACtI,gBAAA,CAAiBqF,QAAA,EAAUrF,gBAAA,CAAiBqF,QAAQ;MACvEkD,YAAA,EAAc,CAACxI,YAAA,CAAa8F,oBAAA,EAAsB9F,YAAA,CAAa8F,oBAAoB;MACnFgE,aAAA,EAAeC,QAAA;MACfC,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,GACD;MACEJ,EAAA,EAAI;MACJhC,WAAA,EAAa,CAAC3H,WAAA,CAAYwE,KAAA,EAAOxE,WAAA,CAAYyE,SAAS;MACtD4D,gBAAA,EAAkB,CAACtI,gBAAA,CAAiBkF,MAAA,EAAQlF,gBAAA,CAAiBkF,MAAM;MACnEqD,YAAA,EAAc,CAACxI,YAAA,CAAa+F,gBAAA,EAAkB/F,YAAA,CAAa+F,gBAAgB;MAC3E+D,aAAA,EAAe;MACfE,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,GACD;MACEJ,EAAA,EAAI;MACJhC,WAAA,EAAa,CAAC3H,WAAA,CAAYwE,KAAA,EAAOxE,WAAA,CAAYyE,SAAS;MACtD4D,gBAAA,EAAkB,CAACtI,gBAAA,CAAiB6E,GAAA,EAAK7E,gBAAA,CAAiB8E,GAAG;MAC7DyD,YAAA,EAAc,CAACxI,YAAA,CAAasG,oBAAA,EAAsBtG,YAAA,CAAakG,qBAAqB;MACpF4D,aAAA,EAAe;MACfE,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,GACD;MACEJ,EAAA,EAAI;MACJhC,WAAA,EAAa,CAAC3H,WAAA,CAAYwE,KAAA,EAAOxE,WAAA,CAAYyE,SAAS;MACtD4D,gBAAA,EAAkB,CAACtI,gBAAA,CAAiB2E,IAAA,EAAM3E,gBAAA,CAAiB4E,IAAI;MAC/D2D,YAAA,EAAc,CAACxI,YAAA,CAAaoG,eAAA,EAAiBpG,YAAA,CAAagG,oBAAoB;MAC9E8D,aAAA,EAAe;MACfE,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,GACD;MACEJ,EAAA,EAAI;MACJhC,WAAA,EAAa,CAAC3H,WAAA,CAAYwE,KAAA,EAAOxE,WAAA,CAAYyE,SAAS;MACtD4D,gBAAA,EAAkB,CAACtI,gBAAA,CAAiB2E,IAAI;MACxC4D,YAAA,EAAc,CAACxI,YAAA,CAAamG,eAAe;MAC3C2D,aAAA,EAAe;MACfE,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,GACD;MACEJ,EAAA,EAAI;MACJhC,WAAA,EAAa,CAAC3H,WAAA,CAAYwE,KAAA,EAAOxE,WAAA,CAAYyE,SAAS;MACtD4D,gBAAA,EAAkB,CAACtI,gBAAA,CAAiBmF,YAAA,EAAcnF,gBAAA,CAAiBoF,aAAa;MAChFmD,YAAA,EAAc,CAACxI,YAAA,CAAaqG,uBAAA,EAAyBrG,YAAA,CAAaiG,wBAAwB;MAC1F6D,aAAA,EAAe;MACfE,aAAA,EAAe;MACfC,eAAA,EAAiB;IAClB,EACF;IAED,MAAMC,aAAA,GAAgBN,cAAA,CAAeO,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;MACxD,OAAOD,CAAA,CAAEN,aAAA,GAAgBO,CAAA,CAAEP,aAAA;IACnC,CAAO;IACD,MAAMQ,aAAA,GAAgBV,cAAA,CAAeO,IAAA,CAAK,UAAUC,CAAA,EAAGC,CAAA,EAAG;MACxD,OAAOD,CAAA,CAAEJ,aAAA,GAAgBK,CAAA,CAAEL,aAAA;IACnC,CAAO;IAED,SAASvB,oBAAoBZ,WAAA,EAAa1F,KAAA,EAAOC,MAAA,EAAQ2E,QAAA,EAAU;MACjE,IAAIwB,gBAAA;MACJ,IAAIC,YAAA;MAEJ,MAAM+B,OAAA,GAAU1C,WAAA,KAAgB3H,WAAA,CAAYwE,KAAA,GAAQwF,aAAA,GAAgBI,aAAA;MAEpE,SAASE,CAAA,GAAI,GAAGA,CAAA,GAAID,OAAA,CAAQtH,MAAA,EAAQuH,CAAA,IAAK;QACvC,MAAMC,GAAA,GAAMF,OAAA,CAAQC,CAAC;QAErB,IAAI,CAACxJ,MAAA,CAAOyJ,GAAA,CAAIZ,EAAE,GAAG;QACrB,IAAI,CAACY,GAAA,CAAI5C,WAAA,CAAY6C,QAAA,CAAS7C,WAAW,GAAG;QAC5C,IAAId,QAAA,IAAY0D,GAAA,CAAIlC,gBAAA,CAAiBtF,MAAA,GAAS,GAAG;QACjD,IAAIwH,GAAA,CAAIR,eAAA,IAAmB,EAAEU,YAAA,CAAaxI,KAAK,KAAKwI,YAAA,CAAavI,MAAM,IAAI;QAE3EmG,gBAAA,GAAmBkC,GAAA,CAAIlC,gBAAA,CAAiBxB,QAAA,GAAW,IAAI,CAAC;QACxDyB,YAAA,GAAeiC,GAAA,CAAIjC,YAAA,CAAazB,QAAA,GAAW,IAAI,CAAC;QAEhD,OAAO;UAAEwB,gBAAA;UAAkBC;QAAc;MAC1C;MAEDhL,OAAA,CAAQC,IAAA,CAAK,oFAAoF;MAEjG8K,gBAAA,GAAmBtI,gBAAA,CAAiBwF,MAAA;MACpC+C,YAAA,GAAexI,YAAA,CAAa6F,UAAA;MAE5B,OAAO;QAAE0C,gBAAA;QAAkBC;MAAc;IAC1C;IAED,SAASmC,aAAaC,KAAA,EAAO;MAC3B,IAAIA,KAAA,IAAS,GAAG,OAAO;MAEvB,QAAQA,KAAA,GAASA,KAAA,GAAQ,OAAQ,KAAKA,KAAA,KAAU;IACjD;IAGD,SAASjB,OAAOkB,MAAA,EAAQ;MACtB,IAAIA,MAAA,CAAO5H,MAAA,KAAW,GAAG,OAAO4H,MAAA,CAAO,CAAC;MAExC,IAAIC,eAAA,GAAkB;MAEtB,SAASN,CAAA,GAAI,GAAGA,CAAA,GAAIK,MAAA,CAAO5H,MAAA,EAAQuH,CAAA,IAAK;QACtC,MAAMO,KAAA,GAAQF,MAAA,CAAOL,CAAC;QACtBM,eAAA,IAAmBC,KAAA,CAAMC,UAAA;MAC1B;MAED,MAAMC,MAAA,GAAS,IAAIpH,UAAA,CAAWiH,eAAe;MAE7C,IAAII,UAAA,GAAa;MAEjB,SAASV,CAAA,GAAI,GAAGA,CAAA,GAAIK,MAAA,CAAO5H,MAAA,EAAQuH,CAAA,IAAK;QACtC,MAAMO,KAAA,GAAQF,MAAA,CAAOL,CAAC;QACtBS,MAAA,CAAO5G,GAAA,CAAI0G,KAAA,EAAOG,UAAU;QAE5BA,UAAA,IAAcH,KAAA,CAAMC,UAAA;MACrB;MAED,OAAOC,MAAA;IACR;EACF;EAiNH,OAAOzG,WAAA;AACT,GAAI;AAMJ,MAAM2G,oBAAA,GAAuB,mBAAIC,GAAA,CAAI,CAACvF,UAAA,EAAYwF,QAAA,EAAUC,SAAS,CAAC;AAEtE,MAAMC,UAAA,GAAa;EACjB,CAACC,6BAA6B,GAAG3F,UAAA;EACjC,CAAC4F,6BAA6B,GAAG5F,UAAA;EACjC,CAAC6F,wBAAwB,GAAG7F,UAAA;EAC5B,CAAC8F,uBAAuB,GAAG9F,UAAA;EAE3B,CAAC+F,uBAAuB,GAAGP,QAAA;EAC3B,CAACQ,uBAAuB,GAAGR,QAAA;EAC3B,CAACS,oBAAoB,GAAGT,QAAA;EACxB,CAACU,mBAAmB,GAAGV,QAAA;EAEvB,CAACW,oBAAoB,GAAGV,SAAA;EACxB,CAACW,oBAAoB,GAAGX,SAAA;EACxB,CAACY,iBAAiB,GAAGZ,SAAA;EACrB,CAACa,kBAAkB,GAAGb,SAAA;EAEtB,CAACc,6BAA6B,GAAGC,oBAAA;EACjC,CAACC,8BAA8B,GAAGD;AACpC;AAEA,MAAME,QAAA,GAAW;EACf,CAACf,6BAA6B,GAAGgB,SAAA;EACjC,CAACf,6BAA6B,GAAGgB,aAAA;EACjC,CAACf,wBAAwB,GAAG/I,gBAAA;EAC5B,CAACgJ,uBAAuB,GAAGhJ,gBAAA;EAE3B,CAACiJ,uBAAuB,GAAGY,SAAA;EAC3B,CAACX,uBAAuB,GAAGY,aAAA;EAC3B,CAACX,oBAAoB,GAAGnJ,gBAAA;EACxB,CAACoJ,mBAAmB,GAAGpJ,gBAAA;EAEvB,CAACqJ,oBAAoB,GAAGQ,SAAA;EACxB,CAACP,oBAAoB,GAAGQ,aAAA;EACxB,CAACP,iBAAiB,GAAGvJ,gBAAA;EACrB,CAACwJ,kBAAkB,GAAGxJ,gBAAA;EAEtB,CAACyJ,6BAA6B,GAAGzJ,gBAAA;EACjC,CAAC2J,8BAA8B,GAAG3J;AACpC;AAEA,eAAeqB,iBAAiB/B,SAAA,EAAW;EACzC,MAAM;IAAE6B;EAAQ,IAAK7B,SAAA;EAErB,IAAIsJ,UAAA,CAAWzH,QAAQ,MAAM,QAAW;IACtC,MAAM,IAAIxC,KAAA,CAAM,yCAAyC;EAC1D;EAID,IAAIoL,IAAA;EAEJ,IAAIzK,SAAA,CAAU0K,sBAAA,KAA2BC,yBAAA,EAA2B;IAClE,IAAI,CAAClQ,KAAA,EAAO;MACVA,KAAA,GAAQ,IAAI2C,OAAA,CAAQ,MAAO8H,OAAA,IAAY;QACrC,MAAM0F,KAAA,GAAO,IAAIC,WAAA,CAAa;QAC9B,MAAMD,KAAA,CAAKnO,IAAA,CAAM;QACjByI,OAAA,CAAQ0F,KAAI;MACpB,CAAO;IACF;IAEDH,IAAA,GAAO,MAAMhQ,KAAA;EACd;EAID,MAAMkG,OAAA,GAAU,EAAE;EAElB,SAASmK,UAAA,GAAa,GAAGA,UAAA,GAAa9K,SAAA,CAAU+K,MAAA,CAAO/J,MAAA,EAAQ8J,UAAA,IAAc;IAC3E,MAAME,UAAA,GAAaC,IAAA,CAAKC,GAAA,CAAI,GAAGlL,SAAA,CAAUmL,UAAA,IAAcL,UAAU;IACjE,MAAMM,WAAA,GAAcH,IAAA,CAAKC,GAAA,CAAI,GAAGlL,SAAA,CAAUqL,WAAA,IAAeP,UAAU;IACnE,MAAMQ,UAAA,GAAatL,SAAA,CAAUuL,UAAA,GAAaN,IAAA,CAAKC,GAAA,CAAI,GAAGlL,SAAA,CAAUuL,UAAA,IAAcT,UAAU,IAAI;IAE5F,MAAMU,KAAA,GAAQxL,SAAA,CAAU+K,MAAA,CAAOD,UAAU;IAEzC,IAAIW,SAAA;IAEJ,IAAIzL,SAAA,CAAU0K,sBAAA,KAA2BgB,yBAAA,EAA2B;MAClED,SAAA,GAAYD,KAAA,CAAMC,SAAA;IACxB,WAAezL,SAAA,CAAU0K,sBAAA,KAA2BC,yBAAA,EAA2B;MACzEc,SAAA,GAAYhB,IAAA,CAAKkB,MAAA,CAAOH,KAAA,CAAMC,SAAA,EAAWD,KAAA,CAAMI,sBAAsB;IAC3E,OAAW;MACL,MAAM,IAAIvM,KAAA,CAAM,uDAAuD;IACxE;IAED,IAAI8C,IAAA;IAEJ,IAAImI,QAAA,CAASzI,QAAQ,MAAM0I,SAAA,EAAW;MACpCpI,IAAA,GAAO,IAAI0J,YAAA,CACTJ,SAAA,CAAUlM,MAAA,EACVkM,SAAA,CAAUxC,UAAA,EACVwC,SAAA,CAAU1C,UAAA,GAAa8C,YAAA,CAAaC,iBACrC;IACF,WAAUxB,QAAA,CAASzI,QAAQ,MAAM2I,aAAA,EAAe;MAC/CrI,IAAA,GAAO,IAAI4J,WAAA,CACTN,SAAA,CAAUlM,MAAA,EACVkM,SAAA,CAAUxC,UAAA,EACVwC,SAAA,CAAU1C,UAAA,GAAagD,WAAA,CAAYD,iBACpC;IACP,OAAW;MACL3J,IAAA,GAAOsJ,SAAA;IACR;IAED9K,OAAA,CAAQ6G,IAAA,CAAK;MACXrF,IAAA;MACAjC,KAAA,EAAO8K,UAAA;MACP7K,MAAA,EAAQiL,WAAA;MACRY,KAAA,EAAOV;IACb,CAAK;EACF;EAED,IAAIzL,OAAA;EAEJ,IAAIqJ,oBAAA,CAAqBjN,GAAA,CAAIqN,UAAA,CAAWzH,QAAQ,CAAC,GAAG;IAClDhC,OAAA,GACEG,SAAA,CAAUuL,UAAA,KAAe,IACrB,IAAIU,WAAA,CAAYtL,OAAA,CAAQ,CAAC,EAAEwB,IAAA,EAAMnC,SAAA,CAAUmL,UAAA,EAAYnL,SAAA,CAAUqL,WAAW,IAC5E,IAAIa,aAAA,CAAcvL,OAAA,CAAQ,CAAC,EAAEwB,IAAA,EAAMnC,SAAA,CAAUmL,UAAA,EAAYnL,SAAA,CAAUqL,WAAA,EAAarL,SAAA,CAAUuL,UAAU;EAC9G,OAAS;IACL,IAAIvL,SAAA,CAAUuL,UAAA,GAAa,GAAG,MAAM,IAAIlM,KAAA,CAAM,2CAA2C;IAEzFQ,OAAA,GAAU,IAAIiB,iBAAA,CAAkBH,OAAA,EAASX,SAAA,CAAUmL,UAAA,EAAYnL,SAAA,CAAUqL,WAAW;EACrF;EAEDxL,OAAA,CAAQc,OAAA,GAAUA,OAAA;EAElBd,OAAA,CAAQf,IAAA,GAAOwL,QAAA,CAASzI,QAAQ;EAChChC,OAAA,CAAQO,MAAA,GAASkJ,UAAA,CAAWzH,QAAQ;EACpChC,OAAA,CAAQwB,WAAA,GAAc;EAEtB,MAAMC,UAAA,GAAaC,eAAA,CAAgBvB,SAAS;EAC5C,IAAI,gBAAgBH,OAAA,EAASA,OAAA,CAAQyB,UAAA,GAAaA,UAAA,MAC7CzB,OAAA,CAAQ2B,QAAA,GAAWF,UAAA,KAAejH,cAAA,GAAiBL,YAAA,GAAeD,cAAA;EAIvE,OAAOqD,OAAA,CAAQ8H,OAAA,CAAQrF,OAAO;AAChC;AAEA,SAAS0B,gBAAgBvB,SAAA,EAAW;EAClC,MAAMmM,GAAA,GAAMnM,SAAA,CAAUoM,oBAAA,CAAqB,CAAC;EAE5C,IAAID,GAAA,CAAIE,cAAA,KAAmBC,sBAAA,EAAwB;IACjD,OAAOH,GAAA,CAAII,gBAAA,KAAqBC,oBAAA,GAAuBnS,cAAA,GAAiBD,oBAAA;EAC5E,WAAa+R,GAAA,CAAIE,cAAA,KAAmBI,0BAAA,EAA4B;IAC5D,OAAON,GAAA,CAAII,gBAAA,KAAqBC,oBAAA,GAAuBtS,mBAAA,GAAsBC,yBAAA;EACjF,WAAagS,GAAA,CAAIE,cAAA,KAAmBK,4BAAA,EAA8B;IAC9D,OAAOzS,YAAA;EACX,OAAS;IACLsB,OAAA,CAAQC,IAAA,CAAK,mDAAmD2Q,GAAA,CAAIE,cAAA,GAAiB;IACrF,OAAOpS,YAAA;EACR;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}