{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\nicola-portfolio\\\\nicola-portfolio\\\\src\\\\components\\\\Navbar\\\\MobileNavbar\\\\MobileNavbar.jsx\";\nimport React from \"react\";\nimport { Link } from \"react-scroll\";\nimport \"./MobileNavbar.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MobileNavbar = ({\n  isOpen,\n  toggleMenu,\n  activeLink,\n  setActiveLink\n}) => {\n  const handleLinkClick = link => {\n    setActiveLink(link); // Set the active link\n    toggleMenu(); // Close the mobile menu\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `mobile-menu ${isOpen ? \"active\" : \"\"}`,\n    onClick: toggleMenu,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobile-menu-container\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"ul\", {\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"home\",\n            smooth: true,\n            duration: 500,\n            className: `menu-item ${activeLink === \"home\" ? \"active\" : \"\"}`,\n            onClick: () => handleLinkClick(\"home\"),\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"about-me\",\n            smooth: true,\n            duration: 500,\n            className: `menu-item ${activeLink === \"about-me\" ? \"active\" : \"\"}`,\n            onClick: () => handleLinkClick(\"about-me\"),\n            children: \"About\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"skills\",\n            smooth: true,\n            duration: 500,\n            className: `menu-item ${activeLink === \"skills\" ? \"active\" : \"\"}`,\n            onClick: () => handleLinkClick(\"skills\"),\n            children: \"Skills\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"projects\",\n            smooth: true,\n            duration: 500,\n            className: `menu-item ${activeLink === \"projects\" ? \"active\" : \"\"}`,\n            onClick: () => handleLinkClick(\"projects\"),\n            children: \"Projects\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"contact-me\",\n            smooth: true,\n            duration: 500,\n            className: `menu-item ${activeLink === \"contact-me\" ? \"active\" : \"\"}`,\n            onClick: () => handleLinkClick(\"contact-me\"),\n            children: \"Contact Me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"contact-btn\",\n        \"aria-label\": \"Hire Me\",\n        onClick: () => {},\n        children: \"Resume\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = MobileNavbar;\nexport default MobileNavbar;\nvar _c;\n$RefreshReg$(_c, \"MobileNavbar\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "MobileNavbar", "isOpen", "toggleMenu", "activeLink", "setActiveLink", "handleLinkClick", "link", "className", "onClick", "children", "e", "stopPropagation", "to", "smooth", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/components/Navbar/MobileNavbar/MobileNavbar.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Link } from \"react-scroll\";\r\nimport \"./MobileNavbar.css\";\r\n\r\nconst MobileNavbar = ({ isOpen, toggleMenu, activeLink, setActiveLink }) => {\r\n  const handleLinkClick = (link) => {\r\n    setActiveLink(link); // Set the active link\r\n    toggleMenu(); // Close the mobile menu\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`mobile-menu ${isOpen ? \"active\" : \"\"}`}\r\n      onClick={toggleMenu}\r\n    >\r\n      <div className=\"mobile-menu-container\" onClick={(e) => e.stopPropagation()}>\r\n        {/* <img\r\n          src={`${process.env.PUBLIC_URL}/images/menu_20dp.svg`}\r\n          alt=\"\"\r\n          className=\"logo\"\r\n        /> */}\r\n        <ul>\r\n          <li>\r\n            <Link\r\n              to=\"home\"\r\n              smooth={true}\r\n              duration={500}\r\n              className={`menu-item ${activeLink === \"home\" ? \"active\" : \"\"}`}\r\n              onClick={() => handleLinkClick(\"home\")}\r\n            >\r\n              Home\r\n            </Link>\r\n          </li>\r\n          <li>\r\n            <Link\r\n              to=\"about-me\"\r\n              smooth={true}\r\n              duration={500}\r\n              className={`menu-item ${activeLink === \"about-me\" ? \"active\" : \"\"}`}\r\n              onClick={() => handleLinkClick(\"about-me\")}\r\n            >\r\n              About\r\n            </Link>\r\n          </li>\r\n          <li>\r\n            <Link\r\n              to=\"skills\"\r\n              smooth={true}\r\n              duration={500}\r\n              className={`menu-item ${activeLink === \"skills\" ? \"active\" : \"\"}`}\r\n              onClick={() => handleLinkClick(\"skills\")}\r\n            >\r\n              Skills\r\n            </Link>\r\n          </li>\r\n          <li>\r\n            <Link\r\n              to=\"projects\"\r\n              smooth={true}\r\n              duration={500}\r\n              className={`menu-item ${activeLink === \"projects\" ? \"active\" : \"\"}`}\r\n              onClick={() => handleLinkClick(\"projects\")}\r\n            >\r\n              Projects\r\n            </Link>\r\n          </li>\r\n          <li>\r\n            <Link\r\n              to=\"contact-me\"\r\n              smooth={true}\r\n              duration={500}\r\n              className={`menu-item ${activeLink === \"contact-me\" ? \"active\" : \"\"}`}\r\n              onClick={() => handleLinkClick(\"contact-me\")}\r\n            >\r\n              Contact Me\r\n            </Link>\r\n          </li>\r\n        </ul>\r\n        <button className=\"contact-btn\" aria-label=\"Hire Me\" onClick={() => {}}>\r\n          Resume\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MobileNavbar;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,cAAc;AACnC,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC,UAAU;EAAEC,UAAU;EAAEC;AAAc,CAAC,KAAK;EAC1E,MAAMC,eAAe,GAAIC,IAAI,IAAK;IAChCF,aAAa,CAACE,IAAI,CAAC,CAAC,CAAC;IACrBJ,UAAU,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,oBACEH,OAAA;IACEQ,SAAS,EAAE,eAAeN,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;IACnDO,OAAO,EAAEN,UAAW;IAAAO,QAAA,eAEpBV,OAAA;MAAKQ,SAAS,EAAC,uBAAuB;MAACC,OAAO,EAAGE,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAF,QAAA,gBAMzEV,OAAA;QAAAU,QAAA,gBACEV,OAAA;UAAAU,QAAA,eACEV,OAAA,CAACF,IAAI;YACHe,EAAE,EAAC,MAAM;YACTC,MAAM,EAAE,IAAK;YACbC,QAAQ,EAAE,GAAI;YACdP,SAAS,EAAE,aAAaJ,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;YAChEK,OAAO,EAAEA,CAAA,KAAMH,eAAe,CAAC,MAAM,CAAE;YAAAI,QAAA,EACxC;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACLnB,OAAA;UAAAU,QAAA,eACEV,OAAA,CAACF,IAAI;YACHe,EAAE,EAAC,UAAU;YACbC,MAAM,EAAE,IAAK;YACbC,QAAQ,EAAE,GAAI;YACdP,SAAS,EAAE,aAAaJ,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YACpEK,OAAO,EAAEA,CAAA,KAAMH,eAAe,CAAC,UAAU,CAAE;YAAAI,QAAA,EAC5C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACLnB,OAAA;UAAAU,QAAA,eACEV,OAAA,CAACF,IAAI;YACHe,EAAE,EAAC,QAAQ;YACXC,MAAM,EAAE,IAAK;YACbC,QAAQ,EAAE,GAAI;YACdP,SAAS,EAAE,aAAaJ,UAAU,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;YAClEK,OAAO,EAAEA,CAAA,KAAMH,eAAe,CAAC,QAAQ,CAAE;YAAAI,QAAA,EAC1C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACLnB,OAAA;UAAAU,QAAA,eACEV,OAAA,CAACF,IAAI;YACHe,EAAE,EAAC,UAAU;YACbC,MAAM,EAAE,IAAK;YACbC,QAAQ,EAAE,GAAI;YACdP,SAAS,EAAE,aAAaJ,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YACpEK,OAAO,EAAEA,CAAA,KAAMH,eAAe,CAAC,UAAU,CAAE;YAAAI,QAAA,EAC5C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACLnB,OAAA;UAAAU,QAAA,eACEV,OAAA,CAACF,IAAI;YACHe,EAAE,EAAC,YAAY;YACfC,MAAM,EAAE,IAAK;YACbC,QAAQ,EAAE,GAAI;YACdP,SAAS,EAAE,aAAaJ,UAAU,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;YACtEK,OAAO,EAAEA,CAAA,KAAMH,eAAe,CAAC,YAAY,CAAE;YAAAI,QAAA,EAC9C;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACLnB,OAAA;QAAQQ,SAAS,EAAC,aAAa;QAAC,cAAW,SAAS;QAACC,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAE;QAAAC,QAAA,EAAC;MAExE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAhFInB,YAAY;AAkFlB,eAAeA,YAAY;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}