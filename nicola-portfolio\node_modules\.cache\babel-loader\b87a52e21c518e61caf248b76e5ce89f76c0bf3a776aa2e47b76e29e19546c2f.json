{"ast": null, "code": "import { Vector3 } from \"three\";\nconst ColorCorrectionShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    powRGB: {\n      value: /* @__PURE__ */new Vector3(2, 2, 2)\n    },\n    mulRGB: {\n      value: /* @__PURE__ */new Vector3(1, 1, 1)\n    },\n    addRGB: {\n      value: /* @__PURE__ */new Vector3(0, 0, 0)\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform sampler2D tDiffuse;\n    uniform vec3 powRGB;\n    uniform vec3 mulRGB;\n    uniform vec3 addRGB;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tgl_FragColor = texture2D( tDiffuse, vUv );\n    \tgl_FragColor.rgb = mulRGB * pow( ( gl_FragColor.rgb + addRGB ), powRGB );\n\n    }\n  `)\n};\nexport { ColorCorrectionShader };", "map": {"version": 3, "names": ["ColorCorrectionShader", "uniforms", "tDiffuse", "value", "powRGB", "Vector3", "mulRGB", "addRGB", "vertexShader", "fragmentShader"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\shaders\\ColorCorrectionShader.ts"], "sourcesContent": ["import { Vector3 } from 'three'\n\n/**\n * Color correction\n */\n\nexport const ColorCorrectionShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    powRGB: { value: /* @__PURE__ */ new Vector3(2, 2, 2) },\n    mulRGB: { value: /* @__PURE__ */ new Vector3(1, 1, 1) },\n    addRGB: { value: /* @__PURE__ */ new Vector3(0, 0, 0) },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform vec3 powRGB;\n    uniform vec3 mulRGB;\n    uniform vec3 addRGB;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tgl_FragColor = texture2D( tDiffuse, vUv );\n    \tgl_FragColor.rgb = mulRGB * pow( ( gl_FragColor.rgb + addRGB ), powRGB );\n\n    }\n  `,\n}\n"], "mappings": ";AAMO,MAAMA,qBAAA,GAAwB;EACnCC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,MAAA,EAAQ;MAAED,KAAA,EAAuB,mBAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAAE;IACtDC,MAAA,EAAQ;MAAEH,KAAA,EAAuB,mBAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAAE;IACtDE,MAAA,EAAQ;MAAEJ,KAAA,EAAuB,mBAAIE,OAAA,CAAQ,GAAG,GAAG,CAAC;IAAE;EACxD;EAEAG,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAe7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}