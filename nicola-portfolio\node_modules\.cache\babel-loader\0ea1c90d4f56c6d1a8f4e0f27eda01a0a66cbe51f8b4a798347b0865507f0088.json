{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\nicola-portfolio\\\\nicola-portfolio\\\\src\\\\components\\\\AboutMe\\\\AboutMe.jsx\";\nimport React from 'react';\nimport './AboutMe.css';\nimport AboutCard from './AboutCard/AboutCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutMe = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about-me\",\n    className: \"about-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n      children: \"About Me\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"about-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"about-description\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"about-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"I'm a passionate junior developer with a strong foundation in both front-end and back-end technologies. My journey into programming began with curiosity and has evolved into a deep love for creating innovative solutions that make a difference.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"I believe in continuous learning and staying up-to-date with the latest technologies. My approach to development is methodical yet creative, always focusing on writing clean, efficient code while maintaining excellent user experience.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"When I'm not coding, I enjoy exploring new technologies, contributing to open-source projects, and sharing knowledge with the developer community. I'm always excited to take on new challenges and collaborate with like-minded individuals.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"about-cards\",\n        children: [/*#__PURE__*/_jsxDEV(AboutCard, {\n          icon: \"\\uD83C\\uDFAF\",\n          title: \"Working Style\",\n          items: [\"Agile & Collaborative\", \"Detail-Oriented\", \"Problem-Solving Focused\", \"Continuous Learning\", \"User-Centric Approach\"]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AboutCard, {\n          icon: \"\\uD83D\\uDCA1\",\n          title: \"Soft Skills\",\n          items: [\"Effective Communication\", \"Team Collaboration\", \"Time Management\", \"Adaptability\", \"Critical Thinking\"]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AboutCard, {\n          icon: \"\\uD83C\\uDFA8\",\n          title: \"Hobbies & Interests\",\n          items: [\"Open Source Contributing\", \"Tech Blogging\", \"Photography\", \"Gaming\", \"Learning New Languages\"]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = AboutMe;\nexport default AboutMe;\nvar _c;\n$RefreshReg$(_c, \"AboutMe\");", "map": {"version": 3, "names": ["React", "AboutCard", "jsxDEV", "_jsxDEV", "AboutMe", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "title", "items", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/components/AboutMe/AboutMe.jsx"], "sourcesContent": ["import React from 'react';\nimport './AboutMe.css';\nimport AboutCard from './AboutCard/AboutCard';\n\nconst AboutMe = () => {\n  return (\n    <section id=\"about-me\" className=\"about-container\">\n      <h5>About Me</h5>\n      <div className=\"about-content\">\n        <div className=\"about-description\">\n          <div className=\"about-text\">\n            <p>\n              I'm a passionate junior developer with a strong foundation in both front-end and back-end technologies. \n              My journey into programming began with curiosity and has evolved into a deep love for creating innovative \n              solutions that make a difference.\n            </p>\n            <p>\n              I believe in continuous learning and staying up-to-date with the latest technologies. My approach to \n              development is methodical yet creative, always focusing on writing clean, efficient code while \n              maintaining excellent user experience.\n            </p>\n            <p>\n              When I'm not coding, I enjoy exploring new technologies, contributing to open-source projects, and \n              sharing knowledge with the developer community. I'm always excited to take on new challenges and \n              collaborate with like-minded individuals.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"about-cards\">\n          <AboutCard\n            icon=\"🎯\"\n            title=\"Working Style\"\n            items={[\n              \"Agile & Collaborative\",\n              \"Detail-Oriented\",\n              \"Problem-Solving Focused\",\n              \"Continuous Learning\",\n              \"User-Centric Approach\"\n            ]}\n          />\n          \n          <AboutCard\n            icon=\"💡\"\n            title=\"Soft Skills\"\n            items={[\n              \"Effective Communication\",\n              \"Team Collaboration\",\n              \"Time Management\",\n              \"Adaptability\",\n              \"Critical Thinking\"\n            ]}\n          />\n          \n          <AboutCard\n            icon=\"🎨\"\n            title=\"Hobbies & Interests\"\n            items={[\n              \"Open Source Contributing\",\n              \"Tech Blogging\",\n              \"Photography\",\n              \"Gaming\",\n              \"Learning New Languages\"\n            ]}\n          />\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default AboutMe;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AACtB,OAAOC,SAAS,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EACpB,oBACED,OAAA;IAASE,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAChDJ,OAAA;MAAAI,QAAA,EAAI;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjBR,OAAA;MAAKG,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BJ,OAAA;QAAKG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCJ,OAAA;UAAKG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBJ,OAAA;YAAAI,QAAA,EAAG;UAIH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJR,OAAA;YAAAI,QAAA,EAAG;UAIH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJR,OAAA;YAAAI,QAAA,EAAG;UAIH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENR,OAAA;QAAKG,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BJ,OAAA,CAACF,SAAS;UACRW,IAAI,EAAC,cAAI;UACTC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAE,CACL,uBAAuB,EACvB,iBAAiB,EACjB,yBAAyB,EACzB,qBAAqB,EACrB,uBAAuB;QACvB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFR,OAAA,CAACF,SAAS;UACRW,IAAI,EAAC,cAAI;UACTC,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAE,CACL,yBAAyB,EACzB,oBAAoB,EACpB,iBAAiB,EACjB,cAAc,EACd,mBAAmB;QACnB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFR,OAAA,CAACF,SAAS;UACRW,IAAI,EAAC,cAAI;UACTC,KAAK,EAAC,qBAAqB;UAC3BC,KAAK,EAAE,CACL,0BAA0B,EAC1B,eAAe,EACf,aAAa,EACb,QAAQ,EACR,wBAAwB;QACxB;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACI,EAAA,GAjEIX,OAAO;AAmEb,eAAeA,OAAO;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}