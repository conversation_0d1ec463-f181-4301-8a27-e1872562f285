{"ast": null, "code": "import { Loader, LoaderUtils, FileLoader, Group, MeshPhongMaterial, DoubleSide, AdditiveBlending, BufferGeometry, Mesh, Float32BufferAttribute, Matrix4, TextureLoader, Color } from \"three\";\nclass TDSLoader extends Loader {\n  constructor(manager) {\n    super(manager);\n    this.debug = false;\n    this.group = null;\n    this.position = 0;\n    this.materials = [];\n    this.meshes = [];\n  }\n  /**\n   * Load 3ds file from url.\n   *\n   * @method load\n   * @param {[type]} url URL for the file.\n   * @param {Function} onLoad onLoad callback, receives group Object3D as argument.\n   * @param {Function} onProgress onProgress callback.\n   * @param {Function} onError onError callback.\n   */\n  load(url, onLoad, onProgress, onError) {\n    const scope = this;\n    const path = this.path === \"\" ? LoaderUtils.extractUrlBase(url) : this.path;\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(this.requestHeader);\n    loader.setWithCredentials(this.withCredentials);\n    loader.load(url, function (data) {\n      try {\n        onLoad(scope.parse(data, path));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  /**\n   * Parse arraybuffer data and load 3ds file.\n   *\n   * @method parse\n   * @param {ArrayBuffer} arraybuffer Arraybuffer data to be loaded.\n   * @param {String} path Path for external resources.\n   * @return {Group} Group loaded from 3ds file.\n   */\n  parse(arraybuffer, path) {\n    this.group = new Group();\n    this.position = 0;\n    this.materials = [];\n    this.meshes = [];\n    this.readFile(arraybuffer, path);\n    for (let i = 0; i < this.meshes.length; i++) {\n      this.group.add(this.meshes[i]);\n    }\n    return this.group;\n  }\n  /**\n   * Decode file content to read 3ds data.\n   *\n   * @method readFile\n   * @param {ArrayBuffer} arraybuffer Arraybuffer data to be loaded.\n   * @param {String} path Path for external resources.\n   */\n  readFile(arraybuffer, path) {\n    const data = new DataView(arraybuffer);\n    const chunk = this.readChunk(data);\n    if (chunk.id === MLIBMAGIC || chunk.id === CMAGIC || chunk.id === M3DMAGIC) {\n      let next = this.nextChunk(data, chunk);\n      while (next !== 0) {\n        if (next === M3D_VERSION) {\n          const version = this.readDWord(data);\n          this.debugMessage(\"3DS file version: \" + version);\n        } else if (next === MDATA) {\n          this.resetPosition(data);\n          this.readMeshData(data, path);\n        } else {\n          this.debugMessage(\"Unknown main chunk: \" + next.toString(16));\n        }\n        next = this.nextChunk(data, chunk);\n      }\n    }\n    this.debugMessage(\"Parsed \" + this.meshes.length + \" meshes\");\n  }\n  /**\n   * Read mesh data chunk.\n   *\n   * @method readMeshData\n   * @param {Dataview} data Dataview in use.\n   * @param {String} path Path for external resources.\n   */\n  readMeshData(data, path) {\n    const chunk = this.readChunk(data);\n    let next = this.nextChunk(data, chunk);\n    while (next !== 0) {\n      if (next === MESH_VERSION) {\n        const version = +this.readDWord(data);\n        this.debugMessage(\"Mesh Version: \" + version);\n      } else if (next === MASTER_SCALE) {\n        const scale = this.readFloat(data);\n        this.debugMessage(\"Master scale: \" + scale);\n        this.group.scale.set(scale, scale, scale);\n      } else if (next === NAMED_OBJECT) {\n        this.debugMessage(\"Named Object\");\n        this.resetPosition(data);\n        this.readNamedObject(data);\n      } else if (next === MAT_ENTRY) {\n        this.debugMessage(\"Material\");\n        this.resetPosition(data);\n        this.readMaterialEntry(data, path);\n      } else {\n        this.debugMessage(\"Unknown MDATA chunk: \" + next.toString(16));\n      }\n      next = this.nextChunk(data, chunk);\n    }\n  }\n  /**\n   * Read named object chunk.\n   *\n   * @method readNamedObject\n   * @param {Dataview} data Dataview in use.\n   */\n  readNamedObject(data) {\n    const chunk = this.readChunk(data);\n    const name = this.readString(data, 64);\n    chunk.cur = this.position;\n    let next = this.nextChunk(data, chunk);\n    while (next !== 0) {\n      if (next === N_TRI_OBJECT) {\n        this.resetPosition(data);\n        const mesh = this.readMesh(data);\n        mesh.name = name;\n        this.meshes.push(mesh);\n      } else {\n        this.debugMessage(\"Unknown named object chunk: \" + next.toString(16));\n      }\n      next = this.nextChunk(data, chunk);\n    }\n    this.endChunk(chunk);\n  }\n  /**\n   * Read material data chunk and add it to the material list.\n   *\n   * @method readMaterialEntry\n   * @param {Dataview} data Dataview in use.\n   * @param {String} path Path for external resources.\n   */\n  readMaterialEntry(data, path) {\n    const chunk = this.readChunk(data);\n    let next = this.nextChunk(data, chunk);\n    const material = new MeshPhongMaterial();\n    while (next !== 0) {\n      if (next === MAT_NAME) {\n        material.name = this.readString(data, 64);\n        this.debugMessage(\"   Name: \" + material.name);\n      } else if (next === MAT_WIRE) {\n        this.debugMessage(\"   Wireframe\");\n        material.wireframe = true;\n      } else if (next === MAT_WIRE_SIZE) {\n        const value = this.readByte(data);\n        material.wireframeLinewidth = value;\n        this.debugMessage(\"   Wireframe Thickness: \" + value);\n      } else if (next === MAT_TWO_SIDE) {\n        material.side = DoubleSide;\n        this.debugMessage(\"   DoubleSided\");\n      } else if (next === MAT_ADDITIVE) {\n        this.debugMessage(\"   Additive Blending\");\n        material.blending = AdditiveBlending;\n      } else if (next === MAT_DIFFUSE) {\n        this.debugMessage(\"   Diffuse Color\");\n        material.color = this.readColor(data);\n      } else if (next === MAT_SPECULAR) {\n        this.debugMessage(\"   Specular Color\");\n        material.specular = this.readColor(data);\n      } else if (next === MAT_AMBIENT) {\n        this.debugMessage(\"   Ambient color\");\n        material.color = this.readColor(data);\n      } else if (next === MAT_SHININESS) {\n        const shininess = this.readPercentage(data);\n        material.shininess = shininess * 100;\n        this.debugMessage(\"   Shininess : \" + shininess);\n      } else if (next === MAT_TRANSPARENCY) {\n        const transparency = this.readPercentage(data);\n        material.opacity = 1 - transparency;\n        this.debugMessage(\"  Transparency : \" + transparency);\n        material.transparent = material.opacity < 1 ? true : false;\n      } else if (next === MAT_TEXMAP) {\n        this.debugMessage(\"   ColorMap\");\n        this.resetPosition(data);\n        material.map = this.readMap(data, path);\n      } else if (next === MAT_BUMPMAP) {\n        this.debugMessage(\"   BumpMap\");\n        this.resetPosition(data);\n        material.bumpMap = this.readMap(data, path);\n      } else if (next === MAT_OPACMAP) {\n        this.debugMessage(\"   OpacityMap\");\n        this.resetPosition(data);\n        material.alphaMap = this.readMap(data, path);\n      } else if (next === MAT_SPECMAP) {\n        this.debugMessage(\"   SpecularMap\");\n        this.resetPosition(data);\n        material.specularMap = this.readMap(data, path);\n      } else {\n        this.debugMessage(\"   Unknown material chunk: \" + next.toString(16));\n      }\n      next = this.nextChunk(data, chunk);\n    }\n    this.endChunk(chunk);\n    this.materials[material.name] = material;\n  }\n  /**\n   * Read mesh data chunk.\n   *\n   * @method readMesh\n   * @param {Dataview} data Dataview in use.\n   * @return {Mesh} The parsed mesh.\n   */\n  readMesh(data) {\n    const chunk = this.readChunk(data);\n    let next = this.nextChunk(data, chunk);\n    const geometry = new BufferGeometry();\n    const material = new MeshPhongMaterial();\n    const mesh = new Mesh(geometry, material);\n    mesh.name = \"mesh\";\n    while (next !== 0) {\n      if (next === POINT_ARRAY) {\n        const points = this.readWord(data);\n        this.debugMessage(\"   Vertex: \" + points);\n        const vertices = [];\n        for (let i = 0; i < points; i++) {\n          vertices.push(this.readFloat(data));\n          vertices.push(this.readFloat(data));\n          vertices.push(this.readFloat(data));\n        }\n        geometry.setAttribute(\"position\", new Float32BufferAttribute(vertices, 3));\n      } else if (next === FACE_ARRAY) {\n        this.resetPosition(data);\n        this.readFaceArray(data, mesh);\n      } else if (next === TEX_VERTS) {\n        const texels = this.readWord(data);\n        this.debugMessage(\"   UV: \" + texels);\n        const uvs = [];\n        for (let i = 0; i < texels; i++) {\n          uvs.push(this.readFloat(data));\n          uvs.push(this.readFloat(data));\n        }\n        geometry.setAttribute(\"uv\", new Float32BufferAttribute(uvs, 2));\n      } else if (next === MESH_MATRIX) {\n        this.debugMessage(\"   Tranformation Matrix (TODO)\");\n        const values = [];\n        for (let i = 0; i < 12; i++) {\n          values[i] = this.readFloat(data);\n        }\n        const matrix = new Matrix4();\n        matrix.elements[0] = values[0];\n        matrix.elements[1] = values[6];\n        matrix.elements[2] = values[3];\n        matrix.elements[3] = values[9];\n        matrix.elements[4] = values[2];\n        matrix.elements[5] = values[8];\n        matrix.elements[6] = values[5];\n        matrix.elements[7] = values[11];\n        matrix.elements[8] = values[1];\n        matrix.elements[9] = values[7];\n        matrix.elements[10] = values[4];\n        matrix.elements[11] = values[10];\n        matrix.elements[12] = 0;\n        matrix.elements[13] = 0;\n        matrix.elements[14] = 0;\n        matrix.elements[15] = 1;\n        matrix.transpose();\n        const inverse = new Matrix4();\n        inverse.copy(matrix).invert();\n        geometry.applyMatrix4(inverse);\n        matrix.decompose(mesh.position, mesh.quaternion, mesh.scale);\n      } else {\n        this.debugMessage(\"   Unknown mesh chunk: \" + next.toString(16));\n      }\n      next = this.nextChunk(data, chunk);\n    }\n    this.endChunk(chunk);\n    geometry.computeVertexNormals();\n    return mesh;\n  }\n  /**\n   * Read face array data chunk.\n   *\n   * @method readFaceArray\n   * @param {Dataview} data Dataview in use.\n   * @param {Mesh} mesh Mesh to be filled with the data read.\n   */\n  readFaceArray(data, mesh) {\n    const chunk = this.readChunk(data);\n    const faces = this.readWord(data);\n    this.debugMessage(\"   Faces: \" + faces);\n    const index = [];\n    for (let i = 0; i < faces; ++i) {\n      index.push(this.readWord(data), this.readWord(data), this.readWord(data));\n      this.readWord(data);\n    }\n    mesh.geometry.setIndex(index);\n    let materialIndex = 0;\n    let start = 0;\n    while (this.position < chunk.end) {\n      const subchunk = this.readChunk(data);\n      if (subchunk.id === MSH_MAT_GROUP) {\n        this.debugMessage(\"      Material Group\");\n        this.resetPosition(data);\n        const group = this.readMaterialGroup(data);\n        const count = group.index.length * 3;\n        mesh.geometry.addGroup(start, count, materialIndex);\n        start += count;\n        materialIndex++;\n        const material = this.materials[group.name];\n        if (Array.isArray(mesh.material) === false) mesh.material = [];\n        if (material !== void 0) {\n          mesh.material.push(material);\n        }\n      } else {\n        this.debugMessage(\"      Unknown face array chunk: \" + subchunk.toString(16));\n      }\n      this.endChunk(subchunk);\n    }\n    if (mesh.material.length === 1) mesh.material = mesh.material[0];\n    this.endChunk(chunk);\n  }\n  /**\n   * Read texture map data chunk.\n   *\n   * @method readMap\n   * @param {Dataview} data Dataview in use.\n   * @param {String} path Path for external resources.\n   * @return {Texture} Texture read from this data chunk.\n   */\n  readMap(data, path) {\n    const chunk = this.readChunk(data);\n    let next = this.nextChunk(data, chunk);\n    let texture = {};\n    const loader = new TextureLoader(this.manager);\n    loader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin);\n    while (next !== 0) {\n      if (next === MAT_MAPNAME) {\n        const name = this.readString(data, 128);\n        texture = loader.load(name);\n        this.debugMessage(\"      File: \" + path + name);\n      } else if (next === MAT_MAP_UOFFSET) {\n        texture.offset.x = this.readFloat(data);\n        this.debugMessage(\"      OffsetX: \" + texture.offset.x);\n      } else if (next === MAT_MAP_VOFFSET) {\n        texture.offset.y = this.readFloat(data);\n        this.debugMessage(\"      OffsetY: \" + texture.offset.y);\n      } else if (next === MAT_MAP_USCALE) {\n        texture.repeat.x = this.readFloat(data);\n        this.debugMessage(\"      RepeatX: \" + texture.repeat.x);\n      } else if (next === MAT_MAP_VSCALE) {\n        texture.repeat.y = this.readFloat(data);\n        this.debugMessage(\"      RepeatY: \" + texture.repeat.y);\n      } else {\n        this.debugMessage(\"      Unknown map chunk: \" + next.toString(16));\n      }\n      next = this.nextChunk(data, chunk);\n    }\n    this.endChunk(chunk);\n    return texture;\n  }\n  /**\n   * Read material group data chunk.\n   *\n   * @method readMaterialGroup\n   * @param {Dataview} data Dataview in use.\n   * @return {Object} Object with name and index of the object.\n   */\n  readMaterialGroup(data) {\n    this.readChunk(data);\n    const name = this.readString(data, 64);\n    const numFaces = this.readWord(data);\n    this.debugMessage(\"         Name: \" + name);\n    this.debugMessage(\"         Faces: \" + numFaces);\n    const index = [];\n    for (let i = 0; i < numFaces; ++i) {\n      index.push(this.readWord(data));\n    }\n    return {\n      name,\n      index\n    };\n  }\n  /**\n   * Read a color value.\n   *\n   * @method readColor\n   * @param {DataView} data Dataview.\n   * @return {Color} Color value read..\n   */\n  readColor(data) {\n    const chunk = this.readChunk(data);\n    const color = new Color();\n    if (chunk.id === COLOR_24 || chunk.id === LIN_COLOR_24) {\n      const r = this.readByte(data);\n      const g = this.readByte(data);\n      const b = this.readByte(data);\n      color.setRGB(r / 255, g / 255, b / 255);\n      this.debugMessage(\"      Color: \" + color.r + \", \" + color.g + \", \" + color.b);\n    } else if (chunk.id === COLOR_F || chunk.id === LIN_COLOR_F) {\n      const r = this.readFloat(data);\n      const g = this.readFloat(data);\n      const b = this.readFloat(data);\n      color.setRGB(r, g, b);\n      this.debugMessage(\"      Color: \" + color.r + \", \" + color.g + \", \" + color.b);\n    } else {\n      this.debugMessage(\"      Unknown color chunk: \" + chunk.toString(16));\n    }\n    this.endChunk(chunk);\n    return color;\n  }\n  /**\n   * Read next chunk of data.\n   *\n   * @method readChunk\n   * @param {DataView} data Dataview.\n   * @return {Object} Chunk of data read.\n   */\n  readChunk(data) {\n    const chunk = {};\n    chunk.cur = this.position;\n    chunk.id = this.readWord(data);\n    chunk.size = this.readDWord(data);\n    chunk.end = chunk.cur + chunk.size;\n    chunk.cur += 6;\n    return chunk;\n  }\n  /**\n   * Set position to the end of the current chunk of data.\n   *\n   * @method endChunk\n   * @param {Object} chunk Data chunk.\n   */\n  endChunk(chunk) {\n    this.position = chunk.end;\n  }\n  /**\n   * Move to the next data chunk.\n   *\n   * @method nextChunk\n   * @param {DataView} data Dataview.\n   * @param {Object} chunk Data chunk.\n   */\n  nextChunk(data, chunk) {\n    if (chunk.cur >= chunk.end) {\n      return 0;\n    }\n    this.position = chunk.cur;\n    try {\n      const next = this.readChunk(data);\n      chunk.cur += next.size;\n      return next.id;\n    } catch (e) {\n      this.debugMessage(\"Unable to read chunk at \" + this.position);\n      return 0;\n    }\n  }\n  /**\n   * Reset dataview position.\n   *\n   * @method resetPosition\n   */\n  resetPosition() {\n    this.position -= 6;\n  }\n  /**\n   * Read byte value.\n   *\n   * @method readByte\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readByte(data) {\n    const v = data.getUint8(this.position, true);\n    this.position += 1;\n    return v;\n  }\n  /**\n   * Read 32 bit float value.\n   *\n   * @method readFloat\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readFloat(data) {\n    try {\n      const v = data.getFloat32(this.position, true);\n      this.position += 4;\n      return v;\n    } catch (e) {\n      this.debugMessage(e + \" \" + this.position + \" \" + data.byteLength);\n    }\n  }\n  /**\n   * Read 32 bit signed integer value.\n   *\n   * @method readInt\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readInt(data) {\n    const v = data.getInt32(this.position, true);\n    this.position += 4;\n    return v;\n  }\n  /**\n   * Read 16 bit signed integer value.\n   *\n   * @method readShort\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readShort(data) {\n    const v = data.getInt16(this.position, true);\n    this.position += 2;\n    return v;\n  }\n  /**\n   * Read 64 bit unsigned integer value.\n   *\n   * @method readDWord\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readDWord(data) {\n    const v = data.getUint32(this.position, true);\n    this.position += 4;\n    return v;\n  }\n  /**\n   * Read 32 bit unsigned integer value.\n   *\n   * @method readWord\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readWord(data) {\n    const v = data.getUint16(this.position, true);\n    this.position += 2;\n    return v;\n  }\n  /**\n   * Read string value.\n   *\n   * @method readString\n   * @param {DataView} data Dataview to read data from.\n   * @param {Number} maxLength Max size of the string to be read.\n   * @return {String} Data read from the dataview.\n   */\n  readString(data, maxLength) {\n    let s = \"\";\n    for (let i = 0; i < maxLength; i++) {\n      const c = this.readByte(data);\n      if (!c) {\n        break;\n      }\n      s += String.fromCharCode(c);\n    }\n    return s;\n  }\n  /**\n   * Read percentage value.\n   *\n   * @method readPercentage\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readPercentage(data) {\n    const chunk = this.readChunk(data);\n    let value;\n    switch (chunk.id) {\n      case INT_PERCENTAGE:\n        value = this.readShort(data) / 100;\n        break;\n      case FLOAT_PERCENTAGE:\n        value = this.readFloat(data);\n        break;\n      default:\n        this.debugMessage(\"      Unknown percentage chunk: \" + chunk.toString(16));\n    }\n    this.endChunk(chunk);\n    return value;\n  }\n  /**\n   * Print debug message to the console.\n   *\n   * Is controlled by a flag to show or hide debug messages.\n   *\n   * @method debugMessage\n   * @param {Object} message Debug message to print to the console.\n   */\n  debugMessage(message) {\n    if (this.debug) {\n      console.log(message);\n    }\n  }\n}\nconst M3DMAGIC = 19789;\nconst MLIBMAGIC = 15786;\nconst CMAGIC = 49725;\nconst M3D_VERSION = 2;\nconst COLOR_F = 16;\nconst COLOR_24 = 17;\nconst LIN_COLOR_24 = 18;\nconst LIN_COLOR_F = 19;\nconst INT_PERCENTAGE = 48;\nconst FLOAT_PERCENTAGE = 49;\nconst MDATA = 15677;\nconst MESH_VERSION = 15678;\nconst MASTER_SCALE = 256;\nconst MAT_ENTRY = 45055;\nconst MAT_NAME = 40960;\nconst MAT_AMBIENT = 40976;\nconst MAT_DIFFUSE = 40992;\nconst MAT_SPECULAR = 41008;\nconst MAT_SHININESS = 41024;\nconst MAT_TRANSPARENCY = 41040;\nconst MAT_TWO_SIDE = 41089;\nconst MAT_ADDITIVE = 41091;\nconst MAT_WIRE = 41093;\nconst MAT_WIRE_SIZE = 41095;\nconst MAT_TEXMAP = 41472;\nconst MAT_OPACMAP = 41488;\nconst MAT_BUMPMAP = 41520;\nconst MAT_SPECMAP = 41476;\nconst MAT_MAPNAME = 41728;\nconst MAT_MAP_USCALE = 41812;\nconst MAT_MAP_VSCALE = 41814;\nconst MAT_MAP_UOFFSET = 41816;\nconst MAT_MAP_VOFFSET = 41818;\nconst NAMED_OBJECT = 16384;\nconst N_TRI_OBJECT = 16640;\nconst POINT_ARRAY = 16656;\nconst FACE_ARRAY = 16672;\nconst MSH_MAT_GROUP = 16688;\nconst TEX_VERTS = 16704;\nconst MESH_MATRIX = 16736;\nexport { TDSLoader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "Loader", "constructor", "manager", "debug", "group", "position", "materials", "meshes", "load", "url", "onLoad", "onProgress", "onError", "scope", "path", "LoaderUtils", "extractUrlBase", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "data", "parse", "e", "console", "error", "itemError", "arraybuffer", "Group", "readFile", "i", "length", "add", "DataView", "chunk", "readChunk", "id", "MLIBMAGIC", "CMAGIC", "M3DMAGIC", "next", "nextChunk", "M3D_VERSION", "version", "readDWord", "debugMessage", "MDATA", "resetPosition", "readMeshData", "toString", "MESH_VERSION", "MASTER_SCALE", "scale", "readFloat", "set", "NAMED_OBJECT", "readNamedObject", "MAT_ENTRY", "readMaterialEntry", "name", "readString", "cur", "N_TRI_OBJECT", "mesh", "readMesh", "push", "endChunk", "material", "MeshPhongMaterial", "MAT_NAME", "MAT_WIRE", "wireframe", "MAT_WIRE_SIZE", "value", "readByte", "wireframeLinewidth", "MAT_TWO_SIDE", "side", "DoubleSide", "MAT_ADDITIVE", "blending", "AdditiveBlending", "MAT_DIFFUSE", "color", "readColor", "MAT_SPECULAR", "specular", "MAT_AMBIENT", "MAT_SHININESS", "shininess", "readPercentage", "MAT_TRANSPARENCY", "transparency", "opacity", "transparent", "MAT_TEXMAP", "map", "readMap", "MAT_BUMPMAP", "bumpMap", "MAT_OPACMAP", "alphaMap", "MAT_SPECMAP", "specularMap", "geometry", "BufferGeometry", "<PERSON><PERSON>", "POINT_ARRAY", "points", "readWord", "vertices", "setAttribute", "Float32BufferAttribute", "FACE_ARRAY", "readFaceArray", "TEX_VERTS", "texels", "uvs", "MESH_MATRIX", "values", "matrix", "Matrix4", "elements", "transpose", "inverse", "copy", "invert", "applyMatrix4", "decompose", "quaternion", "computeVertexNormals", "faces", "index", "setIndex", "materialIndex", "start", "end", "subchunk", "MSH_MAT_GROUP", "readMaterialGroup", "count", "addGroup", "Array", "isArray", "texture", "TextureLoader", "resourcePath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "MAT_MAPNAME", "MAT_MAP_UOFFSET", "offset", "x", "MAT_MAP_VOFFSET", "y", "MAT_MAP_USCALE", "repeat", "MAT_MAP_VSCALE", "numFaces", "Color", "COLOR_24", "LIN_COLOR_24", "r", "g", "b", "setRGB", "COLOR_F", "LIN_COLOR_F", "size", "v", "getUint8", "getFloat32", "byteLength", "readInt", "getInt32", "readShort", "getInt16", "getUint32", "getUint16", "max<PERSON><PERSON><PERSON>", "s", "c", "String", "fromCharCode", "INT_PERCENTAGE", "FLOAT_PERCENTAGE", "message", "log"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\loaders\\TDSLoader.js"], "sourcesContent": ["import {\n  AdditiveBlending,\n  BufferGeometry,\n  Color,\n  DoubleSide,\n  FileLoader,\n  Float32BufferAttribute,\n  Group,\n  Loader,\n  LoaderUtils,\n  Matrix4,\n  Mesh,\n  MeshPhongMaterial,\n  TextureLoader,\n} from 'three'\n\n/**\n * Autodesk 3DS three.js file loader, based on lib3ds.\n *\n * Loads geometry with uv and materials basic properties with texture support.\n *\n * @class TDSLoader\n * @constructor\n */\n\nclass TDSLoader extends Loader {\n  constructor(manager) {\n    super(manager)\n\n    this.debug = false\n\n    this.group = null\n    this.position = 0\n\n    this.materials = []\n    this.meshes = []\n  }\n\n  /**\n   * Load 3ds file from url.\n   *\n   * @method load\n   * @param {[type]} url URL for the file.\n   * @param {Function} onLoad onLoad callback, receives group Object3D as argument.\n   * @param {Function} onProgress onProgress callback.\n   * @param {Function} onError onError callback.\n   */\n  load(url, onLoad, onProgress, onError) {\n    const scope = this\n\n    const path = this.path === '' ? LoaderUtils.extractUrlBase(url) : this.path\n\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(this.requestHeader)\n    loader.setWithCredentials(this.withCredentials)\n\n    loader.load(\n      url,\n      function (data) {\n        try {\n          onLoad(scope.parse(data, path))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  /**\n   * Parse arraybuffer data and load 3ds file.\n   *\n   * @method parse\n   * @param {ArrayBuffer} arraybuffer Arraybuffer data to be loaded.\n   * @param {String} path Path for external resources.\n   * @return {Group} Group loaded from 3ds file.\n   */\n  parse(arraybuffer, path) {\n    this.group = new Group()\n    this.position = 0\n    this.materials = []\n    this.meshes = []\n\n    this.readFile(arraybuffer, path)\n\n    for (let i = 0; i < this.meshes.length; i++) {\n      this.group.add(this.meshes[i])\n    }\n\n    return this.group\n  }\n\n  /**\n   * Decode file content to read 3ds data.\n   *\n   * @method readFile\n   * @param {ArrayBuffer} arraybuffer Arraybuffer data to be loaded.\n   * @param {String} path Path for external resources.\n   */\n  readFile(arraybuffer, path) {\n    const data = new DataView(arraybuffer)\n    const chunk = this.readChunk(data)\n\n    if (chunk.id === MLIBMAGIC || chunk.id === CMAGIC || chunk.id === M3DMAGIC) {\n      let next = this.nextChunk(data, chunk)\n\n      while (next !== 0) {\n        if (next === M3D_VERSION) {\n          const version = this.readDWord(data)\n          this.debugMessage('3DS file version: ' + version)\n        } else if (next === MDATA) {\n          this.resetPosition(data)\n          this.readMeshData(data, path)\n        } else {\n          this.debugMessage('Unknown main chunk: ' + next.toString(16))\n        }\n\n        next = this.nextChunk(data, chunk)\n      }\n    }\n\n    this.debugMessage('Parsed ' + this.meshes.length + ' meshes')\n  }\n\n  /**\n   * Read mesh data chunk.\n   *\n   * @method readMeshData\n   * @param {Dataview} data Dataview in use.\n   * @param {String} path Path for external resources.\n   */\n  readMeshData(data, path) {\n    const chunk = this.readChunk(data)\n    let next = this.nextChunk(data, chunk)\n\n    while (next !== 0) {\n      if (next === MESH_VERSION) {\n        const version = +this.readDWord(data)\n        this.debugMessage('Mesh Version: ' + version)\n      } else if (next === MASTER_SCALE) {\n        const scale = this.readFloat(data)\n        this.debugMessage('Master scale: ' + scale)\n        this.group.scale.set(scale, scale, scale)\n      } else if (next === NAMED_OBJECT) {\n        this.debugMessage('Named Object')\n        this.resetPosition(data)\n        this.readNamedObject(data)\n      } else if (next === MAT_ENTRY) {\n        this.debugMessage('Material')\n        this.resetPosition(data)\n        this.readMaterialEntry(data, path)\n      } else {\n        this.debugMessage('Unknown MDATA chunk: ' + next.toString(16))\n      }\n\n      next = this.nextChunk(data, chunk)\n    }\n  }\n\n  /**\n   * Read named object chunk.\n   *\n   * @method readNamedObject\n   * @param {Dataview} data Dataview in use.\n   */\n  readNamedObject(data) {\n    const chunk = this.readChunk(data)\n    const name = this.readString(data, 64)\n    chunk.cur = this.position\n\n    let next = this.nextChunk(data, chunk)\n    while (next !== 0) {\n      if (next === N_TRI_OBJECT) {\n        this.resetPosition(data)\n        const mesh = this.readMesh(data)\n        mesh.name = name\n        this.meshes.push(mesh)\n      } else {\n        this.debugMessage('Unknown named object chunk: ' + next.toString(16))\n      }\n\n      next = this.nextChunk(data, chunk)\n    }\n\n    this.endChunk(chunk)\n  }\n\n  /**\n   * Read material data chunk and add it to the material list.\n   *\n   * @method readMaterialEntry\n   * @param {Dataview} data Dataview in use.\n   * @param {String} path Path for external resources.\n   */\n  readMaterialEntry(data, path) {\n    const chunk = this.readChunk(data)\n    let next = this.nextChunk(data, chunk)\n    const material = new MeshPhongMaterial()\n\n    while (next !== 0) {\n      if (next === MAT_NAME) {\n        material.name = this.readString(data, 64)\n        this.debugMessage('   Name: ' + material.name)\n      } else if (next === MAT_WIRE) {\n        this.debugMessage('   Wireframe')\n        material.wireframe = true\n      } else if (next === MAT_WIRE_SIZE) {\n        const value = this.readByte(data)\n        material.wireframeLinewidth = value\n        this.debugMessage('   Wireframe Thickness: ' + value)\n      } else if (next === MAT_TWO_SIDE) {\n        material.side = DoubleSide\n        this.debugMessage('   DoubleSided')\n      } else if (next === MAT_ADDITIVE) {\n        this.debugMessage('   Additive Blending')\n        material.blending = AdditiveBlending\n      } else if (next === MAT_DIFFUSE) {\n        this.debugMessage('   Diffuse Color')\n        material.color = this.readColor(data)\n      } else if (next === MAT_SPECULAR) {\n        this.debugMessage('   Specular Color')\n        material.specular = this.readColor(data)\n      } else if (next === MAT_AMBIENT) {\n        this.debugMessage('   Ambient color')\n        material.color = this.readColor(data)\n      } else if (next === MAT_SHININESS) {\n        const shininess = this.readPercentage(data)\n        material.shininess = shininess * 100\n        this.debugMessage('   Shininess : ' + shininess)\n      } else if (next === MAT_TRANSPARENCY) {\n        const transparency = this.readPercentage(data)\n        material.opacity = 1 - transparency\n        this.debugMessage('  Transparency : ' + transparency)\n        material.transparent = material.opacity < 1 ? true : false\n      } else if (next === MAT_TEXMAP) {\n        this.debugMessage('   ColorMap')\n        this.resetPosition(data)\n        material.map = this.readMap(data, path)\n      } else if (next === MAT_BUMPMAP) {\n        this.debugMessage('   BumpMap')\n        this.resetPosition(data)\n        material.bumpMap = this.readMap(data, path)\n      } else if (next === MAT_OPACMAP) {\n        this.debugMessage('   OpacityMap')\n        this.resetPosition(data)\n        material.alphaMap = this.readMap(data, path)\n      } else if (next === MAT_SPECMAP) {\n        this.debugMessage('   SpecularMap')\n        this.resetPosition(data)\n        material.specularMap = this.readMap(data, path)\n      } else {\n        this.debugMessage('   Unknown material chunk: ' + next.toString(16))\n      }\n\n      next = this.nextChunk(data, chunk)\n    }\n\n    this.endChunk(chunk)\n\n    this.materials[material.name] = material\n  }\n\n  /**\n   * Read mesh data chunk.\n   *\n   * @method readMesh\n   * @param {Dataview} data Dataview in use.\n   * @return {Mesh} The parsed mesh.\n   */\n  readMesh(data) {\n    const chunk = this.readChunk(data)\n    let next = this.nextChunk(data, chunk)\n\n    const geometry = new BufferGeometry()\n\n    const material = new MeshPhongMaterial()\n    const mesh = new Mesh(geometry, material)\n    mesh.name = 'mesh'\n\n    while (next !== 0) {\n      if (next === POINT_ARRAY) {\n        const points = this.readWord(data)\n\n        this.debugMessage('   Vertex: ' + points)\n\n        //BufferGeometry\n\n        const vertices = []\n\n        for (let i = 0; i < points; i++) {\n          vertices.push(this.readFloat(data))\n          vertices.push(this.readFloat(data))\n          vertices.push(this.readFloat(data))\n        }\n\n        geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n      } else if (next === FACE_ARRAY) {\n        this.resetPosition(data)\n        this.readFaceArray(data, mesh)\n      } else if (next === TEX_VERTS) {\n        const texels = this.readWord(data)\n\n        this.debugMessage('   UV: ' + texels)\n\n        //BufferGeometry\n\n        const uvs = []\n\n        for (let i = 0; i < texels; i++) {\n          uvs.push(this.readFloat(data))\n          uvs.push(this.readFloat(data))\n        }\n\n        geometry.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n      } else if (next === MESH_MATRIX) {\n        this.debugMessage('   Tranformation Matrix (TODO)')\n\n        const values = []\n        for (let i = 0; i < 12; i++) {\n          values[i] = this.readFloat(data)\n        }\n\n        const matrix = new Matrix4()\n\n        //X Line\n        matrix.elements[0] = values[0]\n        matrix.elements[1] = values[6]\n        matrix.elements[2] = values[3]\n        matrix.elements[3] = values[9]\n\n        //Y Line\n        matrix.elements[4] = values[2]\n        matrix.elements[5] = values[8]\n        matrix.elements[6] = values[5]\n        matrix.elements[7] = values[11]\n\n        //Z Line\n        matrix.elements[8] = values[1]\n        matrix.elements[9] = values[7]\n        matrix.elements[10] = values[4]\n        matrix.elements[11] = values[10]\n\n        //W Line\n        matrix.elements[12] = 0\n        matrix.elements[13] = 0\n        matrix.elements[14] = 0\n        matrix.elements[15] = 1\n\n        matrix.transpose()\n\n        const inverse = new Matrix4()\n        inverse.copy(matrix).invert()\n        geometry.applyMatrix4(inverse)\n\n        matrix.decompose(mesh.position, mesh.quaternion, mesh.scale)\n      } else {\n        this.debugMessage('   Unknown mesh chunk: ' + next.toString(16))\n      }\n\n      next = this.nextChunk(data, chunk)\n    }\n\n    this.endChunk(chunk)\n\n    geometry.computeVertexNormals()\n\n    return mesh\n  }\n\n  /**\n   * Read face array data chunk.\n   *\n   * @method readFaceArray\n   * @param {Dataview} data Dataview in use.\n   * @param {Mesh} mesh Mesh to be filled with the data read.\n   */\n  readFaceArray(data, mesh) {\n    const chunk = this.readChunk(data)\n    const faces = this.readWord(data)\n\n    this.debugMessage('   Faces: ' + faces)\n\n    const index = []\n\n    for (let i = 0; i < faces; ++i) {\n      index.push(this.readWord(data), this.readWord(data), this.readWord(data))\n\n      this.readWord(data) // visibility\n    }\n\n    mesh.geometry.setIndex(index)\n\n    //The rest of the FACE_ARRAY chunk is subchunks\n\n    let materialIndex = 0\n    let start = 0\n\n    while (this.position < chunk.end) {\n      const subchunk = this.readChunk(data)\n\n      if (subchunk.id === MSH_MAT_GROUP) {\n        this.debugMessage('      Material Group')\n\n        this.resetPosition(data)\n\n        const group = this.readMaterialGroup(data)\n        const count = group.index.length * 3 // assuming successive indices\n\n        mesh.geometry.addGroup(start, count, materialIndex)\n\n        start += count\n        materialIndex++\n\n        const material = this.materials[group.name]\n\n        if (Array.isArray(mesh.material) === false) mesh.material = []\n\n        if (material !== undefined) {\n          mesh.material.push(material)\n        }\n      } else {\n        this.debugMessage('      Unknown face array chunk: ' + subchunk.toString(16))\n      }\n\n      this.endChunk(subchunk)\n    }\n\n    if (mesh.material.length === 1) mesh.material = mesh.material[0] // for backwards compatibility\n\n    this.endChunk(chunk)\n  }\n\n  /**\n   * Read texture map data chunk.\n   *\n   * @method readMap\n   * @param {Dataview} data Dataview in use.\n   * @param {String} path Path for external resources.\n   * @return {Texture} Texture read from this data chunk.\n   */\n  readMap(data, path) {\n    const chunk = this.readChunk(data)\n    let next = this.nextChunk(data, chunk)\n    let texture = {}\n\n    const loader = new TextureLoader(this.manager)\n    loader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin)\n\n    while (next !== 0) {\n      if (next === MAT_MAPNAME) {\n        const name = this.readString(data, 128)\n        texture = loader.load(name)\n\n        this.debugMessage('      File: ' + path + name)\n      } else if (next === MAT_MAP_UOFFSET) {\n        texture.offset.x = this.readFloat(data)\n        this.debugMessage('      OffsetX: ' + texture.offset.x)\n      } else if (next === MAT_MAP_VOFFSET) {\n        texture.offset.y = this.readFloat(data)\n        this.debugMessage('      OffsetY: ' + texture.offset.y)\n      } else if (next === MAT_MAP_USCALE) {\n        texture.repeat.x = this.readFloat(data)\n        this.debugMessage('      RepeatX: ' + texture.repeat.x)\n      } else if (next === MAT_MAP_VSCALE) {\n        texture.repeat.y = this.readFloat(data)\n        this.debugMessage('      RepeatY: ' + texture.repeat.y)\n      } else {\n        this.debugMessage('      Unknown map chunk: ' + next.toString(16))\n      }\n\n      next = this.nextChunk(data, chunk)\n    }\n\n    this.endChunk(chunk)\n\n    return texture\n  }\n\n  /**\n   * Read material group data chunk.\n   *\n   * @method readMaterialGroup\n   * @param {Dataview} data Dataview in use.\n   * @return {Object} Object with name and index of the object.\n   */\n  readMaterialGroup(data) {\n    this.readChunk(data)\n    const name = this.readString(data, 64)\n    const numFaces = this.readWord(data)\n\n    this.debugMessage('         Name: ' + name)\n    this.debugMessage('         Faces: ' + numFaces)\n\n    const index = []\n    for (let i = 0; i < numFaces; ++i) {\n      index.push(this.readWord(data))\n    }\n\n    return { name: name, index: index }\n  }\n\n  /**\n   * Read a color value.\n   *\n   * @method readColor\n   * @param {DataView} data Dataview.\n   * @return {Color} Color value read..\n   */\n  readColor(data) {\n    const chunk = this.readChunk(data)\n    const color = new Color()\n\n    if (chunk.id === COLOR_24 || chunk.id === LIN_COLOR_24) {\n      const r = this.readByte(data)\n      const g = this.readByte(data)\n      const b = this.readByte(data)\n\n      color.setRGB(r / 255, g / 255, b / 255)\n\n      this.debugMessage('      Color: ' + color.r + ', ' + color.g + ', ' + color.b)\n    } else if (chunk.id === COLOR_F || chunk.id === LIN_COLOR_F) {\n      const r = this.readFloat(data)\n      const g = this.readFloat(data)\n      const b = this.readFloat(data)\n\n      color.setRGB(r, g, b)\n\n      this.debugMessage('      Color: ' + color.r + ', ' + color.g + ', ' + color.b)\n    } else {\n      this.debugMessage('      Unknown color chunk: ' + chunk.toString(16))\n    }\n\n    this.endChunk(chunk)\n    return color\n  }\n\n  /**\n   * Read next chunk of data.\n   *\n   * @method readChunk\n   * @param {DataView} data Dataview.\n   * @return {Object} Chunk of data read.\n   */\n  readChunk(data) {\n    const chunk = {}\n\n    chunk.cur = this.position\n    chunk.id = this.readWord(data)\n    chunk.size = this.readDWord(data)\n    chunk.end = chunk.cur + chunk.size\n    chunk.cur += 6\n\n    return chunk\n  }\n\n  /**\n   * Set position to the end of the current chunk of data.\n   *\n   * @method endChunk\n   * @param {Object} chunk Data chunk.\n   */\n  endChunk(chunk) {\n    this.position = chunk.end\n  }\n\n  /**\n   * Move to the next data chunk.\n   *\n   * @method nextChunk\n   * @param {DataView} data Dataview.\n   * @param {Object} chunk Data chunk.\n   */\n  nextChunk(data, chunk) {\n    if (chunk.cur >= chunk.end) {\n      return 0\n    }\n\n    this.position = chunk.cur\n\n    try {\n      const next = this.readChunk(data)\n      chunk.cur += next.size\n      return next.id\n    } catch (e) {\n      this.debugMessage('Unable to read chunk at ' + this.position)\n      return 0\n    }\n  }\n\n  /**\n   * Reset dataview position.\n   *\n   * @method resetPosition\n   */\n  resetPosition() {\n    this.position -= 6\n  }\n\n  /**\n   * Read byte value.\n   *\n   * @method readByte\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readByte(data) {\n    const v = data.getUint8(this.position, true)\n    this.position += 1\n    return v\n  }\n\n  /**\n   * Read 32 bit float value.\n   *\n   * @method readFloat\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readFloat(data) {\n    try {\n      const v = data.getFloat32(this.position, true)\n      this.position += 4\n      return v\n    } catch (e) {\n      this.debugMessage(e + ' ' + this.position + ' ' + data.byteLength)\n    }\n  }\n\n  /**\n   * Read 32 bit signed integer value.\n   *\n   * @method readInt\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readInt(data) {\n    const v = data.getInt32(this.position, true)\n    this.position += 4\n    return v\n  }\n\n  /**\n   * Read 16 bit signed integer value.\n   *\n   * @method readShort\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readShort(data) {\n    const v = data.getInt16(this.position, true)\n    this.position += 2\n    return v\n  }\n\n  /**\n   * Read 64 bit unsigned integer value.\n   *\n   * @method readDWord\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readDWord(data) {\n    const v = data.getUint32(this.position, true)\n    this.position += 4\n    return v\n  }\n\n  /**\n   * Read 32 bit unsigned integer value.\n   *\n   * @method readWord\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readWord(data) {\n    const v = data.getUint16(this.position, true)\n    this.position += 2\n    return v\n  }\n\n  /**\n   * Read string value.\n   *\n   * @method readString\n   * @param {DataView} data Dataview to read data from.\n   * @param {Number} maxLength Max size of the string to be read.\n   * @return {String} Data read from the dataview.\n   */\n  readString(data, maxLength) {\n    let s = ''\n\n    for (let i = 0; i < maxLength; i++) {\n      const c = this.readByte(data)\n      if (!c) {\n        break\n      }\n\n      s += String.fromCharCode(c)\n    }\n\n    return s\n  }\n\n  /**\n   * Read percentage value.\n   *\n   * @method readPercentage\n   * @param {DataView} data Dataview to read data from.\n   * @return {Number} Data read from the dataview.\n   */\n  readPercentage(data) {\n    const chunk = this.readChunk(data)\n    let value\n\n    switch (chunk.id) {\n      case INT_PERCENTAGE:\n        value = this.readShort(data) / 100\n        break\n\n      case FLOAT_PERCENTAGE:\n        value = this.readFloat(data)\n        break\n\n      default:\n        this.debugMessage('      Unknown percentage chunk: ' + chunk.toString(16))\n    }\n\n    this.endChunk(chunk)\n\n    return value\n  }\n\n  /**\n   * Print debug message to the console.\n   *\n   * Is controlled by a flag to show or hide debug messages.\n   *\n   * @method debugMessage\n   * @param {Object} message Debug message to print to the console.\n   */\n  debugMessage(message) {\n    if (this.debug) {\n      console.log(message)\n    }\n  }\n}\n\n// const NULL_CHUNK = 0x0000;\nconst M3DMAGIC = 0x4d4d\n// const SMAGIC = 0x2D2D;\n// const LMAGIC = 0x2D3D;\nconst MLIBMAGIC = 0x3daa\n// const MATMAGIC = 0x3DFF;\nconst CMAGIC = 0xc23d\nconst M3D_VERSION = 0x0002\n// const M3D_KFVERSION = 0x0005;\nconst COLOR_F = 0x0010\nconst COLOR_24 = 0x0011\nconst LIN_COLOR_24 = 0x0012\nconst LIN_COLOR_F = 0x0013\nconst INT_PERCENTAGE = 0x0030\nconst FLOAT_PERCENTAGE = 0x0031\nconst MDATA = 0x3d3d\nconst MESH_VERSION = 0x3d3e\nconst MASTER_SCALE = 0x0100\n// const LO_SHADOW_BIAS = 0x1400;\n// const HI_SHADOW_BIAS = 0x1410;\n// const SHADOW_MAP_SIZE = 0x1420;\n// const SHADOW_SAMPLES = 0x1430;\n// const SHADOW_RANGE = 0x1440;\n// const SHADOW_FILTER = 0x1450;\n// const RAY_BIAS = 0x1460;\n// const O_CONSTS = 0x1500;\n// const AMBIENT_LIGHT = 0x2100;\n// const BIT_MAP = 0x1100;\n// const SOLID_BGND = 0x1200;\n// const V_GRADIENT = 0x1300;\n// const USE_BIT_MAP = 0x1101;\n// const USE_SOLID_BGND = 0x1201;\n// const USE_V_GRADIENT = 0x1301;\n// const FOG = 0x2200;\n// const FOG_BGND = 0x2210;\n// const LAYER_FOG = 0x2302;\n// const DISTANCE_CUE = 0x2300;\n// const DCUE_BGND = 0x2310;\n// const USE_FOG = 0x2201;\n// const USE_LAYER_FOG = 0x2303;\n// const USE_DISTANCE_CUE = 0x2301;\nconst MAT_ENTRY = 0xafff\nconst MAT_NAME = 0xa000\nconst MAT_AMBIENT = 0xa010\nconst MAT_DIFFUSE = 0xa020\nconst MAT_SPECULAR = 0xa030\nconst MAT_SHININESS = 0xa040\n// const MAT_SHIN2PCT = 0xA041;\nconst MAT_TRANSPARENCY = 0xa050\n// const MAT_XPFALL = 0xA052;\n// const MAT_USE_XPFALL = 0xA240;\n// const MAT_REFBLUR = 0xA053;\n// const MAT_SHADING = 0xA100;\n// const MAT_USE_REFBLUR = 0xA250;\n// const MAT_SELF_ILLUM = 0xA084;\nconst MAT_TWO_SIDE = 0xa081\n// const MAT_DECAL = 0xA082;\nconst MAT_ADDITIVE = 0xa083\nconst MAT_WIRE = 0xa085\n// const MAT_FACEMAP = 0xA088;\n// const MAT_TRANSFALLOFF_IN = 0xA08A;\n// const MAT_PHONGSOFT = 0xA08C;\n// const MAT_WIREABS = 0xA08E;\nconst MAT_WIRE_SIZE = 0xa087\nconst MAT_TEXMAP = 0xa200\n// const MAT_SXP_TEXT_DATA = 0xA320;\n// const MAT_TEXMASK = 0xA33E;\n// const MAT_SXP_TEXTMASK_DATA = 0xA32A;\n// const MAT_TEX2MAP = 0xA33A;\n// const MAT_SXP_TEXT2_DATA = 0xA321;\n// const MAT_TEX2MASK = 0xA340;\n// const MAT_SXP_TEXT2MASK_DATA = 0xA32C;\nconst MAT_OPACMAP = 0xa210\n// const MAT_SXP_OPAC_DATA = 0xA322;\n// const MAT_OPACMASK = 0xA342;\n// const MAT_SXP_OPACMASK_DATA = 0xA32E;\nconst MAT_BUMPMAP = 0xa230\n// const MAT_SXP_BUMP_DATA = 0xA324;\n// const MAT_BUMPMASK = 0xA344;\n// const MAT_SXP_BUMPMASK_DATA = 0xA330;\nconst MAT_SPECMAP = 0xa204\n// const MAT_SXP_SPEC_DATA = 0xA325;\n// const MAT_SPECMASK = 0xA348;\n// const MAT_SXP_SPECMASK_DATA = 0xA332;\n// const MAT_SHINMAP = 0xA33C;\n// const MAT_SXP_SHIN_DATA = 0xA326;\n// const MAT_SHINMASK = 0xA346;\n// const MAT_SXP_SHINMASK_DATA = 0xA334;\n// const MAT_SELFIMAP = 0xA33D;\n// const MAT_SXP_SELFI_DATA = 0xA328;\n// const MAT_SELFIMASK = 0xA34A;\n// const MAT_SXP_SELFIMASK_DATA = 0xA336;\n// const MAT_REFLMAP = 0xA220;\n// const MAT_REFLMASK = 0xA34C;\n// const MAT_SXP_REFLMASK_DATA = 0xA338;\n// const MAT_ACUBIC = 0xA310;\nconst MAT_MAPNAME = 0xa300\n// const MAT_MAP_TILING = 0xA351;\n// const MAT_MAP_TEXBLUR = 0xA353;\nconst MAT_MAP_USCALE = 0xa354\nconst MAT_MAP_VSCALE = 0xa356\nconst MAT_MAP_UOFFSET = 0xa358\nconst MAT_MAP_VOFFSET = 0xa35a\n// const MAT_MAP_ANG = 0xA35C;\n// const MAT_MAP_COL1 = 0xA360;\n// const MAT_MAP_COL2 = 0xA362;\n// const MAT_MAP_RCOL = 0xA364;\n// const MAT_MAP_GCOL = 0xA366;\n// const MAT_MAP_BCOL = 0xA368;\nconst NAMED_OBJECT = 0x4000\n// const N_DIRECT_LIGHT = 0x4600;\n// const DL_OFF = 0x4620;\n// const DL_OUTER_RANGE = 0x465A;\n// const DL_INNER_RANGE = 0x4659;\n// const DL_MULTIPLIER = 0x465B;\n// const DL_EXCLUDE = 0x4654;\n// const DL_ATTENUATE = 0x4625;\n// const DL_SPOTLIGHT = 0x4610;\n// const DL_SPOT_ROLL = 0x4656;\n// const DL_SHADOWED = 0x4630;\n// const DL_LOCAL_SHADOW2 = 0x4641;\n// const DL_SEE_CONE = 0x4650;\n// const DL_SPOT_RECTANGULAR = 0x4651;\n// const DL_SPOT_ASPECT = 0x4657;\n// const DL_SPOT_PROJECTOR = 0x4653;\n// const DL_SPOT_OVERSHOOT = 0x4652;\n// const DL_RAY_BIAS = 0x4658;\n// const DL_RAYSHAD = 0x4627;\n// const N_CAMERA = 0x4700;\n// const CAM_SEE_CONE = 0x4710;\n// const CAM_RANGES = 0x4720;\n// const OBJ_HIDDEN = 0x4010;\n// const OBJ_VIS_LOFTER = 0x4011;\n// const OBJ_DOESNT_CAST = 0x4012;\n// const OBJ_DONT_RECVSHADOW = 0x4017;\n// const OBJ_MATTE = 0x4013;\n// const OBJ_FAST = 0x4014;\n// const OBJ_PROCEDURAL = 0x4015;\n// const OBJ_FROZEN = 0x4016;\nconst N_TRI_OBJECT = 0x4100\nconst POINT_ARRAY = 0x4110\n// const POINT_FLAG_ARRAY = 0x4111;\nconst FACE_ARRAY = 0x4120\nconst MSH_MAT_GROUP = 0x4130\n// const SMOOTH_GROUP = 0x4150;\n// const MSH_BOXMAP = 0x4190;\nconst TEX_VERTS = 0x4140\nconst MESH_MATRIX = 0x4160\n// const MESH_COLOR = 0x4165;\n// const MESH_TEXTURE_INFO = 0x4170;\n// const KFDATA = 0xB000;\n// const KFHDR = 0xB00A;\n// const KFSEG = 0xB008;\n// const KFCURTIME = 0xB009;\n// const AMBIENT_NODE_TAG = 0xB001;\n// const OBJECT_NODE_TAG = 0xB002;\n// const CAMERA_NODE_TAG = 0xB003;\n// const TARGET_NODE_TAG = 0xB004;\n// const LIGHT_NODE_TAG = 0xB005;\n// const L_TARGET_NODE_TAG = 0xB006;\n// const SPOTLIGHT_NODE_TAG = 0xB007;\n// const NODE_ID = 0xB030;\n// const NODE_HDR = 0xB010;\n// const PIVOT = 0xB013;\n// const INSTANCE_NAME = 0xB011;\n// const MORPH_SMOOTH = 0xB015;\n// const BOUNDBOX = 0xB014;\n// const POS_TRACK_TAG = 0xB020;\n// const COL_TRACK_TAG = 0xB025;\n// const ROT_TRACK_TAG = 0xB021;\n// const SCL_TRACK_TAG = 0xB022;\n// const MORPH_TRACK_TAG = 0xB026;\n// const FOV_TRACK_TAG = 0xB023;\n// const ROLL_TRACK_TAG = 0xB024;\n// const HOT_TRACK_TAG = 0xB027;\n// const FALL_TRACK_TAG = 0xB028;\n// const HIDE_TRACK_TAG = 0xB029;\n// const POLY_2D = 0x5000;\n// const SHAPE_OK = 0x5010;\n// const SHAPE_NOT_OK = 0x5011;\n// const SHAPE_HOOK = 0x5020;\n// const PATH_3D = 0x6000;\n// const PATH_MATRIX = 0x6005;\n// const SHAPE_2D = 0x6010;\n// const M_SCALE = 0x6020;\n// const M_TWIST = 0x6030;\n// const M_TEETER = 0x6040;\n// const M_FIT = 0x6050;\n// const M_BEVEL = 0x6060;\n// const XZ_CURVE = 0x6070;\n// const YZ_CURVE = 0x6080;\n// const INTERPCT = 0x6090;\n// const DEFORM_LIMIT = 0x60A0;\n// const USE_CONTOUR = 0x6100;\n// const USE_TWEEN = 0x6110;\n// const USE_SCALE = 0x6120;\n// const USE_TWIST = 0x6130;\n// const USE_TEETER = 0x6140;\n// const USE_FIT = 0x6150;\n// const USE_BEVEL = 0x6160;\n// const DEFAULT_VIEW = 0x3000;\n// const VIEW_TOP = 0x3010;\n// const VIEW_BOTTOM = 0x3020;\n// const VIEW_LEFT = 0x3030;\n// const VIEW_RIGHT = 0x3040;\n// const VIEW_FRONT = 0x3050;\n// const VIEW_BACK = 0x3060;\n// const VIEW_USER = 0x3070;\n// const VIEW_CAMERA = 0x3080;\n// const VIEW_WINDOW = 0x3090;\n// const VIEWPORT_LAYOUT_OLD = 0x7000;\n// const VIEWPORT_DATA_OLD = 0x7010;\n// const VIEWPORT_LAYOUT = 0x7001;\n// const VIEWPORT_DATA = 0x7011;\n// const VIEWPORT_DATA_3 = 0x7012;\n// const VIEWPORT_SIZE = 0x7020;\n// const NETWORK_VIEW = 0x7030;\n\nexport { TDSLoader }\n"], "mappings": ";AAyBA,MAAMA,SAAA,SAAkBC,MAAA,CAAO;EAC7BC,YAAYC,OAAA,EAAS;IACnB,MAAMA,OAAO;IAEb,KAAKC,KAAA,GAAQ;IAEb,KAAKC,KAAA,GAAQ;IACb,KAAKC,QAAA,GAAW;IAEhB,KAAKC,SAAA,GAAY,EAAE;IACnB,KAAKC,MAAA,GAAS,EAAE;EACjB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,KAAA,GAAQ;IAEd,MAAMC,IAAA,GAAO,KAAKA,IAAA,KAAS,KAAKC,WAAA,CAAYC,cAAA,CAAeP,GAAG,IAAI,KAAKK,IAAA;IAEvE,MAAMG,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKhB,OAAO;IAC1Ce,MAAA,CAAOE,OAAA,CAAQ,KAAKL,IAAI;IACxBG,MAAA,CAAOG,eAAA,CAAgB,aAAa;IACpCH,MAAA,CAAOI,gBAAA,CAAiB,KAAKC,aAAa;IAC1CL,MAAA,CAAOM,kBAAA,CAAmB,KAAKC,eAAe;IAE9CP,MAAA,CAAOT,IAAA,CACLC,GAAA,EACA,UAAUgB,IAAA,EAAM;MACd,IAAI;QACFf,MAAA,CAAOG,KAAA,CAAMa,KAAA,CAAMD,IAAA,EAAMX,IAAI,CAAC;MAC/B,SAAQa,CAAA,EAAP;QACA,IAAIf,OAAA,EAAS;UACXA,OAAA,CAAQe,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDd,KAAA,CAAMX,OAAA,CAAQ4B,SAAA,CAAUrB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUDc,MAAMK,WAAA,EAAajB,IAAA,EAAM;IACvB,KAAKV,KAAA,GAAQ,IAAI4B,KAAA,CAAO;IACxB,KAAK3B,QAAA,GAAW;IAChB,KAAKC,SAAA,GAAY,EAAE;IACnB,KAAKC,MAAA,GAAS,EAAE;IAEhB,KAAK0B,QAAA,CAASF,WAAA,EAAajB,IAAI;IAE/B,SAASoB,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK3B,MAAA,CAAO4B,MAAA,EAAQD,CAAA,IAAK;MAC3C,KAAK9B,KAAA,CAAMgC,GAAA,CAAI,KAAK7B,MAAA,CAAO2B,CAAC,CAAC;IAC9B;IAED,OAAO,KAAK9B,KAAA;EACb;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASD6B,SAASF,WAAA,EAAajB,IAAA,EAAM;IAC1B,MAAMW,IAAA,GAAO,IAAIY,QAAA,CAASN,WAAW;IACrC,MAAMO,KAAA,GAAQ,KAAKC,SAAA,CAAUd,IAAI;IAEjC,IAAIa,KAAA,CAAME,EAAA,KAAOC,SAAA,IAAaH,KAAA,CAAME,EAAA,KAAOE,MAAA,IAAUJ,KAAA,CAAME,EAAA,KAAOG,QAAA,EAAU;MAC1E,IAAIC,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;MAErC,OAAOM,IAAA,KAAS,GAAG;QACjB,IAAIA,IAAA,KAASE,WAAA,EAAa;UACxB,MAAMC,OAAA,GAAU,KAAKC,SAAA,CAAUvB,IAAI;UACnC,KAAKwB,YAAA,CAAa,uBAAuBF,OAAO;QAC1D,WAAmBH,IAAA,KAASM,KAAA,EAAO;UACzB,KAAKC,aAAA,CAAc1B,IAAI;UACvB,KAAK2B,YAAA,CAAa3B,IAAA,EAAMX,IAAI;QACtC,OAAe;UACL,KAAKmC,YAAA,CAAa,yBAAyBL,IAAA,CAAKS,QAAA,CAAS,EAAE,CAAC;QAC7D;QAEDT,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;MAClC;IACF;IAED,KAAKW,YAAA,CAAa,YAAY,KAAK1C,MAAA,CAAO4B,MAAA,GAAS,SAAS;EAC7D;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDiB,aAAa3B,IAAA,EAAMX,IAAA,EAAM;IACvB,MAAMwB,KAAA,GAAQ,KAAKC,SAAA,CAAUd,IAAI;IACjC,IAAImB,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;IAErC,OAAOM,IAAA,KAAS,GAAG;MACjB,IAAIA,IAAA,KAASU,YAAA,EAAc;QACzB,MAAMP,OAAA,GAAU,CAAC,KAAKC,SAAA,CAAUvB,IAAI;QACpC,KAAKwB,YAAA,CAAa,mBAAmBF,OAAO;MACpD,WAAiBH,IAAA,KAASW,YAAA,EAAc;QAChC,MAAMC,KAAA,GAAQ,KAAKC,SAAA,CAAUhC,IAAI;QACjC,KAAKwB,YAAA,CAAa,mBAAmBO,KAAK;QAC1C,KAAKpD,KAAA,CAAMoD,KAAA,CAAME,GAAA,CAAIF,KAAA,EAAOA,KAAA,EAAOA,KAAK;MAChD,WAAiBZ,IAAA,KAASe,YAAA,EAAc;QAChC,KAAKV,YAAA,CAAa,cAAc;QAChC,KAAKE,aAAA,CAAc1B,IAAI;QACvB,KAAKmC,eAAA,CAAgBnC,IAAI;MACjC,WAAiBmB,IAAA,KAASiB,SAAA,EAAW;QAC7B,KAAKZ,YAAA,CAAa,UAAU;QAC5B,KAAKE,aAAA,CAAc1B,IAAI;QACvB,KAAKqC,iBAAA,CAAkBrC,IAAA,EAAMX,IAAI;MACzC,OAAa;QACL,KAAKmC,YAAA,CAAa,0BAA0BL,IAAA,CAAKS,QAAA,CAAS,EAAE,CAAC;MAC9D;MAEDT,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;IAClC;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDsB,gBAAgBnC,IAAA,EAAM;IACpB,MAAMa,KAAA,GAAQ,KAAKC,SAAA,CAAUd,IAAI;IACjC,MAAMsC,IAAA,GAAO,KAAKC,UAAA,CAAWvC,IAAA,EAAM,EAAE;IACrCa,KAAA,CAAM2B,GAAA,GAAM,KAAK5D,QAAA;IAEjB,IAAIuC,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;IACrC,OAAOM,IAAA,KAAS,GAAG;MACjB,IAAIA,IAAA,KAASsB,YAAA,EAAc;QACzB,KAAKf,aAAA,CAAc1B,IAAI;QACvB,MAAM0C,IAAA,GAAO,KAAKC,QAAA,CAAS3C,IAAI;QAC/B0C,IAAA,CAAKJ,IAAA,GAAOA,IAAA;QACZ,KAAKxD,MAAA,CAAO8D,IAAA,CAAKF,IAAI;MAC7B,OAAa;QACL,KAAKlB,YAAA,CAAa,iCAAiCL,IAAA,CAAKS,QAAA,CAAS,EAAE,CAAC;MACrE;MAEDT,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;IAClC;IAED,KAAKgC,QAAA,CAAShC,KAAK;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDwB,kBAAkBrC,IAAA,EAAMX,IAAA,EAAM;IAC5B,MAAMwB,KAAA,GAAQ,KAAKC,SAAA,CAAUd,IAAI;IACjC,IAAImB,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;IACrC,MAAMiC,QAAA,GAAW,IAAIC,iBAAA,CAAmB;IAExC,OAAO5B,IAAA,KAAS,GAAG;MACjB,IAAIA,IAAA,KAAS6B,QAAA,EAAU;QACrBF,QAAA,CAASR,IAAA,GAAO,KAAKC,UAAA,CAAWvC,IAAA,EAAM,EAAE;QACxC,KAAKwB,YAAA,CAAa,cAAcsB,QAAA,CAASR,IAAI;MACrD,WAAiBnB,IAAA,KAAS8B,QAAA,EAAU;QAC5B,KAAKzB,YAAA,CAAa,cAAc;QAChCsB,QAAA,CAASI,SAAA,GAAY;MAC7B,WAAiB/B,IAAA,KAASgC,aAAA,EAAe;QACjC,MAAMC,KAAA,GAAQ,KAAKC,QAAA,CAASrD,IAAI;QAChC8C,QAAA,CAASQ,kBAAA,GAAqBF,KAAA;QAC9B,KAAK5B,YAAA,CAAa,6BAA6B4B,KAAK;MAC5D,WAAiBjC,IAAA,KAASoC,YAAA,EAAc;QAChCT,QAAA,CAASU,IAAA,GAAOC,UAAA;QAChB,KAAKjC,YAAA,CAAa,gBAAgB;MAC1C,WAAiBL,IAAA,KAASuC,YAAA,EAAc;QAChC,KAAKlC,YAAA,CAAa,sBAAsB;QACxCsB,QAAA,CAASa,QAAA,GAAWC,gBAAA;MAC5B,WAAiBzC,IAAA,KAAS0C,WAAA,EAAa;QAC/B,KAAKrC,YAAA,CAAa,kBAAkB;QACpCsB,QAAA,CAASgB,KAAA,GAAQ,KAAKC,SAAA,CAAU/D,IAAI;MAC5C,WAAiBmB,IAAA,KAAS6C,YAAA,EAAc;QAChC,KAAKxC,YAAA,CAAa,mBAAmB;QACrCsB,QAAA,CAASmB,QAAA,GAAW,KAAKF,SAAA,CAAU/D,IAAI;MAC/C,WAAiBmB,IAAA,KAAS+C,WAAA,EAAa;QAC/B,KAAK1C,YAAA,CAAa,kBAAkB;QACpCsB,QAAA,CAASgB,KAAA,GAAQ,KAAKC,SAAA,CAAU/D,IAAI;MAC5C,WAAiBmB,IAAA,KAASgD,aAAA,EAAe;QACjC,MAAMC,SAAA,GAAY,KAAKC,cAAA,CAAerE,IAAI;QAC1C8C,QAAA,CAASsB,SAAA,GAAYA,SAAA,GAAY;QACjC,KAAK5C,YAAA,CAAa,oBAAoB4C,SAAS;MACvD,WAAiBjD,IAAA,KAASmD,gBAAA,EAAkB;QACpC,MAAMC,YAAA,GAAe,KAAKF,cAAA,CAAerE,IAAI;QAC7C8C,QAAA,CAAS0B,OAAA,GAAU,IAAID,YAAA;QACvB,KAAK/C,YAAA,CAAa,sBAAsB+C,YAAY;QACpDzB,QAAA,CAAS2B,WAAA,GAAc3B,QAAA,CAAS0B,OAAA,GAAU,IAAI,OAAO;MAC7D,WAAiBrD,IAAA,KAASuD,UAAA,EAAY;QAC9B,KAAKlD,YAAA,CAAa,aAAa;QAC/B,KAAKE,aAAA,CAAc1B,IAAI;QACvB8C,QAAA,CAAS6B,GAAA,GAAM,KAAKC,OAAA,CAAQ5E,IAAA,EAAMX,IAAI;MAC9C,WAAiB8B,IAAA,KAAS0D,WAAA,EAAa;QAC/B,KAAKrD,YAAA,CAAa,YAAY;QAC9B,KAAKE,aAAA,CAAc1B,IAAI;QACvB8C,QAAA,CAASgC,OAAA,GAAU,KAAKF,OAAA,CAAQ5E,IAAA,EAAMX,IAAI;MAClD,WAAiB8B,IAAA,KAAS4D,WAAA,EAAa;QAC/B,KAAKvD,YAAA,CAAa,eAAe;QACjC,KAAKE,aAAA,CAAc1B,IAAI;QACvB8C,QAAA,CAASkC,QAAA,GAAW,KAAKJ,OAAA,CAAQ5E,IAAA,EAAMX,IAAI;MACnD,WAAiB8B,IAAA,KAAS8D,WAAA,EAAa;QAC/B,KAAKzD,YAAA,CAAa,gBAAgB;QAClC,KAAKE,aAAA,CAAc1B,IAAI;QACvB8C,QAAA,CAASoC,WAAA,GAAc,KAAKN,OAAA,CAAQ5E,IAAA,EAAMX,IAAI;MACtD,OAAa;QACL,KAAKmC,YAAA,CAAa,gCAAgCL,IAAA,CAAKS,QAAA,CAAS,EAAE,CAAC;MACpE;MAEDT,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;IAClC;IAED,KAAKgC,QAAA,CAAShC,KAAK;IAEnB,KAAKhC,SAAA,CAAUiE,QAAA,CAASR,IAAI,IAAIQ,QAAA;EACjC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDH,SAAS3C,IAAA,EAAM;IACb,MAAMa,KAAA,GAAQ,KAAKC,SAAA,CAAUd,IAAI;IACjC,IAAImB,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;IAErC,MAAMsE,QAAA,GAAW,IAAIC,cAAA,CAAgB;IAErC,MAAMtC,QAAA,GAAW,IAAIC,iBAAA,CAAmB;IACxC,MAAML,IAAA,GAAO,IAAI2C,IAAA,CAAKF,QAAA,EAAUrC,QAAQ;IACxCJ,IAAA,CAAKJ,IAAA,GAAO;IAEZ,OAAOnB,IAAA,KAAS,GAAG;MACjB,IAAIA,IAAA,KAASmE,WAAA,EAAa;QACxB,MAAMC,MAAA,GAAS,KAAKC,QAAA,CAASxF,IAAI;QAEjC,KAAKwB,YAAA,CAAa,gBAAgB+D,MAAM;QAIxC,MAAME,QAAA,GAAW,EAAE;QAEnB,SAAShF,CAAA,GAAI,GAAGA,CAAA,GAAI8E,MAAA,EAAQ9E,CAAA,IAAK;UAC/BgF,QAAA,CAAS7C,IAAA,CAAK,KAAKZ,SAAA,CAAUhC,IAAI,CAAC;UAClCyF,QAAA,CAAS7C,IAAA,CAAK,KAAKZ,SAAA,CAAUhC,IAAI,CAAC;UAClCyF,QAAA,CAAS7C,IAAA,CAAK,KAAKZ,SAAA,CAAUhC,IAAI,CAAC;QACnC;QAEDmF,QAAA,CAASO,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBF,QAAA,EAAU,CAAC,CAAC;MACjF,WAAiBtE,IAAA,KAASyE,UAAA,EAAY;QAC9B,KAAKlE,aAAA,CAAc1B,IAAI;QACvB,KAAK6F,aAAA,CAAc7F,IAAA,EAAM0C,IAAI;MACrC,WAAiBvB,IAAA,KAAS2E,SAAA,EAAW;QAC7B,MAAMC,MAAA,GAAS,KAAKP,QAAA,CAASxF,IAAI;QAEjC,KAAKwB,YAAA,CAAa,YAAYuE,MAAM;QAIpC,MAAMC,GAAA,GAAM,EAAE;QAEd,SAASvF,CAAA,GAAI,GAAGA,CAAA,GAAIsF,MAAA,EAAQtF,CAAA,IAAK;UAC/BuF,GAAA,CAAIpD,IAAA,CAAK,KAAKZ,SAAA,CAAUhC,IAAI,CAAC;UAC7BgG,GAAA,CAAIpD,IAAA,CAAK,KAAKZ,SAAA,CAAUhC,IAAI,CAAC;QAC9B;QAEDmF,QAAA,CAASO,YAAA,CAAa,MAAM,IAAIC,sBAAA,CAAuBK,GAAA,EAAK,CAAC,CAAC;MACtE,WAAiB7E,IAAA,KAAS8E,WAAA,EAAa;QAC/B,KAAKzE,YAAA,CAAa,gCAAgC;QAElD,MAAM0E,MAAA,GAAS,EAAE;QACjB,SAASzF,CAAA,GAAI,GAAGA,CAAA,GAAI,IAAIA,CAAA,IAAK;UAC3ByF,MAAA,CAAOzF,CAAC,IAAI,KAAKuB,SAAA,CAAUhC,IAAI;QAChC;QAED,MAAMmG,MAAA,GAAS,IAAIC,OAAA,CAAS;QAG5BD,MAAA,CAAOE,QAAA,CAAS,CAAC,IAAIH,MAAA,CAAO,CAAC;QAC7BC,MAAA,CAAOE,QAAA,CAAS,CAAC,IAAIH,MAAA,CAAO,CAAC;QAC7BC,MAAA,CAAOE,QAAA,CAAS,CAAC,IAAIH,MAAA,CAAO,CAAC;QAC7BC,MAAA,CAAOE,QAAA,CAAS,CAAC,IAAIH,MAAA,CAAO,CAAC;QAG7BC,MAAA,CAAOE,QAAA,CAAS,CAAC,IAAIH,MAAA,CAAO,CAAC;QAC7BC,MAAA,CAAOE,QAAA,CAAS,CAAC,IAAIH,MAAA,CAAO,CAAC;QAC7BC,MAAA,CAAOE,QAAA,CAAS,CAAC,IAAIH,MAAA,CAAO,CAAC;QAC7BC,MAAA,CAAOE,QAAA,CAAS,CAAC,IAAIH,MAAA,CAAO,EAAE;QAG9BC,MAAA,CAAOE,QAAA,CAAS,CAAC,IAAIH,MAAA,CAAO,CAAC;QAC7BC,MAAA,CAAOE,QAAA,CAAS,CAAC,IAAIH,MAAA,CAAO,CAAC;QAC7BC,MAAA,CAAOE,QAAA,CAAS,EAAE,IAAIH,MAAA,CAAO,CAAC;QAC9BC,MAAA,CAAOE,QAAA,CAAS,EAAE,IAAIH,MAAA,CAAO,EAAE;QAG/BC,MAAA,CAAOE,QAAA,CAAS,EAAE,IAAI;QACtBF,MAAA,CAAOE,QAAA,CAAS,EAAE,IAAI;QACtBF,MAAA,CAAOE,QAAA,CAAS,EAAE,IAAI;QACtBF,MAAA,CAAOE,QAAA,CAAS,EAAE,IAAI;QAEtBF,MAAA,CAAOG,SAAA,CAAW;QAElB,MAAMC,OAAA,GAAU,IAAIH,OAAA,CAAS;QAC7BG,OAAA,CAAQC,IAAA,CAAKL,MAAM,EAAEM,MAAA,CAAQ;QAC7BtB,QAAA,CAASuB,YAAA,CAAaH,OAAO;QAE7BJ,MAAA,CAAOQ,SAAA,CAAUjE,IAAA,CAAK9D,QAAA,EAAU8D,IAAA,CAAKkE,UAAA,EAAYlE,IAAA,CAAKX,KAAK;MACnE,OAAa;QACL,KAAKP,YAAA,CAAa,4BAA4BL,IAAA,CAAKS,QAAA,CAAS,EAAE,CAAC;MAChE;MAEDT,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;IAClC;IAED,KAAKgC,QAAA,CAAShC,KAAK;IAEnBsE,QAAA,CAAS0B,oBAAA,CAAsB;IAE/B,OAAOnE,IAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDmD,cAAc7F,IAAA,EAAM0C,IAAA,EAAM;IACxB,MAAM7B,KAAA,GAAQ,KAAKC,SAAA,CAAUd,IAAI;IACjC,MAAM8G,KAAA,GAAQ,KAAKtB,QAAA,CAASxF,IAAI;IAEhC,KAAKwB,YAAA,CAAa,eAAesF,KAAK;IAEtC,MAAMC,KAAA,GAAQ,EAAE;IAEhB,SAAStG,CAAA,GAAI,GAAGA,CAAA,GAAIqG,KAAA,EAAO,EAAErG,CAAA,EAAG;MAC9BsG,KAAA,CAAMnE,IAAA,CAAK,KAAK4C,QAAA,CAASxF,IAAI,GAAG,KAAKwF,QAAA,CAASxF,IAAI,GAAG,KAAKwF,QAAA,CAASxF,IAAI,CAAC;MAExE,KAAKwF,QAAA,CAASxF,IAAI;IACnB;IAED0C,IAAA,CAAKyC,QAAA,CAAS6B,QAAA,CAASD,KAAK;IAI5B,IAAIE,aAAA,GAAgB;IACpB,IAAIC,KAAA,GAAQ;IAEZ,OAAO,KAAKtI,QAAA,GAAWiC,KAAA,CAAMsG,GAAA,EAAK;MAChC,MAAMC,QAAA,GAAW,KAAKtG,SAAA,CAAUd,IAAI;MAEpC,IAAIoH,QAAA,CAASrG,EAAA,KAAOsG,aAAA,EAAe;QACjC,KAAK7F,YAAA,CAAa,sBAAsB;QAExC,KAAKE,aAAA,CAAc1B,IAAI;QAEvB,MAAMrB,KAAA,GAAQ,KAAK2I,iBAAA,CAAkBtH,IAAI;QACzC,MAAMuH,KAAA,GAAQ5I,KAAA,CAAMoI,KAAA,CAAMrG,MAAA,GAAS;QAEnCgC,IAAA,CAAKyC,QAAA,CAASqC,QAAA,CAASN,KAAA,EAAOK,KAAA,EAAON,aAAa;QAElDC,KAAA,IAASK,KAAA;QACTN,aAAA;QAEA,MAAMnE,QAAA,GAAW,KAAKjE,SAAA,CAAUF,KAAA,CAAM2D,IAAI;QAE1C,IAAImF,KAAA,CAAMC,OAAA,CAAQhF,IAAA,CAAKI,QAAQ,MAAM,OAAOJ,IAAA,CAAKI,QAAA,GAAW,EAAE;QAE9D,IAAIA,QAAA,KAAa,QAAW;UAC1BJ,IAAA,CAAKI,QAAA,CAASF,IAAA,CAAKE,QAAQ;QAC5B;MACT,OAAa;QACL,KAAKtB,YAAA,CAAa,qCAAqC4F,QAAA,CAASxF,QAAA,CAAS,EAAE,CAAC;MAC7E;MAED,KAAKiB,QAAA,CAASuE,QAAQ;IACvB;IAED,IAAI1E,IAAA,CAAKI,QAAA,CAASpC,MAAA,KAAW,GAAGgC,IAAA,CAAKI,QAAA,GAAWJ,IAAA,CAAKI,QAAA,CAAS,CAAC;IAE/D,KAAKD,QAAA,CAAShC,KAAK;EACpB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUD+D,QAAQ5E,IAAA,EAAMX,IAAA,EAAM;IAClB,MAAMwB,KAAA,GAAQ,KAAKC,SAAA,CAAUd,IAAI;IACjC,IAAImB,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;IACrC,IAAI8G,OAAA,GAAU,CAAE;IAEhB,MAAMnI,MAAA,GAAS,IAAIoI,aAAA,CAAc,KAAKnJ,OAAO;IAC7Ce,MAAA,CAAOE,OAAA,CAAQ,KAAKmI,YAAA,IAAgBxI,IAAI,EAAEyI,cAAA,CAAe,KAAKC,WAAW;IAEzE,OAAO5G,IAAA,KAAS,GAAG;MACjB,IAAIA,IAAA,KAAS6G,WAAA,EAAa;QACxB,MAAM1F,IAAA,GAAO,KAAKC,UAAA,CAAWvC,IAAA,EAAM,GAAG;QACtC2H,OAAA,GAAUnI,MAAA,CAAOT,IAAA,CAAKuD,IAAI;QAE1B,KAAKd,YAAA,CAAa,iBAAiBnC,IAAA,GAAOiD,IAAI;MACtD,WAAiBnB,IAAA,KAAS8G,eAAA,EAAiB;QACnCN,OAAA,CAAQO,MAAA,CAAOC,CAAA,GAAI,KAAKnG,SAAA,CAAUhC,IAAI;QACtC,KAAKwB,YAAA,CAAa,oBAAoBmG,OAAA,CAAQO,MAAA,CAAOC,CAAC;MAC9D,WAAiBhH,IAAA,KAASiH,eAAA,EAAiB;QACnCT,OAAA,CAAQO,MAAA,CAAOG,CAAA,GAAI,KAAKrG,SAAA,CAAUhC,IAAI;QACtC,KAAKwB,YAAA,CAAa,oBAAoBmG,OAAA,CAAQO,MAAA,CAAOG,CAAC;MAC9D,WAAiBlH,IAAA,KAASmH,cAAA,EAAgB;QAClCX,OAAA,CAAQY,MAAA,CAAOJ,CAAA,GAAI,KAAKnG,SAAA,CAAUhC,IAAI;QACtC,KAAKwB,YAAA,CAAa,oBAAoBmG,OAAA,CAAQY,MAAA,CAAOJ,CAAC;MAC9D,WAAiBhH,IAAA,KAASqH,cAAA,EAAgB;QAClCb,OAAA,CAAQY,MAAA,CAAOF,CAAA,GAAI,KAAKrG,SAAA,CAAUhC,IAAI;QACtC,KAAKwB,YAAA,CAAa,oBAAoBmG,OAAA,CAAQY,MAAA,CAAOF,CAAC;MAC9D,OAAa;QACL,KAAK7G,YAAA,CAAa,8BAA8BL,IAAA,CAAKS,QAAA,CAAS,EAAE,CAAC;MAClE;MAEDT,IAAA,GAAO,KAAKC,SAAA,CAAUpB,IAAA,EAAMa,KAAK;IAClC;IAED,KAAKgC,QAAA,CAAShC,KAAK;IAEnB,OAAO8G,OAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDL,kBAAkBtH,IAAA,EAAM;IACtB,KAAKc,SAAA,CAAUd,IAAI;IACnB,MAAMsC,IAAA,GAAO,KAAKC,UAAA,CAAWvC,IAAA,EAAM,EAAE;IACrC,MAAMyI,QAAA,GAAW,KAAKjD,QAAA,CAASxF,IAAI;IAEnC,KAAKwB,YAAA,CAAa,oBAAoBc,IAAI;IAC1C,KAAKd,YAAA,CAAa,qBAAqBiH,QAAQ;IAE/C,MAAM1B,KAAA,GAAQ,EAAE;IAChB,SAAStG,CAAA,GAAI,GAAGA,CAAA,GAAIgI,QAAA,EAAU,EAAEhI,CAAA,EAAG;MACjCsG,KAAA,CAAMnE,IAAA,CAAK,KAAK4C,QAAA,CAASxF,IAAI,CAAC;IAC/B;IAED,OAAO;MAAEsC,IAAA;MAAYyE;IAAc;EACpC;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDhD,UAAU/D,IAAA,EAAM;IACd,MAAMa,KAAA,GAAQ,KAAKC,SAAA,CAAUd,IAAI;IACjC,MAAM8D,KAAA,GAAQ,IAAI4E,KAAA,CAAO;IAEzB,IAAI7H,KAAA,CAAME,EAAA,KAAO4H,QAAA,IAAY9H,KAAA,CAAME,EAAA,KAAO6H,YAAA,EAAc;MACtD,MAAMC,CAAA,GAAI,KAAKxF,QAAA,CAASrD,IAAI;MAC5B,MAAM8I,CAAA,GAAI,KAAKzF,QAAA,CAASrD,IAAI;MAC5B,MAAM+I,CAAA,GAAI,KAAK1F,QAAA,CAASrD,IAAI;MAE5B8D,KAAA,CAAMkF,MAAA,CAAOH,CAAA,GAAI,KAAKC,CAAA,GAAI,KAAKC,CAAA,GAAI,GAAG;MAEtC,KAAKvH,YAAA,CAAa,kBAAkBsC,KAAA,CAAM+E,CAAA,GAAI,OAAO/E,KAAA,CAAMgF,CAAA,GAAI,OAAOhF,KAAA,CAAMiF,CAAC;IACnF,WAAelI,KAAA,CAAME,EAAA,KAAOkI,OAAA,IAAWpI,KAAA,CAAME,EAAA,KAAOmI,WAAA,EAAa;MAC3D,MAAML,CAAA,GAAI,KAAK7G,SAAA,CAAUhC,IAAI;MAC7B,MAAM8I,CAAA,GAAI,KAAK9G,SAAA,CAAUhC,IAAI;MAC7B,MAAM+I,CAAA,GAAI,KAAK/G,SAAA,CAAUhC,IAAI;MAE7B8D,KAAA,CAAMkF,MAAA,CAAOH,CAAA,EAAGC,CAAA,EAAGC,CAAC;MAEpB,KAAKvH,YAAA,CAAa,kBAAkBsC,KAAA,CAAM+E,CAAA,GAAI,OAAO/E,KAAA,CAAMgF,CAAA,GAAI,OAAOhF,KAAA,CAAMiF,CAAC;IACnF,OAAW;MACL,KAAKvH,YAAA,CAAa,gCAAgCX,KAAA,CAAMe,QAAA,CAAS,EAAE,CAAC;IACrE;IAED,KAAKiB,QAAA,CAAShC,KAAK;IACnB,OAAOiD,KAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDhD,UAAUd,IAAA,EAAM;IACd,MAAMa,KAAA,GAAQ,CAAE;IAEhBA,KAAA,CAAM2B,GAAA,GAAM,KAAK5D,QAAA;IACjBiC,KAAA,CAAME,EAAA,GAAK,KAAKyE,QAAA,CAASxF,IAAI;IAC7Ba,KAAA,CAAMsI,IAAA,GAAO,KAAK5H,SAAA,CAAUvB,IAAI;IAChCa,KAAA,CAAMsG,GAAA,GAAMtG,KAAA,CAAM2B,GAAA,GAAM3B,KAAA,CAAMsI,IAAA;IAC9BtI,KAAA,CAAM2B,GAAA,IAAO;IAEb,OAAO3B,KAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAQDgC,SAAShC,KAAA,EAAO;IACd,KAAKjC,QAAA,GAAWiC,KAAA,CAAMsG,GAAA;EACvB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASD/F,UAAUpB,IAAA,EAAMa,KAAA,EAAO;IACrB,IAAIA,KAAA,CAAM2B,GAAA,IAAO3B,KAAA,CAAMsG,GAAA,EAAK;MAC1B,OAAO;IACR;IAED,KAAKvI,QAAA,GAAWiC,KAAA,CAAM2B,GAAA;IAEtB,IAAI;MACF,MAAMrB,IAAA,GAAO,KAAKL,SAAA,CAAUd,IAAI;MAChCa,KAAA,CAAM2B,GAAA,IAAOrB,IAAA,CAAKgI,IAAA;MAClB,OAAOhI,IAAA,CAAKJ,EAAA;IACb,SAAQb,CAAA,EAAP;MACA,KAAKsB,YAAA,CAAa,6BAA6B,KAAK5C,QAAQ;MAC5D,OAAO;IACR;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;EAOD8C,cAAA,EAAgB;IACd,KAAK9C,QAAA,IAAY;EAClB;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDyE,SAASrD,IAAA,EAAM;IACb,MAAMoJ,CAAA,GAAIpJ,IAAA,CAAKqJ,QAAA,CAAS,KAAKzK,QAAA,EAAU,IAAI;IAC3C,KAAKA,QAAA,IAAY;IACjB,OAAOwK,CAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDpH,UAAUhC,IAAA,EAAM;IACd,IAAI;MACF,MAAMoJ,CAAA,GAAIpJ,IAAA,CAAKsJ,UAAA,CAAW,KAAK1K,QAAA,EAAU,IAAI;MAC7C,KAAKA,QAAA,IAAY;MACjB,OAAOwK,CAAA;IACR,SAAQlJ,CAAA,EAAP;MACA,KAAKsB,YAAA,CAAatB,CAAA,GAAI,MAAM,KAAKtB,QAAA,GAAW,MAAMoB,IAAA,CAAKuJ,UAAU;IAClE;EACF;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDC,QAAQxJ,IAAA,EAAM;IACZ,MAAMoJ,CAAA,GAAIpJ,IAAA,CAAKyJ,QAAA,CAAS,KAAK7K,QAAA,EAAU,IAAI;IAC3C,KAAKA,QAAA,IAAY;IACjB,OAAOwK,CAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASDM,UAAU1J,IAAA,EAAM;IACd,MAAMoJ,CAAA,GAAIpJ,IAAA,CAAK2J,QAAA,CAAS,KAAK/K,QAAA,EAAU,IAAI;IAC3C,KAAKA,QAAA,IAAY;IACjB,OAAOwK,CAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASD7H,UAAUvB,IAAA,EAAM;IACd,MAAMoJ,CAAA,GAAIpJ,IAAA,CAAK4J,SAAA,CAAU,KAAKhL,QAAA,EAAU,IAAI;IAC5C,KAAKA,QAAA,IAAY;IACjB,OAAOwK,CAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASD5D,SAASxF,IAAA,EAAM;IACb,MAAMoJ,CAAA,GAAIpJ,IAAA,CAAK6J,SAAA,CAAU,KAAKjL,QAAA,EAAU,IAAI;IAC5C,KAAKA,QAAA,IAAY;IACjB,OAAOwK,CAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUD7G,WAAWvC,IAAA,EAAM8J,SAAA,EAAW;IAC1B,IAAIC,CAAA,GAAI;IAER,SAAStJ,CAAA,GAAI,GAAGA,CAAA,GAAIqJ,SAAA,EAAWrJ,CAAA,IAAK;MAClC,MAAMuJ,CAAA,GAAI,KAAK3G,QAAA,CAASrD,IAAI;MAC5B,IAAI,CAACgK,CAAA,EAAG;QACN;MACD;MAEDD,CAAA,IAAKE,MAAA,CAAOC,YAAA,CAAaF,CAAC;IAC3B;IAED,OAAOD,CAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASD1F,eAAerE,IAAA,EAAM;IACnB,MAAMa,KAAA,GAAQ,KAAKC,SAAA,CAAUd,IAAI;IACjC,IAAIoD,KAAA;IAEJ,QAAQvC,KAAA,CAAME,EAAA;MACZ,KAAKoJ,cAAA;QACH/G,KAAA,GAAQ,KAAKsG,SAAA,CAAU1J,IAAI,IAAI;QAC/B;MAEF,KAAKoK,gBAAA;QACHhH,KAAA,GAAQ,KAAKpB,SAAA,CAAUhC,IAAI;QAC3B;MAEF;QACE,KAAKwB,YAAA,CAAa,qCAAqCX,KAAA,CAAMe,QAAA,CAAS,EAAE,CAAC;IAC5E;IAED,KAAKiB,QAAA,CAAShC,KAAK;IAEnB,OAAOuC,KAAA;EACR;EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUD5B,aAAa6I,OAAA,EAAS;IACpB,IAAI,KAAK3L,KAAA,EAAO;MACdyB,OAAA,CAAQmK,GAAA,CAAID,OAAO;IACpB;EACF;AACH;AAGA,MAAMnJ,QAAA,GAAW;AAGjB,MAAMF,SAAA,GAAY;AAElB,MAAMC,MAAA,GAAS;AACf,MAAMI,WAAA,GAAc;AAEpB,MAAM4H,OAAA,GAAU;AAChB,MAAMN,QAAA,GAAW;AACjB,MAAMC,YAAA,GAAe;AACrB,MAAMM,WAAA,GAAc;AACpB,MAAMiB,cAAA,GAAiB;AACvB,MAAMC,gBAAA,GAAmB;AACzB,MAAM3I,KAAA,GAAQ;AACd,MAAMI,YAAA,GAAe;AACrB,MAAMC,YAAA,GAAe;AAwBrB,MAAMM,SAAA,GAAY;AAClB,MAAMY,QAAA,GAAW;AACjB,MAAMkB,WAAA,GAAc;AACpB,MAAML,WAAA,GAAc;AACpB,MAAMG,YAAA,GAAe;AACrB,MAAMG,aAAA,GAAgB;AAEtB,MAAMG,gBAAA,GAAmB;AAOzB,MAAMf,YAAA,GAAe;AAErB,MAAMG,YAAA,GAAe;AACrB,MAAMT,QAAA,GAAW;AAKjB,MAAME,aAAA,GAAgB;AACtB,MAAMuB,UAAA,GAAa;AAQnB,MAAMK,WAAA,GAAc;AAIpB,MAAMF,WAAA,GAAc;AAIpB,MAAMI,WAAA,GAAc;AAgBpB,MAAM+C,WAAA,GAAc;AAGpB,MAAMM,cAAA,GAAiB;AACvB,MAAME,cAAA,GAAiB;AACvB,MAAMP,eAAA,GAAkB;AACxB,MAAMG,eAAA,GAAkB;AAOxB,MAAMlG,YAAA,GAAe;AA8BrB,MAAMO,YAAA,GAAe;AACrB,MAAM6C,WAAA,GAAc;AAEpB,MAAMM,UAAA,GAAa;AACnB,MAAMyB,aAAA,GAAgB;AAGtB,MAAMvB,SAAA,GAAY;AAClB,MAAMG,WAAA,GAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}