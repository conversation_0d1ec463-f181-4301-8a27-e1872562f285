{"ast": null, "code": "const ARButton = {\n  createButton(renderer, sessionInit = {}) {\n    const button = document.createElement(\"button\");\n    function showStartAR() {\n      if (sessionInit.domOverlay === void 0) {\n        const overlay = document.createElement(\"div\");\n        overlay.style.display = \"none\";\n        document.body.appendChild(overlay);\n        const svg = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n        svg.setAttribute(\"width\", \"38px\");\n        svg.setAttribute(\"height\", \"38px\");\n        svg.style.position = \"absolute\";\n        svg.style.right = \"20px\";\n        svg.style.top = \"20px\";\n        svg.addEventListener(\"click\", function () {\n          currentSession == null ? void 0 : currentSession.end();\n        });\n        overlay.appendChild(svg);\n        const path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n        path.setAttribute(\"d\", \"M 12,12 L 28,28 M 28,12 12,28\");\n        path.setAttribute(\"stroke\", \"#fff\");\n        path.setAttribute(\"stroke-width\", \"2px\");\n        svg.appendChild(path);\n        if (sessionInit.optionalFeatures === void 0) {\n          sessionInit.optionalFeatures = [];\n        }\n        sessionInit.optionalFeatures.push(\"dom-overlay\");\n        sessionInit.domOverlay = {\n          root: overlay\n        };\n      }\n      let currentSession = null;\n      async function onSessionStarted(session) {\n        session.addEventListener(\"end\", onSessionEnded);\n        renderer.xr.setReferenceSpaceType(\"local\");\n        await renderer.xr.setSession(session);\n        button.textContent = \"STOP AR\";\n        sessionInit.domOverlay.root.style.display = \"\";\n        currentSession = session;\n      }\n      function onSessionEnded() {\n        currentSession.removeEventListener(\"end\", onSessionEnded);\n        button.textContent = \"START AR\";\n        sessionInit.domOverlay.root.style.display = \"none\";\n        currentSession = null;\n      }\n      button.style.display = \"\";\n      button.style.cursor = \"pointer\";\n      button.style.left = \"calc(50% - 50px)\";\n      button.style.width = \"100px\";\n      button.textContent = \"START AR\";\n      button.onmouseenter = () => {\n        button.style.opacity = \"1.0\";\n      };\n      button.onmouseleave = () => {\n        button.style.opacity = \"0.5\";\n      };\n      button.onclick = () => {\n        if (currentSession === null) {\n          navigator.xr.requestSession(\"immersive-ar\", sessionInit).then(onSessionStarted);\n        } else {\n          currentSession.end();\n        }\n      };\n    }\n    function disableButton() {\n      button.style.display = \"\";\n      button.style.cursor = \"auto\";\n      button.style.left = \"calc(50% - 75px)\";\n      button.style.width = \"150px\";\n      button.onmouseenter = null;\n      button.onmouseleave = null;\n      button.onclick = null;\n    }\n    function showARNotSupported() {\n      disableButton();\n      button.textContent = \"AR NOT SUPPORTED\";\n    }\n    function stylizeElement(element) {\n      element.style.position = \"absolute\";\n      element.style.bottom = \"20px\";\n      element.style.padding = \"12px 6px\";\n      element.style.border = \"1px solid #fff\";\n      element.style.borderRadius = \"4px\";\n      element.style.background = \"rgba(0,0,0,0.1)\";\n      element.style.color = \"#fff\";\n      element.style.font = \"normal 13px sans-serif\";\n      element.style.textAlign = \"center\";\n      element.style.opacity = \"0.5\";\n      element.style.outline = \"none\";\n      element.style.zIndex = \"999\";\n    }\n    if (\"xr\" in navigator) {\n      button.id = \"ARButton\";\n      button.style.display = \"none\";\n      stylizeElement(button);\n      navigator.xr.isSessionSupported(\"immersive-ar\").then(function (supported) {\n        supported ? showStartAR() : showARNotSupported();\n      }).catch(showARNotSupported);\n      return button;\n    } else {\n      const message = document.createElement(\"a\");\n      if (window.isSecureContext === false) {\n        message.href = document.location.href.replace(/^http:/, \"https:\");\n        message.innerHTML = \"WEBXR NEEDS HTTPS\";\n      } else {\n        message.href = \"https://immersiveweb.dev/\";\n        message.innerHTML = \"WEBXR NOT AVAILABLE\";\n      }\n      message.style.left = \"calc(50% - 90px)\";\n      message.style.width = \"180px\";\n      message.style.textDecoration = \"none\";\n      stylizeElement(message);\n      return message;\n    }\n  }\n};\nexport { ARButton };", "map": {"version": 3, "names": ["ARButton", "createButton", "renderer", "sessionInit", "button", "document", "createElement", "showStartAR", "dom<PERSON><PERSON><PERSON>", "overlay", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "svg", "createElementNS", "setAttribute", "position", "right", "top", "addEventListener", "currentSession", "end", "path", "optionalFeatures", "push", "root", "onSessionStarted", "session", "onSessionEnded", "xr", "setReferenceSpaceType", "setSession", "textContent", "removeEventListener", "cursor", "left", "width", "onmouseenter", "opacity", "onmouseleave", "onclick", "navigator", "requestSession", "then", "disable<PERSON><PERSON><PERSON>", "showARNotSupported", "stylizeElement", "element", "bottom", "padding", "border", "borderRadius", "background", "color", "font", "textAlign", "outline", "zIndex", "id", "isSessionSupported", "supported", "catch", "message", "window", "isSecureContext", "href", "location", "replace", "innerHTML", "textDecoration"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\webxr\\ARButton.ts"], "sourcesContent": ["import { WebGLRenderer } from 'three'\n\nconst ARButton = {\n  createButton(renderer: WebG<PERSON>enderer, sessionInit: XRSessionInit = {}): HTMLButtonElement | HTMLAnchorElement {\n    const button = document.createElement('button')\n\n    function showStartAR(/*device*/): void {\n      if ((sessionInit as any).domOverlay === undefined) {\n        const overlay = document.createElement('div')\n        overlay.style.display = 'none'\n        document.body.appendChild(overlay)\n\n        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')\n        svg.setAttribute('width', '38px')\n        svg.setAttribute('height', '38px')\n        svg.style.position = 'absolute'\n        svg.style.right = '20px'\n        svg.style.top = '20px'\n        svg.addEventListener('click', function () {\n          currentSession?.end()\n        })\n        overlay.appendChild(svg)\n\n        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path')\n        path.setAttribute('d', 'M 12,12 L 28,28 M 28,12 12,28')\n        path.setAttribute('stroke', '#fff')\n        path.setAttribute('stroke-width', '2px')\n        svg.appendChild(path)\n\n        if (sessionInit.optionalFeatures === undefined) {\n          sessionInit.optionalFeatures = []\n        }\n\n        sessionInit.optionalFeatures.push('dom-overlay')\n        ;(sessionInit as any).domOverlay = { root: overlay }\n      }\n\n      //\n\n      let currentSession: XRSession | null = null\n\n      async function onSessionStarted(session: XRSession): Promise<void> {\n        session.addEventListener('end', onSessionEnded)\n\n        renderer.xr.setReferenceSpaceType('local')\n\n        await renderer.xr.setSession(session as any)\n\n        button.textContent = 'STOP AR'\n        ;(sessionInit as any).domOverlay!.root.style.display = ''\n\n        currentSession = session\n      }\n\n      function onSessionEnded(/*event*/): void {\n        currentSession!.removeEventListener('end', onSessionEnded)\n\n        button.textContent = 'START AR'\n        ;(sessionInit as any).domOverlay!.root.style.display = 'none'\n\n        currentSession = null\n      }\n\n      //\n\n      button.style.display = ''\n\n      button.style.cursor = 'pointer'\n      button.style.left = 'calc(50% - 50px)'\n      button.style.width = '100px'\n\n      button.textContent = 'START AR'\n\n      button.onmouseenter = (): void => {\n        button.style.opacity = '1.0'\n      }\n\n      button.onmouseleave = (): void => {\n        button.style.opacity = '0.5'\n      }\n\n      button.onclick = (): void => {\n        if (currentSession === null) {\n          ;(navigator as Navigator).xr!.requestSession('immersive-ar', sessionInit).then(onSessionStarted)\n        } else {\n          currentSession.end()\n        }\n      }\n    }\n\n    function disableButton(): void {\n      button.style.display = ''\n\n      button.style.cursor = 'auto'\n      button.style.left = 'calc(50% - 75px)'\n      button.style.width = '150px'\n\n      button.onmouseenter = null\n      button.onmouseleave = null\n\n      button.onclick = null\n    }\n\n    function showARNotSupported(): void {\n      disableButton()\n\n      button.textContent = 'AR NOT SUPPORTED'\n    }\n\n    function stylizeElement(element: HTMLElement): void {\n      element.style.position = 'absolute'\n      element.style.bottom = '20px'\n      element.style.padding = '12px 6px'\n      element.style.border = '1px solid #fff'\n      element.style.borderRadius = '4px'\n      element.style.background = 'rgba(0,0,0,0.1)'\n      element.style.color = '#fff'\n      element.style.font = 'normal 13px sans-serif'\n      element.style.textAlign = 'center'\n      element.style.opacity = '0.5'\n      element.style.outline = 'none'\n      element.style.zIndex = '999'\n    }\n\n    if ('xr' in navigator) {\n      button.id = 'ARButton'\n      button.style.display = 'none'\n\n      stylizeElement(button)\n\n      // Query for session mode\n      ;(navigator as Navigator)\n        .xr!.isSessionSupported('immersive-ar')\n        .then(function (supported: boolean) {\n          supported ? showStartAR() : showARNotSupported()\n        })\n        .catch(showARNotSupported)\n\n      return button\n    } else {\n      const message = document.createElement('a')\n\n      if (window.isSecureContext === false) {\n        message.href = document.location.href.replace(/^http:/, 'https:')\n        message.innerHTML = 'WEBXR NEEDS HTTPS' // TODO Improve message\n      } else {\n        message.href = 'https://immersiveweb.dev/'\n        message.innerHTML = 'WEBXR NOT AVAILABLE'\n      }\n\n      message.style.left = 'calc(50% - 90px)'\n      message.style.width = '180px'\n      message.style.textDecoration = 'none'\n\n      stylizeElement(message)\n\n      return message\n    }\n  },\n}\n\nexport { ARButton }\n"], "mappings": "AAEA,MAAMA,QAAA,GAAW;EACfC,aAAaC,QAAA,EAAyBC,WAAA,GAA6B,IAA2C;IACtG,MAAAC,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;IAE9C,SAASC,YAAA,EAA8B;MAChC,IAAAJ,WAAA,CAAoBK,UAAA,KAAe,QAAW;QAC3C,MAAAC,OAAA,GAAUJ,QAAA,CAASC,aAAA,CAAc,KAAK;QAC5CG,OAAA,CAAQC,KAAA,CAAMC,OAAA,GAAU;QACfN,QAAA,CAAAO,IAAA,CAAKC,WAAA,CAAYJ,OAAO;QAEjC,MAAMK,GAAA,GAAMT,QAAA,CAASU,eAAA,CAAgB,8BAA8B,KAAK;QACpED,GAAA,CAAAE,YAAA,CAAa,SAAS,MAAM;QAC5BF,GAAA,CAAAE,YAAA,CAAa,UAAU,MAAM;QACjCF,GAAA,CAAIJ,KAAA,CAAMO,QAAA,GAAW;QACrBH,GAAA,CAAIJ,KAAA,CAAMQ,KAAA,GAAQ;QAClBJ,GAAA,CAAIJ,KAAA,CAAMS,GAAA,GAAM;QACZL,GAAA,CAAAM,gBAAA,CAAiB,SAAS,YAAY;UACxCC,cAAA,oBAAAA,cAAA,CAAgBC,GAAA;QAAI,CACrB;QACDb,OAAA,CAAQI,WAAA,CAAYC,GAAG;QAEvB,MAAMS,IAAA,GAAOlB,QAAA,CAASU,eAAA,CAAgB,8BAA8B,MAAM;QACrEQ,IAAA,CAAAP,YAAA,CAAa,KAAK,+BAA+B;QACjDO,IAAA,CAAAP,YAAA,CAAa,UAAU,MAAM;QAC7BO,IAAA,CAAAP,YAAA,CAAa,gBAAgB,KAAK;QACvCF,GAAA,CAAID,WAAA,CAAYU,IAAI;QAEhB,IAAApB,WAAA,CAAYqB,gBAAA,KAAqB,QAAW;UAC9CrB,WAAA,CAAYqB,gBAAA,GAAmB;QACjC;QAEYrB,WAAA,CAAAqB,gBAAA,CAAiBC,IAAA,CAAK,aAAa;QAC7CtB,WAAA,CAAoBK,UAAA,GAAa;UAAEkB,IAAA,EAAMjB;QAAQ;MACrD;MAIA,IAAIY,cAAA,GAAmC;MAEvC,eAAeM,iBAAiBC,OAAA,EAAmC;QACzDA,OAAA,CAAAR,gBAAA,CAAiB,OAAOS,cAAc;QAErC3B,QAAA,CAAA4B,EAAA,CAAGC,qBAAA,CAAsB,OAAO;QAEnC,MAAA7B,QAAA,CAAS4B,EAAA,CAAGE,UAAA,CAAWJ,OAAc;QAE3CxB,MAAA,CAAO6B,WAAA,GAAc;QACnB9B,WAAA,CAAoBK,UAAA,CAAYkB,IAAA,CAAKhB,KAAA,CAAMC,OAAA,GAAU;QAEtCU,cAAA,GAAAO,OAAA;MACnB;MAEA,SAASC,eAAA,EAAgC;QACvBR,cAAA,CAAAa,mBAAA,CAAoB,OAAOL,cAAc;QAEzDzB,MAAA,CAAO6B,WAAA,GAAc;QACnB9B,WAAA,CAAoBK,UAAA,CAAYkB,IAAA,CAAKhB,KAAA,CAAMC,OAAA,GAAU;QAEtCU,cAAA;MACnB;MAIAjB,MAAA,CAAOM,KAAA,CAAMC,OAAA,GAAU;MAEvBP,MAAA,CAAOM,KAAA,CAAMyB,MAAA,GAAS;MACtB/B,MAAA,CAAOM,KAAA,CAAM0B,IAAA,GAAO;MACpBhC,MAAA,CAAOM,KAAA,CAAM2B,KAAA,GAAQ;MAErBjC,MAAA,CAAO6B,WAAA,GAAc;MAErB7B,MAAA,CAAOkC,YAAA,GAAe,MAAY;QAChClC,MAAA,CAAOM,KAAA,CAAM6B,OAAA,GAAU;MAAA;MAGzBnC,MAAA,CAAOoC,YAAA,GAAe,MAAY;QAChCpC,MAAA,CAAOM,KAAA,CAAM6B,OAAA,GAAU;MAAA;MAGzBnC,MAAA,CAAOqC,OAAA,GAAU,MAAY;QAC3B,IAAIpB,cAAA,KAAmB,MAAM;UACzBqB,SAAA,CAAwBZ,EAAA,CAAIa,cAAA,CAAe,gBAAgBxC,WAAW,EAAEyC,IAAA,CAAKjB,gBAAgB;QAAA,OAC1F;UACLN,cAAA,CAAeC,GAAA,CAAI;QACrB;MAAA;IAEJ;IAEA,SAASuB,cAAA,EAAsB;MAC7BzC,MAAA,CAAOM,KAAA,CAAMC,OAAA,GAAU;MAEvBP,MAAA,CAAOM,KAAA,CAAMyB,MAAA,GAAS;MACtB/B,MAAA,CAAOM,KAAA,CAAM0B,IAAA,GAAO;MACpBhC,MAAA,CAAOM,KAAA,CAAM2B,KAAA,GAAQ;MAErBjC,MAAA,CAAOkC,YAAA,GAAe;MACtBlC,MAAA,CAAOoC,YAAA,GAAe;MAEtBpC,MAAA,CAAOqC,OAAA,GAAU;IACnB;IAEA,SAASK,mBAAA,EAA2B;MACpBD,aAAA;MAEdzC,MAAA,CAAO6B,WAAA,GAAc;IACvB;IAEA,SAASc,eAAeC,OAAA,EAA4B;MAClDA,OAAA,CAAQtC,KAAA,CAAMO,QAAA,GAAW;MACzB+B,OAAA,CAAQtC,KAAA,CAAMuC,MAAA,GAAS;MACvBD,OAAA,CAAQtC,KAAA,CAAMwC,OAAA,GAAU;MACxBF,OAAA,CAAQtC,KAAA,CAAMyC,MAAA,GAAS;MACvBH,OAAA,CAAQtC,KAAA,CAAM0C,YAAA,GAAe;MAC7BJ,OAAA,CAAQtC,KAAA,CAAM2C,UAAA,GAAa;MAC3BL,OAAA,CAAQtC,KAAA,CAAM4C,KAAA,GAAQ;MACtBN,OAAA,CAAQtC,KAAA,CAAM6C,IAAA,GAAO;MACrBP,OAAA,CAAQtC,KAAA,CAAM8C,SAAA,GAAY;MAC1BR,OAAA,CAAQtC,KAAA,CAAM6B,OAAA,GAAU;MACxBS,OAAA,CAAQtC,KAAA,CAAM+C,OAAA,GAAU;MACxBT,OAAA,CAAQtC,KAAA,CAAMgD,MAAA,GAAS;IACzB;IAEA,IAAI,QAAQhB,SAAA,EAAW;MACrBtC,MAAA,CAAOuD,EAAA,GAAK;MACZvD,MAAA,CAAOM,KAAA,CAAMC,OAAA,GAAU;MAEvBoC,cAAA,CAAe3C,MAAM;MAGnBsC,SAAA,CACCZ,EAAA,CAAI8B,kBAAA,CAAmB,cAAc,EACrChB,IAAA,CAAK,UAAUiB,SAAA,EAAoB;QACtBA,SAAA,GAAAtD,WAAA,KAAgBuC,kBAAA;MAAmB,CAChD,EACAgB,KAAA,CAAMhB,kBAAkB;MAEpB,OAAA1C,MAAA;IAAA,OACF;MACC,MAAA2D,OAAA,GAAU1D,QAAA,CAASC,aAAA,CAAc,GAAG;MAEtC,IAAA0D,MAAA,CAAOC,eAAA,KAAoB,OAAO;QACpCF,OAAA,CAAQG,IAAA,GAAO7D,QAAA,CAAS8D,QAAA,CAASD,IAAA,CAAKE,OAAA,CAAQ,UAAU,QAAQ;QAChEL,OAAA,CAAQM,SAAA,GAAY;MAAA,OACf;QACLN,OAAA,CAAQG,IAAA,GAAO;QACfH,OAAA,CAAQM,SAAA,GAAY;MACtB;MAEAN,OAAA,CAAQrD,KAAA,CAAM0B,IAAA,GAAO;MACrB2B,OAAA,CAAQrD,KAAA,CAAM2B,KAAA,GAAQ;MACtB0B,OAAA,CAAQrD,KAAA,CAAM4D,cAAA,GAAiB;MAE/BvB,cAAA,CAAegB,OAAO;MAEf,OAAAA,OAAA;IACT;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}