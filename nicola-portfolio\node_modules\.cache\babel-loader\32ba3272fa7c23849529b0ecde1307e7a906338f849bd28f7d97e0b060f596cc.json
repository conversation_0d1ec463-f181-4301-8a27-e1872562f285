{"ast": null, "code": "import { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { PrimitivePool } from './PrimitivePool.js';\nclass ExtendedTrianglePoolBase extends PrimitivePool {\n  constructor() {\n    super(() => new ExtendedTriangle());\n  }\n}\nexport const ExtendedTrianglePool = /* @__PURE__ */new ExtendedTrianglePoolBase();", "map": {"version": 3, "names": ["ExtendedTriangle", "PrimitivePool", "ExtendedTrianglePoolBase", "constructor", "ExtendedTrianglePool"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/node_modules/three-mesh-bvh/src/utils/ExtendedTrianglePool.js"], "sourcesContent": ["import { ExtendedTriangle } from '../math/ExtendedTriangle.js';\nimport { PrimitivePool } from './PrimitivePool.js';\n\nclass ExtendedTrianglePoolBase extends PrimitivePool {\n\n\tconstructor() {\n\n\t\tsuper( () => new ExtendedTriangle() );\n\n\t}\n\n}\n\nexport const ExtendedTrianglePool = /* @__PURE__ */ new ExtendedTrianglePoolBase();\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,aAAa,QAAQ,oBAAoB;AAElD,MAAMC,wBAAwB,SAASD,aAAa,CAAC;EAEpDE,WAAWA,CAAA,EAAG;IAEb,KAAK,CAAE,MAAM,IAAIH,gBAAgB,CAAC,CAAE,CAAC;EAEtC;AAED;AAEA,OAAO,MAAMI,oBAAoB,GAAG,eAAgB,IAAIF,wBAAwB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}