{"ast": null, "code": "import { Line, BufferGeometry, BufferAttribute, LineBasicMaterial, MathUtils } from \"three\";\nclass PositionalAudioHelper extends Line {\n  constructor(audio, range = 1, divisionsInnerAngle = 16, divisionsOuterAngle = 2) {\n    const geometry = new BufferGeometry();\n    const divisions = divisionsInnerAngle + divisionsOuterAngle * 2;\n    const positions = new Float32Array((divisions * 3 + 3) * 3);\n    geometry.setAttribute(\"position\", new BufferAttribute(positions, 3));\n    const materialInnerAngle = new LineBasicMaterial({\n      color: 65280\n    });\n    const materialOuterAngle = new LineBasicMaterial({\n      color: 16776960\n    });\n    super(geometry, [materialOuterAngle, materialInnerAngle]);\n    this.type = \"PositionalAudioHelper\";\n    this.audio = audio;\n    this.range = range;\n    this.divisionsInnerAngle = divisionsInnerAngle;\n    this.divisionsOuterAngle = divisionsOuterAngle;\n    this.update();\n  }\n  update() {\n    const audio = this.audio;\n    const range = this.range;\n    const divisionsInnerAngle = this.divisionsInnerAngle;\n    const divisionsOuterAngle = this.divisionsOuterAngle;\n    const coneInnerAngle = MathUtils.degToRad(audio.panner.coneInnerAngle);\n    const coneOuterAngle = MathUtils.degToRad(audio.panner.coneOuterAngle);\n    const halfConeInnerAngle = coneInnerAngle / 2;\n    const halfConeOuterAngle = coneOuterAngle / 2;\n    let start = 0;\n    let count = 0;\n    let i, stride;\n    const geometry = this.geometry;\n    const positionAttribute = geometry.attributes.position;\n    geometry.clearGroups();\n    function generateSegment(from, to, divisions, materialIndex) {\n      const step = (to - from) / divisions;\n      positionAttribute.setXYZ(start, 0, 0, 0);\n      count++;\n      for (i = from; i < to; i += step) {\n        stride = start + count;\n        positionAttribute.setXYZ(stride, Math.sin(i) * range, 0, Math.cos(i) * range);\n        positionAttribute.setXYZ(stride + 1, Math.sin(Math.min(i + step, to)) * range, 0, Math.cos(Math.min(i + step, to)) * range);\n        positionAttribute.setXYZ(stride + 2, 0, 0, 0);\n        count += 3;\n      }\n      geometry.addGroup(start, count, materialIndex);\n      start += count;\n      count = 0;\n    }\n    generateSegment(-halfConeOuterAngle, -halfConeInnerAngle, divisionsOuterAngle, 0);\n    generateSegment(-halfConeInnerAngle, halfConeInnerAngle, divisionsInnerAngle, 1);\n    generateSegment(halfConeInnerAngle, halfConeOuterAngle, divisionsOuterAngle, 0);\n    positionAttribute.needsUpdate = true;\n    if (coneInnerAngle === coneOuterAngle) this.material[0].visible = false;\n  }\n  dispose() {\n    this.geometry.dispose();\n    this.material[0].dispose();\n    this.material[1].dispose();\n  }\n}\nexport { PositionalAudioHelper };", "map": {"version": 3, "names": ["PositionalAudioHelper", "Line", "constructor", "audio", "range", "divisionsInnerAngle", "divisionsOuterAngle", "geometry", "BufferGeometry", "divisions", "positions", "Float32Array", "setAttribute", "BufferAttribute", "materialInnerAngle", "LineBasicMaterial", "color", "materialOuterAngle", "type", "update", "coneInnerAngle", "MathUtils", "degToRad", "panner", "coneOuterAngle", "halfConeInnerAngle", "halfConeOuterAngle", "start", "count", "i", "stride", "positionAttribute", "attributes", "position", "clearGroups", "generateSegment", "from", "to", "materialIndex", "step", "setXYZ", "Math", "sin", "cos", "min", "addGroup", "needsUpdate", "material", "visible", "dispose"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\helpers\\PositionalAudioHelper.js"], "sourcesContent": ["import { BufferGeo<PERSON>, BufferAttribute, LineBasicMaterial, Line, MathUtils } from 'three'\n\nclass PositionalAudioHelper extends Line {\n  constructor(audio, range = 1, divisionsInnerAngle = 16, divisionsOuterAngle = 2) {\n    const geometry = new BufferGeometry()\n    const divisions = divisionsInnerAngle + divisionsOuterAngle * 2\n    const positions = new Float32Array((divisions * 3 + 3) * 3)\n    geometry.setAttribute('position', new BufferAttribute(positions, 3))\n\n    const materialInnerAngle = new LineBasicMaterial({ color: 0x00ff00 })\n    const materialOuterAngle = new LineBasicMaterial({ color: 0xffff00 })\n\n    super(geometry, [materialOuterAngle, materialInnerAngle])\n\n    this.type = 'PositionalAudioHelper'\n    this.audio = audio\n    this.range = range\n    this.divisionsInnerAngle = divisionsInnerAngle\n    this.divisionsOuterAngle = divisionsOuterAngle\n\n    this.update()\n  }\n\n  update() {\n    const audio = this.audio\n    const range = this.range\n    const divisionsInnerAngle = this.divisionsInnerAngle\n    const divisionsOuterAngle = this.divisionsOuterAngle\n\n    const coneInnerAngle = MathUtils.degToRad(audio.panner.coneInnerAngle)\n    const coneOuterAngle = MathUtils.degToRad(audio.panner.coneOuterAngle)\n\n    const halfConeInnerAngle = coneInnerAngle / 2\n    const halfConeOuterAngle = coneOuterAngle / 2\n\n    let start = 0\n    let count = 0\n    let i, stride\n\n    const geometry = this.geometry\n    const positionAttribute = geometry.attributes.position\n\n    geometry.clearGroups()\n\n    //\n\n    function generateSegment(from, to, divisions, materialIndex) {\n      const step = (to - from) / divisions\n\n      positionAttribute.setXYZ(start, 0, 0, 0)\n      count++\n\n      for (i = from; i < to; i += step) {\n        stride = start + count\n\n        positionAttribute.setXYZ(stride, Math.sin(i) * range, 0, Math.cos(i) * range)\n        positionAttribute.setXYZ(\n          stride + 1,\n          Math.sin(Math.min(i + step, to)) * range,\n          0,\n          Math.cos(Math.min(i + step, to)) * range,\n        )\n        positionAttribute.setXYZ(stride + 2, 0, 0, 0)\n\n        count += 3\n      }\n\n      geometry.addGroup(start, count, materialIndex)\n\n      start += count\n      count = 0\n    }\n\n    //\n\n    generateSegment(-halfConeOuterAngle, -halfConeInnerAngle, divisionsOuterAngle, 0)\n    generateSegment(-halfConeInnerAngle, halfConeInnerAngle, divisionsInnerAngle, 1)\n    generateSegment(halfConeInnerAngle, halfConeOuterAngle, divisionsOuterAngle, 0)\n\n    //\n\n    positionAttribute.needsUpdate = true\n\n    if (coneInnerAngle === coneOuterAngle) this.material[0].visible = false\n  }\n\n  dispose() {\n    this.geometry.dispose()\n    this.material[0].dispose()\n    this.material[1].dispose()\n  }\n}\n\nexport { PositionalAudioHelper }\n"], "mappings": ";AAEA,MAAMA,qBAAA,SAA8BC,IAAA,CAAK;EACvCC,YAAYC,KAAA,EAAOC,KAAA,GAAQ,GAAGC,mBAAA,GAAsB,IAAIC,mBAAA,GAAsB,GAAG;IAC/E,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAgB;IACrC,MAAMC,SAAA,GAAYJ,mBAAA,GAAsBC,mBAAA,GAAsB;IAC9D,MAAMI,SAAA,GAAY,IAAIC,YAAA,EAAcF,SAAA,GAAY,IAAI,KAAK,CAAC;IAC1DF,QAAA,CAASK,YAAA,CAAa,YAAY,IAAIC,eAAA,CAAgBH,SAAA,EAAW,CAAC,CAAC;IAEnE,MAAMI,kBAAA,GAAqB,IAAIC,iBAAA,CAAkB;MAAEC,KAAA,EAAO;IAAQ,CAAE;IACpE,MAAMC,kBAAA,GAAqB,IAAIF,iBAAA,CAAkB;MAAEC,KAAA,EAAO;IAAQ,CAAE;IAEpE,MAAMT,QAAA,EAAU,CAACU,kBAAA,EAAoBH,kBAAkB,CAAC;IAExD,KAAKI,IAAA,GAAO;IACZ,KAAKf,KAAA,GAAQA,KAAA;IACb,KAAKC,KAAA,GAAQA,KAAA;IACb,KAAKC,mBAAA,GAAsBA,mBAAA;IAC3B,KAAKC,mBAAA,GAAsBA,mBAAA;IAE3B,KAAKa,MAAA,CAAQ;EACd;EAEDA,OAAA,EAAS;IACP,MAAMhB,KAAA,GAAQ,KAAKA,KAAA;IACnB,MAAMC,KAAA,GAAQ,KAAKA,KAAA;IACnB,MAAMC,mBAAA,GAAsB,KAAKA,mBAAA;IACjC,MAAMC,mBAAA,GAAsB,KAAKA,mBAAA;IAEjC,MAAMc,cAAA,GAAiBC,SAAA,CAAUC,QAAA,CAASnB,KAAA,CAAMoB,MAAA,CAAOH,cAAc;IACrE,MAAMI,cAAA,GAAiBH,SAAA,CAAUC,QAAA,CAASnB,KAAA,CAAMoB,MAAA,CAAOC,cAAc;IAErE,MAAMC,kBAAA,GAAqBL,cAAA,GAAiB;IAC5C,MAAMM,kBAAA,GAAqBF,cAAA,GAAiB;IAE5C,IAAIG,KAAA,GAAQ;IACZ,IAAIC,KAAA,GAAQ;IACZ,IAAIC,CAAA,EAAGC,MAAA;IAEP,MAAMvB,QAAA,GAAW,KAAKA,QAAA;IACtB,MAAMwB,iBAAA,GAAoBxB,QAAA,CAASyB,UAAA,CAAWC,QAAA;IAE9C1B,QAAA,CAAS2B,WAAA,CAAa;IAItB,SAASC,gBAAgBC,IAAA,EAAMC,EAAA,EAAI5B,SAAA,EAAW6B,aAAA,EAAe;MAC3D,MAAMC,IAAA,IAAQF,EAAA,GAAKD,IAAA,IAAQ3B,SAAA;MAE3BsB,iBAAA,CAAkBS,MAAA,CAAOb,KAAA,EAAO,GAAG,GAAG,CAAC;MACvCC,KAAA;MAEA,KAAKC,CAAA,GAAIO,IAAA,EAAMP,CAAA,GAAIQ,EAAA,EAAIR,CAAA,IAAKU,IAAA,EAAM;QAChCT,MAAA,GAASH,KAAA,GAAQC,KAAA;QAEjBG,iBAAA,CAAkBS,MAAA,CAAOV,MAAA,EAAQW,IAAA,CAAKC,GAAA,CAAIb,CAAC,IAAIzB,KAAA,EAAO,GAAGqC,IAAA,CAAKE,GAAA,CAAId,CAAC,IAAIzB,KAAK;QAC5E2B,iBAAA,CAAkBS,MAAA,CAChBV,MAAA,GAAS,GACTW,IAAA,CAAKC,GAAA,CAAID,IAAA,CAAKG,GAAA,CAAIf,CAAA,GAAIU,IAAA,EAAMF,EAAE,CAAC,IAAIjC,KAAA,EACnC,GACAqC,IAAA,CAAKE,GAAA,CAAIF,IAAA,CAAKG,GAAA,CAAIf,CAAA,GAAIU,IAAA,EAAMF,EAAE,CAAC,IAAIjC,KACpC;QACD2B,iBAAA,CAAkBS,MAAA,CAAOV,MAAA,GAAS,GAAG,GAAG,GAAG,CAAC;QAE5CF,KAAA,IAAS;MACV;MAEDrB,QAAA,CAASsC,QAAA,CAASlB,KAAA,EAAOC,KAAA,EAAOU,aAAa;MAE7CX,KAAA,IAASC,KAAA;MACTA,KAAA,GAAQ;IACT;IAIDO,eAAA,CAAgB,CAACT,kBAAA,EAAoB,CAACD,kBAAA,EAAoBnB,mBAAA,EAAqB,CAAC;IAChF6B,eAAA,CAAgB,CAACV,kBAAA,EAAoBA,kBAAA,EAAoBpB,mBAAA,EAAqB,CAAC;IAC/E8B,eAAA,CAAgBV,kBAAA,EAAoBC,kBAAA,EAAoBpB,mBAAA,EAAqB,CAAC;IAI9EyB,iBAAA,CAAkBe,WAAA,GAAc;IAEhC,IAAI1B,cAAA,KAAmBI,cAAA,EAAgB,KAAKuB,QAAA,CAAS,CAAC,EAAEC,OAAA,GAAU;EACnE;EAEDC,QAAA,EAAU;IACR,KAAK1C,QAAA,CAAS0C,OAAA,CAAS;IACvB,KAAKF,QAAA,CAAS,CAAC,EAAEE,OAAA,CAAS;IAC1B,KAAKF,QAAA,CAAS,CAAC,EAAEE,OAAA,CAAS;EAC3B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}