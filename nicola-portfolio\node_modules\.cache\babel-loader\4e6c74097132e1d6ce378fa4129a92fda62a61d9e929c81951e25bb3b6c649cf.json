{"ast": null, "code": "import { Vector3, Frustum } from \"three\";\nconst frustum = /* @__PURE__ */new Frustum();\nconst center = /* @__PURE__ */new Vector3();\nconst tmpPoint = /* @__PURE__ */new Vector3();\nconst vecNear = /* @__PURE__ */new Vector3();\nconst vecTopLeft = /* @__PURE__ */new Vector3();\nconst vecTopRight = /* @__PURE__ */new Vector3();\nconst vecDownRight = /* @__PURE__ */new Vector3();\nconst vecDownLeft = /* @__PURE__ */new Vector3();\nconst vecFarTopLeft = /* @__PURE__ */new Vector3();\nconst vecFarTopRight = /* @__PURE__ */new Vector3();\nconst vecFarDownRight = /* @__PURE__ */new Vector3();\nconst vecFarDownLeft = /* @__PURE__ */new Vector3();\nconst vectemp1 = /* @__PURE__ */new Vector3();\nconst vectemp2 = /* @__PURE__ */new Vector3();\nconst vectemp3 = /* @__PURE__ */new Vector3();\nclass SelectionBox {\n  constructor(camera, scene, deep) {\n    this.camera = camera;\n    this.scene = scene;\n    this.startPoint = new Vector3();\n    this.endPoint = new Vector3();\n    this.collection = [];\n    this.deep = deep || Number.MAX_VALUE;\n  }\n  select(startPoint, endPoint) {\n    this.startPoint = startPoint || this.startPoint;\n    this.endPoint = endPoint || this.endPoint;\n    this.collection = [];\n    this.updateFrustum(this.startPoint, this.endPoint);\n    this.searchChildInFrustum(frustum, this.scene);\n    return this.collection;\n  }\n  updateFrustum(startPoint, endPoint) {\n    startPoint = startPoint || this.startPoint;\n    endPoint = endPoint || this.endPoint;\n    if (startPoint.x === endPoint.x) {\n      endPoint.x += Number.EPSILON;\n    }\n    if (startPoint.y === endPoint.y) {\n      endPoint.y += Number.EPSILON;\n    }\n    this.camera.updateProjectionMatrix();\n    this.camera.updateMatrixWorld();\n    if (this.camera.isPerspectiveCamera) {\n      tmpPoint.copy(startPoint);\n      tmpPoint.x = Math.min(startPoint.x, endPoint.x);\n      tmpPoint.y = Math.max(startPoint.y, endPoint.y);\n      endPoint.x = Math.max(startPoint.x, endPoint.x);\n      endPoint.y = Math.min(startPoint.y, endPoint.y);\n      vecNear.setFromMatrixPosition(this.camera.matrixWorld);\n      vecTopLeft.copy(tmpPoint);\n      vecTopRight.set(endPoint.x, tmpPoint.y, 0);\n      vecDownRight.copy(endPoint);\n      vecDownLeft.set(tmpPoint.x, endPoint.y, 0);\n      vecTopLeft.unproject(this.camera);\n      vecTopRight.unproject(this.camera);\n      vecDownRight.unproject(this.camera);\n      vecDownLeft.unproject(this.camera);\n      vectemp1.copy(vecTopLeft).sub(vecNear);\n      vectemp2.copy(vecTopRight).sub(vecNear);\n      vectemp3.copy(vecDownRight).sub(vecNear);\n      vectemp1.normalize();\n      vectemp2.normalize();\n      vectemp3.normalize();\n      vectemp1.multiplyScalar(this.deep);\n      vectemp2.multiplyScalar(this.deep);\n      vectemp3.multiplyScalar(this.deep);\n      vectemp1.add(vecNear);\n      vectemp2.add(vecNear);\n      vectemp3.add(vecNear);\n      var planes = frustum.planes;\n      planes[0].setFromCoplanarPoints(vecNear, vecTopLeft, vecTopRight);\n      planes[1].setFromCoplanarPoints(vecNear, vecTopRight, vecDownRight);\n      planes[2].setFromCoplanarPoints(vecDownRight, vecDownLeft, vecNear);\n      planes[3].setFromCoplanarPoints(vecDownLeft, vecTopLeft, vecNear);\n      planes[4].setFromCoplanarPoints(vecTopRight, vecDownRight, vecDownLeft);\n      planes[5].setFromCoplanarPoints(vectemp3, vectemp2, vectemp1);\n      planes[5].normal.multiplyScalar(-1);\n    } else if (this.camera.isOrthographicCamera) {\n      const left = Math.min(startPoint.x, endPoint.x);\n      const top = Math.max(startPoint.y, endPoint.y);\n      const right = Math.max(startPoint.x, endPoint.x);\n      const down = Math.min(startPoint.y, endPoint.y);\n      vecTopLeft.set(left, top, -1);\n      vecTopRight.set(right, top, -1);\n      vecDownRight.set(right, down, -1);\n      vecDownLeft.set(left, down, -1);\n      vecFarTopLeft.set(left, top, 1);\n      vecFarTopRight.set(right, top, 1);\n      vecFarDownRight.set(right, down, 1);\n      vecFarDownLeft.set(left, down, 1);\n      vecTopLeft.unproject(this.camera);\n      vecTopRight.unproject(this.camera);\n      vecDownRight.unproject(this.camera);\n      vecDownLeft.unproject(this.camera);\n      vecFarTopLeft.unproject(this.camera);\n      vecFarTopRight.unproject(this.camera);\n      vecFarDownRight.unproject(this.camera);\n      vecFarDownLeft.unproject(this.camera);\n      var planes = frustum.planes;\n      planes[0].setFromCoplanarPoints(vecTopLeft, vecFarTopLeft, vecFarTopRight);\n      planes[1].setFromCoplanarPoints(vecTopRight, vecFarTopRight, vecFarDownRight);\n      planes[2].setFromCoplanarPoints(vecFarDownRight, vecFarDownLeft, vecDownLeft);\n      planes[3].setFromCoplanarPoints(vecFarDownLeft, vecFarTopLeft, vecTopLeft);\n      planes[4].setFromCoplanarPoints(vecTopRight, vecDownRight, vecDownLeft);\n      planes[5].setFromCoplanarPoints(vecFarDownRight, vecFarTopRight, vecFarTopLeft);\n      planes[5].normal.multiplyScalar(-1);\n    } else {\n      console.error(\"THREE.SelectionBox: Unsupported camera type.\");\n    }\n  }\n  searchChildInFrustum(frustum2, object) {\n    if (object.isMesh || object.isLine || object.isPoints) {\n      if (object.material !== void 0) {\n        if (object.geometry.boundingSphere === null) object.geometry.computeBoundingSphere();\n        center.copy(object.geometry.boundingSphere.center);\n        center.applyMatrix4(object.matrixWorld);\n        if (frustum2.containsPoint(center)) {\n          this.collection.push(object);\n        }\n      }\n    }\n    if (object.children.length > 0) {\n      for (let x = 0; x < object.children.length; x++) {\n        this.searchChildInFrustum(frustum2, object.children[x]);\n      }\n    }\n  }\n}\nexport { SelectionBox };", "map": {"version": 3, "names": ["frustum", "Frustum", "center", "Vector3", "tmpPoint", "vecNear", "vecTopLeft", "vecTopRight", "vecDownRight", "vecDownLeft", "vecFarTopLeft", "vecFarTopRight", "vecFarDownRight", "vecFarDownLeft", "vectemp1", "vectemp2", "vectemp3", "SelectionBox", "constructor", "camera", "scene", "deep", "startPoint", "endPoint", "collection", "Number", "MAX_VALUE", "select", "updateFrustum", "searchChildInFrustum", "x", "EPSILON", "y", "updateProjectionMatrix", "updateMatrixWorld", "isPerspectiveCamera", "copy", "Math", "min", "max", "setFromMatrixPosition", "matrixWorld", "set", "unproject", "sub", "normalize", "multiplyScalar", "add", "planes", "setFromCoplanarPoints", "normal", "isOrthographicCamera", "left", "top", "right", "down", "console", "error", "frustum2", "object", "<PERSON><PERSON><PERSON>", "isLine", "isPoints", "material", "geometry", "boundingSphere", "computeBoundingSphere", "applyMatrix4", "containsPoint", "push", "children", "length"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\interactive\\SelectionBox.js"], "sourcesContent": ["import { Frustum, Vector3 } from 'three'\n\nconst frustum = /* @__PURE__ */ new Frustum()\nconst center = /* @__PURE__ */ new Vector3()\n\nconst tmpPoint = /* @__PURE__ */ new Vector3()\n\nconst vecNear = /* @__PURE__ */ new Vector3()\nconst vecTopLeft = /* @__PURE__ */ new Vector3()\nconst vecTopRight = /* @__PURE__ */ new Vector3()\nconst vecDownRight = /* @__PURE__ */ new Vector3()\nconst vecDownLeft = /* @__PURE__ */ new Vector3()\n\nconst vecFarTopLeft = /* @__PURE__ */ new Vector3()\nconst vecFarTopRight = /* @__PURE__ */ new Vector3()\nconst vecFarDownRight = /* @__PURE__ */ new Vector3()\nconst vecFarDownLeft = /* @__PURE__ */ new Vector3()\n\nconst vectemp1 = /* @__PURE__ */ new Vector3()\nconst vectemp2 = /* @__PURE__ */ new Vector3()\nconst vectemp3 = /* @__PURE__ */ new Vector3()\n\nclass SelectionBox {\n  constructor(camera, scene, deep) {\n    this.camera = camera\n    this.scene = scene\n    this.startPoint = new Vector3()\n    this.endPoint = new Vector3()\n    this.collection = []\n    this.deep = deep || Number.MAX_VALUE\n  }\n\n  select(startPoint, endPoint) {\n    this.startPoint = startPoint || this.startPoint\n    this.endPoint = endPoint || this.endPoint\n    this.collection = []\n\n    this.updateFrustum(this.startPoint, this.endPoint)\n    this.searchChildInFrustum(frustum, this.scene)\n\n    return this.collection\n  }\n\n  updateFrustum(startPoint, endPoint) {\n    startPoint = startPoint || this.startPoint\n    endPoint = endPoint || this.endPoint\n\n    // Avoid invalid frustum\n\n    if (startPoint.x === endPoint.x) {\n      endPoint.x += Number.EPSILON\n    }\n\n    if (startPoint.y === endPoint.y) {\n      endPoint.y += Number.EPSILON\n    }\n\n    this.camera.updateProjectionMatrix()\n    this.camera.updateMatrixWorld()\n\n    if (this.camera.isPerspectiveCamera) {\n      tmpPoint.copy(startPoint)\n      tmpPoint.x = Math.min(startPoint.x, endPoint.x)\n      tmpPoint.y = Math.max(startPoint.y, endPoint.y)\n      endPoint.x = Math.max(startPoint.x, endPoint.x)\n      endPoint.y = Math.min(startPoint.y, endPoint.y)\n\n      vecNear.setFromMatrixPosition(this.camera.matrixWorld)\n      vecTopLeft.copy(tmpPoint)\n      vecTopRight.set(endPoint.x, tmpPoint.y, 0)\n      vecDownRight.copy(endPoint)\n      vecDownLeft.set(tmpPoint.x, endPoint.y, 0)\n\n      vecTopLeft.unproject(this.camera)\n      vecTopRight.unproject(this.camera)\n      vecDownRight.unproject(this.camera)\n      vecDownLeft.unproject(this.camera)\n\n      vectemp1.copy(vecTopLeft).sub(vecNear)\n      vectemp2.copy(vecTopRight).sub(vecNear)\n      vectemp3.copy(vecDownRight).sub(vecNear)\n      vectemp1.normalize()\n      vectemp2.normalize()\n      vectemp3.normalize()\n\n      vectemp1.multiplyScalar(this.deep)\n      vectemp2.multiplyScalar(this.deep)\n      vectemp3.multiplyScalar(this.deep)\n      vectemp1.add(vecNear)\n      vectemp2.add(vecNear)\n      vectemp3.add(vecNear)\n\n      var planes = frustum.planes\n\n      planes[0].setFromCoplanarPoints(vecNear, vecTopLeft, vecTopRight)\n      planes[1].setFromCoplanarPoints(vecNear, vecTopRight, vecDownRight)\n      planes[2].setFromCoplanarPoints(vecDownRight, vecDownLeft, vecNear)\n      planes[3].setFromCoplanarPoints(vecDownLeft, vecTopLeft, vecNear)\n      planes[4].setFromCoplanarPoints(vecTopRight, vecDownRight, vecDownLeft)\n      planes[5].setFromCoplanarPoints(vectemp3, vectemp2, vectemp1)\n      planes[5].normal.multiplyScalar(-1)\n    } else if (this.camera.isOrthographicCamera) {\n      const left = Math.min(startPoint.x, endPoint.x)\n      const top = Math.max(startPoint.y, endPoint.y)\n      const right = Math.max(startPoint.x, endPoint.x)\n      const down = Math.min(startPoint.y, endPoint.y)\n\n      vecTopLeft.set(left, top, -1)\n      vecTopRight.set(right, top, -1)\n      vecDownRight.set(right, down, -1)\n      vecDownLeft.set(left, down, -1)\n\n      vecFarTopLeft.set(left, top, 1)\n      vecFarTopRight.set(right, top, 1)\n      vecFarDownRight.set(right, down, 1)\n      vecFarDownLeft.set(left, down, 1)\n\n      vecTopLeft.unproject(this.camera)\n      vecTopRight.unproject(this.camera)\n      vecDownRight.unproject(this.camera)\n      vecDownLeft.unproject(this.camera)\n\n      vecFarTopLeft.unproject(this.camera)\n      vecFarTopRight.unproject(this.camera)\n      vecFarDownRight.unproject(this.camera)\n      vecFarDownLeft.unproject(this.camera)\n\n      var planes = frustum.planes\n\n      planes[0].setFromCoplanarPoints(vecTopLeft, vecFarTopLeft, vecFarTopRight)\n      planes[1].setFromCoplanarPoints(vecTopRight, vecFarTopRight, vecFarDownRight)\n      planes[2].setFromCoplanarPoints(vecFarDownRight, vecFarDownLeft, vecDownLeft)\n      planes[3].setFromCoplanarPoints(vecFarDownLeft, vecFarTopLeft, vecTopLeft)\n      planes[4].setFromCoplanarPoints(vecTopRight, vecDownRight, vecDownLeft)\n      planes[5].setFromCoplanarPoints(vecFarDownRight, vecFarTopRight, vecFarTopLeft)\n      planes[5].normal.multiplyScalar(-1)\n    } else {\n      console.error('THREE.SelectionBox: Unsupported camera type.')\n    }\n  }\n\n  searchChildInFrustum(frustum, object) {\n    if (object.isMesh || object.isLine || object.isPoints) {\n      if (object.material !== undefined) {\n        if (object.geometry.boundingSphere === null) object.geometry.computeBoundingSphere()\n\n        center.copy(object.geometry.boundingSphere.center)\n\n        center.applyMatrix4(object.matrixWorld)\n\n        if (frustum.containsPoint(center)) {\n          this.collection.push(object)\n        }\n      }\n    }\n\n    if (object.children.length > 0) {\n      for (let x = 0; x < object.children.length; x++) {\n        this.searchChildInFrustum(frustum, object.children[x])\n      }\n    }\n  }\n}\n\nexport { SelectionBox }\n"], "mappings": ";AAEA,MAAMA,OAAA,GAA0B,mBAAIC,OAAA,CAAS;AAC7C,MAAMC,MAAA,GAAyB,mBAAIC,OAAA,CAAS;AAE5C,MAAMC,QAAA,GAA2B,mBAAID,OAAA,CAAS;AAE9C,MAAME,OAAA,GAA0B,mBAAIF,OAAA,CAAS;AAC7C,MAAMG,UAAA,GAA6B,mBAAIH,OAAA,CAAS;AAChD,MAAMI,WAAA,GAA8B,mBAAIJ,OAAA,CAAS;AACjD,MAAMK,YAAA,GAA+B,mBAAIL,OAAA,CAAS;AAClD,MAAMM,WAAA,GAA8B,mBAAIN,OAAA,CAAS;AAEjD,MAAMO,aAAA,GAAgC,mBAAIP,OAAA,CAAS;AACnD,MAAMQ,cAAA,GAAiC,mBAAIR,OAAA,CAAS;AACpD,MAAMS,eAAA,GAAkC,mBAAIT,OAAA,CAAS;AACrD,MAAMU,cAAA,GAAiC,mBAAIV,OAAA,CAAS;AAEpD,MAAMW,QAAA,GAA2B,mBAAIX,OAAA,CAAS;AAC9C,MAAMY,QAAA,GAA2B,mBAAIZ,OAAA,CAAS;AAC9C,MAAMa,QAAA,GAA2B,mBAAIb,OAAA,CAAS;AAE9C,MAAMc,YAAA,CAAa;EACjBC,YAAYC,MAAA,EAAQC,KAAA,EAAOC,IAAA,EAAM;IAC/B,KAAKF,MAAA,GAASA,MAAA;IACd,KAAKC,KAAA,GAAQA,KAAA;IACb,KAAKE,UAAA,GAAa,IAAInB,OAAA,CAAS;IAC/B,KAAKoB,QAAA,GAAW,IAAIpB,OAAA,CAAS;IAC7B,KAAKqB,UAAA,GAAa,EAAE;IACpB,KAAKH,IAAA,GAAOA,IAAA,IAAQI,MAAA,CAAOC,SAAA;EAC5B;EAEDC,OAAOL,UAAA,EAAYC,QAAA,EAAU;IAC3B,KAAKD,UAAA,GAAaA,UAAA,IAAc,KAAKA,UAAA;IACrC,KAAKC,QAAA,GAAWA,QAAA,IAAY,KAAKA,QAAA;IACjC,KAAKC,UAAA,GAAa,EAAE;IAEpB,KAAKI,aAAA,CAAc,KAAKN,UAAA,EAAY,KAAKC,QAAQ;IACjD,KAAKM,oBAAA,CAAqB7B,OAAA,EAAS,KAAKoB,KAAK;IAE7C,OAAO,KAAKI,UAAA;EACb;EAEDI,cAAcN,UAAA,EAAYC,QAAA,EAAU;IAClCD,UAAA,GAAaA,UAAA,IAAc,KAAKA,UAAA;IAChCC,QAAA,GAAWA,QAAA,IAAY,KAAKA,QAAA;IAI5B,IAAID,UAAA,CAAWQ,CAAA,KAAMP,QAAA,CAASO,CAAA,EAAG;MAC/BP,QAAA,CAASO,CAAA,IAAKL,MAAA,CAAOM,OAAA;IACtB;IAED,IAAIT,UAAA,CAAWU,CAAA,KAAMT,QAAA,CAASS,CAAA,EAAG;MAC/BT,QAAA,CAASS,CAAA,IAAKP,MAAA,CAAOM,OAAA;IACtB;IAED,KAAKZ,MAAA,CAAOc,sBAAA,CAAwB;IACpC,KAAKd,MAAA,CAAOe,iBAAA,CAAmB;IAE/B,IAAI,KAAKf,MAAA,CAAOgB,mBAAA,EAAqB;MACnC/B,QAAA,CAASgC,IAAA,CAAKd,UAAU;MACxBlB,QAAA,CAAS0B,CAAA,GAAIO,IAAA,CAAKC,GAAA,CAAIhB,UAAA,CAAWQ,CAAA,EAAGP,QAAA,CAASO,CAAC;MAC9C1B,QAAA,CAAS4B,CAAA,GAAIK,IAAA,CAAKE,GAAA,CAAIjB,UAAA,CAAWU,CAAA,EAAGT,QAAA,CAASS,CAAC;MAC9CT,QAAA,CAASO,CAAA,GAAIO,IAAA,CAAKE,GAAA,CAAIjB,UAAA,CAAWQ,CAAA,EAAGP,QAAA,CAASO,CAAC;MAC9CP,QAAA,CAASS,CAAA,GAAIK,IAAA,CAAKC,GAAA,CAAIhB,UAAA,CAAWU,CAAA,EAAGT,QAAA,CAASS,CAAC;MAE9C3B,OAAA,CAAQmC,qBAAA,CAAsB,KAAKrB,MAAA,CAAOsB,WAAW;MACrDnC,UAAA,CAAW8B,IAAA,CAAKhC,QAAQ;MACxBG,WAAA,CAAYmC,GAAA,CAAInB,QAAA,CAASO,CAAA,EAAG1B,QAAA,CAAS4B,CAAA,EAAG,CAAC;MACzCxB,YAAA,CAAa4B,IAAA,CAAKb,QAAQ;MAC1Bd,WAAA,CAAYiC,GAAA,CAAItC,QAAA,CAAS0B,CAAA,EAAGP,QAAA,CAASS,CAAA,EAAG,CAAC;MAEzC1B,UAAA,CAAWqC,SAAA,CAAU,KAAKxB,MAAM;MAChCZ,WAAA,CAAYoC,SAAA,CAAU,KAAKxB,MAAM;MACjCX,YAAA,CAAamC,SAAA,CAAU,KAAKxB,MAAM;MAClCV,WAAA,CAAYkC,SAAA,CAAU,KAAKxB,MAAM;MAEjCL,QAAA,CAASsB,IAAA,CAAK9B,UAAU,EAAEsC,GAAA,CAAIvC,OAAO;MACrCU,QAAA,CAASqB,IAAA,CAAK7B,WAAW,EAAEqC,GAAA,CAAIvC,OAAO;MACtCW,QAAA,CAASoB,IAAA,CAAK5B,YAAY,EAAEoC,GAAA,CAAIvC,OAAO;MACvCS,QAAA,CAAS+B,SAAA,CAAW;MACpB9B,QAAA,CAAS8B,SAAA,CAAW;MACpB7B,QAAA,CAAS6B,SAAA,CAAW;MAEpB/B,QAAA,CAASgC,cAAA,CAAe,KAAKzB,IAAI;MACjCN,QAAA,CAAS+B,cAAA,CAAe,KAAKzB,IAAI;MACjCL,QAAA,CAAS8B,cAAA,CAAe,KAAKzB,IAAI;MACjCP,QAAA,CAASiC,GAAA,CAAI1C,OAAO;MACpBU,QAAA,CAASgC,GAAA,CAAI1C,OAAO;MACpBW,QAAA,CAAS+B,GAAA,CAAI1C,OAAO;MAEpB,IAAI2C,MAAA,GAAShD,OAAA,CAAQgD,MAAA;MAErBA,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsB5C,OAAA,EAASC,UAAA,EAAYC,WAAW;MAChEyC,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsB5C,OAAA,EAASE,WAAA,EAAaC,YAAY;MAClEwC,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsBzC,YAAA,EAAcC,WAAA,EAAaJ,OAAO;MAClE2C,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsBxC,WAAA,EAAaH,UAAA,EAAYD,OAAO;MAChE2C,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsB1C,WAAA,EAAaC,YAAA,EAAcC,WAAW;MACtEuC,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsBjC,QAAA,EAAUD,QAAA,EAAUD,QAAQ;MAC5DkC,MAAA,CAAO,CAAC,EAAEE,MAAA,CAAOJ,cAAA,CAAe,EAAE;IACxC,WAAe,KAAK3B,MAAA,CAAOgC,oBAAA,EAAsB;MAC3C,MAAMC,IAAA,GAAOf,IAAA,CAAKC,GAAA,CAAIhB,UAAA,CAAWQ,CAAA,EAAGP,QAAA,CAASO,CAAC;MAC9C,MAAMuB,GAAA,GAAMhB,IAAA,CAAKE,GAAA,CAAIjB,UAAA,CAAWU,CAAA,EAAGT,QAAA,CAASS,CAAC;MAC7C,MAAMsB,KAAA,GAAQjB,IAAA,CAAKE,GAAA,CAAIjB,UAAA,CAAWQ,CAAA,EAAGP,QAAA,CAASO,CAAC;MAC/C,MAAMyB,IAAA,GAAOlB,IAAA,CAAKC,GAAA,CAAIhB,UAAA,CAAWU,CAAA,EAAGT,QAAA,CAASS,CAAC;MAE9C1B,UAAA,CAAWoC,GAAA,CAAIU,IAAA,EAAMC,GAAA,EAAK,EAAE;MAC5B9C,WAAA,CAAYmC,GAAA,CAAIY,KAAA,EAAOD,GAAA,EAAK,EAAE;MAC9B7C,YAAA,CAAakC,GAAA,CAAIY,KAAA,EAAOC,IAAA,EAAM,EAAE;MAChC9C,WAAA,CAAYiC,GAAA,CAAIU,IAAA,EAAMG,IAAA,EAAM,EAAE;MAE9B7C,aAAA,CAAcgC,GAAA,CAAIU,IAAA,EAAMC,GAAA,EAAK,CAAC;MAC9B1C,cAAA,CAAe+B,GAAA,CAAIY,KAAA,EAAOD,GAAA,EAAK,CAAC;MAChCzC,eAAA,CAAgB8B,GAAA,CAAIY,KAAA,EAAOC,IAAA,EAAM,CAAC;MAClC1C,cAAA,CAAe6B,GAAA,CAAIU,IAAA,EAAMG,IAAA,EAAM,CAAC;MAEhCjD,UAAA,CAAWqC,SAAA,CAAU,KAAKxB,MAAM;MAChCZ,WAAA,CAAYoC,SAAA,CAAU,KAAKxB,MAAM;MACjCX,YAAA,CAAamC,SAAA,CAAU,KAAKxB,MAAM;MAClCV,WAAA,CAAYkC,SAAA,CAAU,KAAKxB,MAAM;MAEjCT,aAAA,CAAciC,SAAA,CAAU,KAAKxB,MAAM;MACnCR,cAAA,CAAegC,SAAA,CAAU,KAAKxB,MAAM;MACpCP,eAAA,CAAgB+B,SAAA,CAAU,KAAKxB,MAAM;MACrCN,cAAA,CAAe8B,SAAA,CAAU,KAAKxB,MAAM;MAEpC,IAAI6B,MAAA,GAAShD,OAAA,CAAQgD,MAAA;MAErBA,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsB3C,UAAA,EAAYI,aAAA,EAAeC,cAAc;MACzEqC,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsB1C,WAAA,EAAaI,cAAA,EAAgBC,eAAe;MAC5EoC,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsBrC,eAAA,EAAiBC,cAAA,EAAgBJ,WAAW;MAC5EuC,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsBpC,cAAA,EAAgBH,aAAA,EAAeJ,UAAU;MACzE0C,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsB1C,WAAA,EAAaC,YAAA,EAAcC,WAAW;MACtEuC,MAAA,CAAO,CAAC,EAAEC,qBAAA,CAAsBrC,eAAA,EAAiBD,cAAA,EAAgBD,aAAa;MAC9EsC,MAAA,CAAO,CAAC,EAAEE,MAAA,CAAOJ,cAAA,CAAe,EAAE;IACxC,OAAW;MACLU,OAAA,CAAQC,KAAA,CAAM,8CAA8C;IAC7D;EACF;EAED5B,qBAAqB6B,QAAA,EAASC,MAAA,EAAQ;IACpC,IAAIA,MAAA,CAAOC,MAAA,IAAUD,MAAA,CAAOE,MAAA,IAAUF,MAAA,CAAOG,QAAA,EAAU;MACrD,IAAIH,MAAA,CAAOI,QAAA,KAAa,QAAW;QACjC,IAAIJ,MAAA,CAAOK,QAAA,CAASC,cAAA,KAAmB,MAAMN,MAAA,CAAOK,QAAA,CAASE,qBAAA,CAAuB;QAEpFhE,MAAA,CAAOkC,IAAA,CAAKuB,MAAA,CAAOK,QAAA,CAASC,cAAA,CAAe/D,MAAM;QAEjDA,MAAA,CAAOiE,YAAA,CAAaR,MAAA,CAAOlB,WAAW;QAEtC,IAAIiB,QAAA,CAAQU,aAAA,CAAclE,MAAM,GAAG;UACjC,KAAKsB,UAAA,CAAW6C,IAAA,CAAKV,MAAM;QAC5B;MACF;IACF;IAED,IAAIA,MAAA,CAAOW,QAAA,CAASC,MAAA,GAAS,GAAG;MAC9B,SAASzC,CAAA,GAAI,GAAGA,CAAA,GAAI6B,MAAA,CAAOW,QAAA,CAASC,MAAA,EAAQzC,CAAA,IAAK;QAC/C,KAAKD,oBAAA,CAAqB6B,QAAA,EAASC,MAAA,CAAOW,QAAA,CAASxC,CAAC,CAAC;MACtD;IACF;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}