{"ast": null, "code": "import { Box3, Matrix4 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../../math/ExtendedTriangle.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { IS_LEAF, OFFSET, COUNT, BOUNDING_DATA_INDEX } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\n\n/*****************************************************************/\n/* This file is generated from \"intersectsGeometry.template.js\". */\n/*****************************************************************/\n/* eslint-disable indent */\n\nconst boundingBox = /* @__PURE__ */new Box3();\nconst triangle = /* @__PURE__ */new ExtendedTriangle();\nconst triangle2 = /* @__PURE__ */new ExtendedTriangle();\nconst invertedMat = /* @__PURE__ */new Matrix4();\nconst obb = /* @__PURE__ */new OrientedBox();\nconst obb2 = /* @__PURE__ */new OrientedBox();\nfunction intersectsGeometry(bvh, root, otherGeometry, geometryToBvh) {\n  BufferStack.setBuffer(bvh._roots[root]);\n  const result = _intersectsGeometry(0, bvh, otherGeometry, geometryToBvh);\n  BufferStack.clearBuffer();\n  return result;\n}\nfunction _intersectsGeometry(nodeIndex32, bvh, otherGeometry, geometryToBvh, cachedObb = null) {\n  const {\n    float32Array,\n    uint16Array,\n    uint32Array\n  } = BufferStack;\n  let nodeIndex16 = nodeIndex32 * 2;\n  if (cachedObb === null) {\n    if (!otherGeometry.boundingBox) {\n      otherGeometry.computeBoundingBox();\n    }\n    obb.set(otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh);\n    cachedObb = obb;\n  }\n  const isLeaf = IS_LEAF(nodeIndex16, uint16Array);\n  if (isLeaf) {\n    const thisGeometry = bvh.geometry;\n    const thisIndex = thisGeometry.index;\n    const thisPos = thisGeometry.attributes.position;\n    const index = otherGeometry.index;\n    const pos = otherGeometry.attributes.position;\n    const offset = OFFSET(nodeIndex32, uint32Array);\n    const count = COUNT(nodeIndex16, uint16Array);\n\n    // get the inverse of the geometry matrix so we can transform our triangles into the\n    // geometry space we're trying to test. We assume there are fewer triangles being checked\n    // here.\n    invertedMat.copy(geometryToBvh).invert();\n    if (otherGeometry.boundsTree) {\n      // if there's a bounds tree\n      arrayToBox(BOUNDING_DATA_INDEX(nodeIndex32), float32Array, obb2);\n      obb2.matrix.copy(invertedMat);\n      obb2.needsUpdate = true;\n\n      // TODO: use a triangle iteration function here\n      const res = otherGeometry.boundsTree.shapecast({\n        intersectsBounds: box => obb2.intersectsBox(box),\n        intersectsTriangle: tri => {\n          tri.a.applyMatrix4(geometryToBvh);\n          tri.b.applyMatrix4(geometryToBvh);\n          tri.c.applyMatrix4(geometryToBvh);\n          tri.needsUpdate = true;\n          for (let i = offset * 3, l = (count + offset) * 3; i < l; i += 3) {\n            // this triangle needs to be transformed into the current BVH coordinate frame\n            setTriangle(triangle2, i, thisIndex, thisPos);\n            triangle2.needsUpdate = true;\n            if (tri.intersectsTriangle(triangle2)) {\n              return true;\n            }\n          }\n          return false;\n        }\n      });\n      return res;\n    } else {\n      // if we're just dealing with raw geometry\n\n      for (let i = offset * 3, l = (count + offset) * 3; i < l; i += 3) {\n        // this triangle needs to be transformed into the current BVH coordinate frame\n        setTriangle(triangle, i, thisIndex, thisPos);\n        triangle.a.applyMatrix4(invertedMat);\n        triangle.b.applyMatrix4(invertedMat);\n        triangle.c.applyMatrix4(invertedMat);\n        triangle.needsUpdate = true;\n        for (let i2 = 0, l2 = index.count; i2 < l2; i2 += 3) {\n          setTriangle(triangle2, i2, index, pos);\n          triangle2.needsUpdate = true;\n          if (triangle.intersectsTriangle(triangle2)) {\n            return true;\n          }\n        }\n      }\n    }\n  } else {\n    const left = nodeIndex32 + 8;\n    const right = uint32Array[nodeIndex32 + 6];\n    arrayToBox(BOUNDING_DATA_INDEX(left), float32Array, boundingBox);\n    const leftIntersection = cachedObb.intersectsBox(boundingBox) && _intersectsGeometry(left, bvh, otherGeometry, geometryToBvh, cachedObb);\n    if (leftIntersection) return true;\n    arrayToBox(BOUNDING_DATA_INDEX(right), float32Array, boundingBox);\n    const rightIntersection = cachedObb.intersectsBox(boundingBox) && _intersectsGeometry(right, bvh, otherGeometry, geometryToBvh, cachedObb);\n    if (rightIntersection) return true;\n    return false;\n  }\n}\nexport { intersectsGeometry };", "map": {"version": 3, "names": ["Box3", "Matrix4", "OrientedBox", "ExtendedTriangle", "set<PERSON>riangle", "arrayToBox", "IS_LEAF", "OFFSET", "COUNT", "BOUNDING_DATA_INDEX", "<PERSON><PERSON><PERSON><PERSON>ta<PERSON>", "boundingBox", "triangle", "triangle2", "invertedMat", "obb", "obb2", "intersectsGeometry", "bvh", "root", "otherGeometry", "geometryToBvh", "<PERSON><PERSON><PERSON><PERSON>", "_roots", "result", "_intersectsGeometry", "<PERSON><PERSON><PERSON><PERSON>", "nodeIndex32", "cachedObb", "float32Array", "uint16Array", "uint32Array", "nodeIndex16", "computeBoundingBox", "set", "min", "max", "<PERSON><PERSON><PERSON><PERSON>", "thisGeometry", "geometry", "thisIndex", "index", "thisPos", "attributes", "position", "pos", "offset", "count", "copy", "invert", "boundsTree", "matrix", "needsUpdate", "res", "shapecast", "intersectsBounds", "box", "intersectsBox", "intersectsTriangle", "tri", "a", "applyMatrix4", "b", "c", "i", "l", "i2", "l2", "left", "right", "leftIntersection", "rightIntersection"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/node_modules/three-mesh-bvh/src/core/cast/intersectsGeometry.generated.js"], "sourcesContent": ["import { Box3, Matrix4 } from 'three';\nimport { OrientedBox } from '../../math/OrientedBox.js';\nimport { ExtendedTriangle } from '../../math/ExtendedTriangle.js';\nimport { setTriangle } from '../../utils/TriangleUtilities.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { IS_LEAF, OFFSET, COUNT, BOUNDING_DATA_INDEX } from '../utils/nodeBufferUtils.js';\nimport { BufferStack } from '../utils/BufferStack.js';\n\n/*****************************************************************/\n/* This file is generated from \"intersectsGeometry.template.js\". */\n/*****************************************************************/\n/* eslint-disable indent */\n\nconst boundingBox = /* @__PURE__ */ new Box3();\nconst triangle = /* @__PURE__ */ new ExtendedTriangle();\nconst triangle2 = /* @__PURE__ */ new ExtendedTriangle();\nconst invertedMat = /* @__PURE__ */ new Matrix4();\n\nconst obb = /* @__PURE__ */ new OrientedBox();\nconst obb2 = /* @__PURE__ */ new OrientedBox();\n\nfunction intersectsGeometry( bvh, root, otherGeometry, geometryToBvh ) {\n\n\tBufferStack.setBuffer( bvh._roots[ root ] );\n\tconst result = _intersectsGeometry( 0, bvh, otherGeometry, geometryToBvh );\n\tBufferStack.clearBuffer();\n\n\treturn result;\n\n}\n\nfunction _intersectsGeometry( nodeIndex32, bvh, otherGeometry, geometryToBvh, cachedObb = null ) {\n\n\tconst { float32Array, uint16Array, uint32Array } = BufferStack;\n\tlet nodeIndex16 = nodeIndex32 * 2;\n\n\tif ( cachedObb === null ) {\n\n\t\tif ( ! otherGeometry.boundingBox ) {\n\n\t\t\totherGeometry.computeBoundingBox();\n\n\t\t}\n\n\t\tobb.set( otherGeometry.boundingBox.min, otherGeometry.boundingBox.max, geometryToBvh );\n\t\tcachedObb = obb;\n\n\t}\n\n\tconst isLeaf = IS_LEAF( nodeIndex16, uint16Array );\n\tif ( isLeaf ) {\n\n\t\tconst thisGeometry = bvh.geometry;\n\t\tconst thisIndex = thisGeometry.index;\n\t\tconst thisPos = thisGeometry.attributes.position;\n\n\t\tconst index = otherGeometry.index;\n\t\tconst pos = otherGeometry.attributes.position;\n\n\t\tconst offset = OFFSET( nodeIndex32, uint32Array );\n\t\tconst count = COUNT( nodeIndex16, uint16Array );\n\n\t\t// get the inverse of the geometry matrix so we can transform our triangles into the\n\t\t// geometry space we're trying to test. We assume there are fewer triangles being checked\n\t\t// here.\n\t\tinvertedMat.copy( geometryToBvh ).invert();\n\n\t\tif ( otherGeometry.boundsTree ) {\n\n\t\t\t// if there's a bounds tree\n\t\t\tarrayToBox( BOUNDING_DATA_INDEX( nodeIndex32 ), float32Array, obb2 );\n\t\t\tobb2.matrix.copy( invertedMat );\n\t\t\tobb2.needsUpdate = true;\n\n\t\t\t// TODO: use a triangle iteration function here\n\t\t\tconst res = otherGeometry.boundsTree.shapecast( {\n\n\t\t\t\tintersectsBounds: box => obb2.intersectsBox( box ),\n\n\t\t\t\tintersectsTriangle: tri => {\n\n\t\t\t\t\ttri.a.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.b.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.c.applyMatrix4( geometryToBvh );\n\t\t\t\t\ttri.needsUpdate = true;\n\n\n\t\t\t\t\tfor ( let i = offset * 3, l = ( count + offset ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\t\t\tsetTriangle( triangle2, i, thisIndex, thisPos );\n\t\t\t\t\t\ttriangle2.needsUpdate = true;\n\t\t\t\t\t\tif ( tri.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\n\t\t\t\t\treturn false;\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t\treturn res;\n\n\t\t} else {\n\n\t\t\t// if we're just dealing with raw geometry\n\n\t\t\tfor ( let i = offset * 3, l = ( count + offset ) * 3; i < l; i += 3 ) {\n\n\t\t\t\t// this triangle needs to be transformed into the current BVH coordinate frame\n\t\t\t\tsetTriangle( triangle, i, thisIndex, thisPos );\n\n\n\t\t\t\ttriangle.a.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.b.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.c.applyMatrix4( invertedMat );\n\t\t\t\ttriangle.needsUpdate = true;\n\n\t\t\t\tfor ( let i2 = 0, l2 = index.count; i2 < l2; i2 += 3 ) {\n\n\t\t\t\t\tsetTriangle( triangle2, i2, index, pos );\n\t\t\t\t\ttriangle2.needsUpdate = true;\n\n\t\t\t\t\tif ( triangle.intersectsTriangle( triangle2 ) ) {\n\n\t\t\t\t\t\treturn true;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\n\t\t\t}\n\n\n\t\t}\n\n\t} else {\n\n\t\tconst left = nodeIndex32 + 8;\n\t\tconst right = uint32Array[ nodeIndex32 + 6 ];\n\n\t\tarrayToBox( BOUNDING_DATA_INDEX( left ), float32Array, boundingBox );\n\t\tconst leftIntersection =\n\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t_intersectsGeometry( left, bvh, otherGeometry, geometryToBvh, cachedObb );\n\n\t\tif ( leftIntersection ) return true;\n\n\t\tarrayToBox( BOUNDING_DATA_INDEX( right ), float32Array, boundingBox );\n\t\tconst rightIntersection =\n\t\t\tcachedObb.intersectsBox( boundingBox ) &&\n\t\t\t_intersectsGeometry( right, bvh, otherGeometry, geometryToBvh, cachedObb );\n\n\t\tif ( rightIntersection ) return true;\n\n\t\treturn false;\n\n\t}\n\n}\n\nexport { intersectsGeometry };\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,OAAO;AACrC,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,WAAW,QAAQ,kCAAkC;AAC9D,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,mBAAmB,QAAQ,6BAA6B;AACzF,SAASC,WAAW,QAAQ,yBAAyB;;AAErD;AACA;AACA;AACA;;AAEA,MAAMC,WAAW,GAAG,eAAgB,IAAIX,IAAI,CAAC,CAAC;AAC9C,MAAMY,QAAQ,GAAG,eAAgB,IAAIT,gBAAgB,CAAC,CAAC;AACvD,MAAMU,SAAS,GAAG,eAAgB,IAAIV,gBAAgB,CAAC,CAAC;AACxD,MAAMW,WAAW,GAAG,eAAgB,IAAIb,OAAO,CAAC,CAAC;AAEjD,MAAMc,GAAG,GAAG,eAAgB,IAAIb,WAAW,CAAC,CAAC;AAC7C,MAAMc,IAAI,GAAG,eAAgB,IAAId,WAAW,CAAC,CAAC;AAE9C,SAASe,kBAAkBA,CAAEC,GAAG,EAAEC,IAAI,EAAEC,aAAa,EAAEC,aAAa,EAAG;EAEtEX,WAAW,CAACY,SAAS,CAAEJ,GAAG,CAACK,MAAM,CAAEJ,IAAI,CAAG,CAAC;EAC3C,MAAMK,MAAM,GAAGC,mBAAmB,CAAE,CAAC,EAAEP,GAAG,EAAEE,aAAa,EAAEC,aAAc,CAAC;EAC1EX,WAAW,CAACgB,WAAW,CAAC,CAAC;EAEzB,OAAOF,MAAM;AAEd;AAEA,SAASC,mBAAmBA,CAAEE,WAAW,EAAET,GAAG,EAAEE,aAAa,EAAEC,aAAa,EAAEO,SAAS,GAAG,IAAI,EAAG;EAEhG,MAAM;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAY,CAAC,GAAGrB,WAAW;EAC9D,IAAIsB,WAAW,GAAGL,WAAW,GAAG,CAAC;EAEjC,IAAKC,SAAS,KAAK,IAAI,EAAG;IAEzB,IAAK,CAAER,aAAa,CAACT,WAAW,EAAG;MAElCS,aAAa,CAACa,kBAAkB,CAAC,CAAC;IAEnC;IAEAlB,GAAG,CAACmB,GAAG,CAAEd,aAAa,CAACT,WAAW,CAACwB,GAAG,EAAEf,aAAa,CAACT,WAAW,CAACyB,GAAG,EAAEf,aAAc,CAAC;IACtFO,SAAS,GAAGb,GAAG;EAEhB;EAEA,MAAMsB,MAAM,GAAG/B,OAAO,CAAE0B,WAAW,EAAEF,WAAY,CAAC;EAClD,IAAKO,MAAM,EAAG;IAEb,MAAMC,YAAY,GAAGpB,GAAG,CAACqB,QAAQ;IACjC,MAAMC,SAAS,GAAGF,YAAY,CAACG,KAAK;IACpC,MAAMC,OAAO,GAAGJ,YAAY,CAACK,UAAU,CAACC,QAAQ;IAEhD,MAAMH,KAAK,GAAGrB,aAAa,CAACqB,KAAK;IACjC,MAAMI,GAAG,GAAGzB,aAAa,CAACuB,UAAU,CAACC,QAAQ;IAE7C,MAAME,MAAM,GAAGvC,MAAM,CAAEoB,WAAW,EAAEI,WAAY,CAAC;IACjD,MAAMgB,KAAK,GAAGvC,KAAK,CAAEwB,WAAW,EAAEF,WAAY,CAAC;;IAE/C;IACA;IACA;IACAhB,WAAW,CAACkC,IAAI,CAAE3B,aAAc,CAAC,CAAC4B,MAAM,CAAC,CAAC;IAE1C,IAAK7B,aAAa,CAAC8B,UAAU,EAAG;MAE/B;MACA7C,UAAU,CAAEI,mBAAmB,CAAEkB,WAAY,CAAC,EAAEE,YAAY,EAAEb,IAAK,CAAC;MACpEA,IAAI,CAACmC,MAAM,CAACH,IAAI,CAAElC,WAAY,CAAC;MAC/BE,IAAI,CAACoC,WAAW,GAAG,IAAI;;MAEvB;MACA,MAAMC,GAAG,GAAGjC,aAAa,CAAC8B,UAAU,CAACI,SAAS,CAAE;QAE/CC,gBAAgB,EAAEC,GAAG,IAAIxC,IAAI,CAACyC,aAAa,CAAED,GAAI,CAAC;QAElDE,kBAAkB,EAAEC,GAAG,IAAI;UAE1BA,GAAG,CAACC,CAAC,CAACC,YAAY,CAAExC,aAAc,CAAC;UACnCsC,GAAG,CAACG,CAAC,CAACD,YAAY,CAAExC,aAAc,CAAC;UACnCsC,GAAG,CAACI,CAAC,CAACF,YAAY,CAAExC,aAAc,CAAC;UACnCsC,GAAG,CAACP,WAAW,GAAG,IAAI;UAGtB,KAAM,IAAIY,CAAC,GAAGlB,MAAM,GAAG,CAAC,EAAEmB,CAAC,GAAG,CAAElB,KAAK,GAAGD,MAAM,IAAK,CAAC,EAAEkB,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAI,CAAC,EAAG;YAErE;YACA5D,WAAW,CAAES,SAAS,EAAEmD,CAAC,EAAExB,SAAS,EAAEE,OAAQ,CAAC;YAC/C7B,SAAS,CAACuC,WAAW,GAAG,IAAI;YAC5B,IAAKO,GAAG,CAACD,kBAAkB,CAAE7C,SAAU,CAAC,EAAG;cAE1C,OAAO,IAAI;YAEZ;UAED;UAGA,OAAO,KAAK;QAEb;MAED,CAAE,CAAC;MAEH,OAAOwC,GAAG;IAEX,CAAC,MAAM;MAEN;;MAEA,KAAM,IAAIW,CAAC,GAAGlB,MAAM,GAAG,CAAC,EAAEmB,CAAC,GAAG,CAAElB,KAAK,GAAGD,MAAM,IAAK,CAAC,EAAEkB,CAAC,GAAGC,CAAC,EAAED,CAAC,IAAI,CAAC,EAAG;QAErE;QACA5D,WAAW,CAAEQ,QAAQ,EAAEoD,CAAC,EAAExB,SAAS,EAAEE,OAAQ,CAAC;QAG9C9B,QAAQ,CAACgD,CAAC,CAACC,YAAY,CAAE/C,WAAY,CAAC;QACtCF,QAAQ,CAACkD,CAAC,CAACD,YAAY,CAAE/C,WAAY,CAAC;QACtCF,QAAQ,CAACmD,CAAC,CAACF,YAAY,CAAE/C,WAAY,CAAC;QACtCF,QAAQ,CAACwC,WAAW,GAAG,IAAI;QAE3B,KAAM,IAAIc,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG1B,KAAK,CAACM,KAAK,EAAEmB,EAAE,GAAGC,EAAE,EAAED,EAAE,IAAI,CAAC,EAAG;UAEtD9D,WAAW,CAAES,SAAS,EAAEqD,EAAE,EAAEzB,KAAK,EAAEI,GAAI,CAAC;UACxChC,SAAS,CAACuC,WAAW,GAAG,IAAI;UAE5B,IAAKxC,QAAQ,CAAC8C,kBAAkB,CAAE7C,SAAU,CAAC,EAAG;YAE/C,OAAO,IAAI;UAEZ;QAED;MAGD;IAGD;EAED,CAAC,MAAM;IAEN,MAAMuD,IAAI,GAAGzC,WAAW,GAAG,CAAC;IAC5B,MAAM0C,KAAK,GAAGtC,WAAW,CAAEJ,WAAW,GAAG,CAAC,CAAE;IAE5CtB,UAAU,CAAEI,mBAAmB,CAAE2D,IAAK,CAAC,EAAEvC,YAAY,EAAElB,WAAY,CAAC;IACpE,MAAM2D,gBAAgB,GACrB1C,SAAS,CAAC6B,aAAa,CAAE9C,WAAY,CAAC,IACtCc,mBAAmB,CAAE2C,IAAI,EAAElD,GAAG,EAAEE,aAAa,EAAEC,aAAa,EAAEO,SAAU,CAAC;IAE1E,IAAK0C,gBAAgB,EAAG,OAAO,IAAI;IAEnCjE,UAAU,CAAEI,mBAAmB,CAAE4D,KAAM,CAAC,EAAExC,YAAY,EAAElB,WAAY,CAAC;IACrE,MAAM4D,iBAAiB,GACtB3C,SAAS,CAAC6B,aAAa,CAAE9C,WAAY,CAAC,IACtCc,mBAAmB,CAAE4C,KAAK,EAAEnD,GAAG,EAAEE,aAAa,EAAEC,aAAa,EAAEO,SAAU,CAAC;IAE3E,IAAK2C,iBAAiB,EAAG,OAAO,IAAI;IAEpC,OAAO,KAAK;EAEb;AAED;AAEA,SAAStD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}