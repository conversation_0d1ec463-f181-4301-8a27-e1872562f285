{"ast": null, "code": "import React from\"react\";import\"./Projects.css\";import ProjectCard from\"./ProjectCard/ProjectCard\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Projects=()=>{return/*#__PURE__*/_jsxs(\"section\",{id:\"projects\",className:\"projects-container\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"My Projects\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"projects-content\",children:[/*#__PURE__*/_jsx(ProjectCard,{imageUrl:`${process.env.PUBLIC_URL}/images/three.png`,title:\"3D Boat Simulation\",description:\"Created a 3D boat simulation using Three.js, handling physics and modeling independently. This project demonstrates advanced 3D graphics and interactive simulation skills.\",reverse:false,ProjectUrl:\"https://github.com/Nicholass206/Boat-3js\"}),/*#__PURE__*/_jsx(ProjectCard,{imageUrl:`${process.env.PUBLIC_URL}/images/church-image.jpg`,title:\"Alessandra Parisi Sito\",description:\"Developed a responsive portfolio website using React.js to showcase Alessandra Parisi\\u2019s collection of holy icons and religious artwork.\",reverse:true,ProjectUrl:\"https://alessandraparisi.it\"}),/*#__PURE__*/_jsx(ProjectCard,{imageUrl:`${process.env.PUBLIC_URL}/images/pierre4ad-logo.jpg`,title:\"Pierre4Ad\",description:\"Contributed to a freelance project by building responsive web pages with HTML, CSS, and JavaScript, focusing on user-friendly design and functionality.\",reverse:false,ProjectUrl:\"https://pierre4ad.com/\"}),/*#__PURE__*/_jsx(ProjectCard,{imageUrl:`${process.env.PUBLIC_URL}/images/passport-image.jpg`,title:\"Passports-IDs Scanner\",description:\"Integrated a Flutter SDK to scan documents like (Passports, IDs, Driver's License etc..) using MRZ decoding and pdf417, check validity of these documents \",reverse:true,ProjectUrl:\"https://gitlab.com/fatora1/Scanner-Flutter-App\"}),/*#__PURE__*/_jsx(ProjectCard,{imageUrl:`${process.env.PUBLIC_URL}/images/athletics-dxb.png`,title:\"AthleticsDXB\",description:\"Designed and developed a full-featured athlete management system from the ground up using Flutter for the front end and ASP.NET Core for the back end.\",reverse:false,ProjectUrl:\"https://gitlab.com/fatora1/*********************\"})]})]});};export default Projects;", "map": {"version": 3, "names": ["React", "ProjectCard", "jsx", "_jsx", "jsxs", "_jsxs", "Projects", "id", "className", "children", "imageUrl", "process", "env", "PUBLIC_URL", "title", "description", "reverse", "ProjectUrl"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/components/Projects/Projects.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport \"./Projects.css\";\r\nimport ProjectCard from \"./ProjectCard/ProjectCard\";\r\n\r\nconst Projects = () => {\r\n  return (\r\n    <section id=\"projects\" className=\"projects-container\">\r\n      <h5>My Projects</h5>\r\n      <div className=\"projects-content\">\r\n        {/* <ProjectCard\r\n                imageUrl={`${process.env.PUBLIC_URL}/images/Medicure with name.png`}\r\n                title=\" Medicure \"\r\n                description=\"Built a Laravel-based system for managing pharmacy inventory and orders, streamlining warehouse operations with efficient backend functionality and database management.\"\r\n                reverse={false}  \r\n                ProjectUrl=\"https://github.com/Nicholass206/Pharmacy-Warehouse/tree/first\"\r\n            /> */}\r\n        <ProjectCard\r\n          imageUrl={`${process.env.PUBLIC_URL}/images/three.png`}\r\n          title=\"3D Boat Simulation\"\r\n          description=\"Created a 3D boat simulation using Three.js, handling physics and modeling independently. This project demonstrates advanced 3D graphics and interactive simulation skills.\"\r\n          reverse={false}\r\n          ProjectUrl=\"https://github.com/Nicholass206/Boat-3js\"\r\n        />\r\n        <ProjectCard\r\n          imageUrl={`${process.env.PUBLIC_URL}/images/church-image.jpg`}\r\n          title=\"Alessandra Parisi Sito\"\r\n          description=\"Developed a responsive portfolio website using React.js to showcase Alessandra Parisi’s collection of holy icons and religious artwork.\"\r\n          reverse={true}\r\n          ProjectUrl=\"https://alessandraparisi.it\"\r\n        />\r\n        <ProjectCard\r\n          imageUrl={`${process.env.PUBLIC_URL}/images/pierre4ad-logo.jpg`}\r\n          title=\"Pierre4Ad\"\r\n          description=\"Contributed to a freelance project by building responsive web pages with HTML, CSS, and JavaScript, focusing on user-friendly design and functionality.\"\r\n          reverse={false}\r\n          ProjectUrl=\"https://pierre4ad.com/\"\r\n        />\r\n        <ProjectCard\r\n          imageUrl={`${process.env.PUBLIC_URL}/images/passport-image.jpg`}\r\n          title=\"Passports-IDs Scanner\"\r\n          description=\"Integrated a Flutter SDK to scan documents like (Passports, IDs, Driver's License etc..) using MRZ decoding and pdf417, check validity of these documents \"\r\n          reverse={true}\r\n          ProjectUrl=\"https://gitlab.com/fatora1/Scanner-Flutter-App\"\r\n        />\r\n        <ProjectCard\r\n          imageUrl={`${process.env.PUBLIC_URL}/images/athletics-dxb.png`}\r\n          title=\"AthleticsDXB\"\r\n          description=\"Designed and developed a full-featured athlete management system from the ground up using Flutter for the front end and ASP.NET Core for the back end.\"\r\n          reverse={false}\r\n          ProjectUrl=\"https://gitlab.com/fatora1/*********************\"\r\n        />\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Projects;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,gBAAgB,CACvB,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,mBACED,KAAA,YAASE,EAAE,CAAC,UAAU,CAACC,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnDN,IAAA,OAAAM,QAAA,CAAI,aAAW,CAAI,CAAC,cACpBJ,KAAA,QAAKG,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAQ/BN,IAAA,CAACF,WAAW,EACVS,QAAQ,CAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,mBAAoB,CACvDC,KAAK,CAAC,oBAAoB,CAC1BC,WAAW,CAAC,6KAA6K,CACzLC,OAAO,CAAE,KAAM,CACfC,UAAU,CAAC,0CAA0C,CACtD,CAAC,cACFd,IAAA,CAACF,WAAW,EACVS,QAAQ,CAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,0BAA2B,CAC9DC,KAAK,CAAC,wBAAwB,CAC9BC,WAAW,CAAC,8IAAyI,CACrJC,OAAO,CAAE,IAAK,CACdC,UAAU,CAAC,6BAA6B,CACzC,CAAC,cACFd,IAAA,CAACF,WAAW,EACVS,QAAQ,CAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,4BAA6B,CAChEC,KAAK,CAAC,WAAW,CACjBC,WAAW,CAAC,yJAAyJ,CACrKC,OAAO,CAAE,KAAM,CACfC,UAAU,CAAC,wBAAwB,CACpC,CAAC,cACFd,IAAA,CAACF,WAAW,EACVS,QAAQ,CAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,4BAA6B,CAChEC,KAAK,CAAC,uBAAuB,CAC7BC,WAAW,CAAC,4JAA4J,CACxKC,OAAO,CAAE,IAAK,CACdC,UAAU,CAAC,gDAAgD,CAC5D,CAAC,cACFd,IAAA,CAACF,WAAW,EACVS,QAAQ,CAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,2BAA4B,CAC/DC,KAAK,CAAC,cAAc,CACpBC,WAAW,CAAC,wJAAwJ,CACpKC,OAAO,CAAE,KAAM,CACfC,UAAU,CAAC,kDAAkD,CAC9D,CAAC,EACC,CAAC,EACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAAX,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}