{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\nicola-portfolio\\\\nicola-portfolio\\\\src\\\\components\\\\Footer\\\\Footer.jsx\";\nimport React from 'react';\nimport './Footer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  const socialLinks = [{\n    name: 'GitHub',\n    url: 'https://github.com/nicolafadoul',\n    icon: './images/github.png',\n    description: 'Check out my code'\n  }, {\n    name: 'Git<PERSON><PERSON>',\n    url: 'https://gitlab.com/nicolafadoul',\n    icon: '🦊',\n    description: 'View my projects'\n  }, {\n    name: 'Instagram',\n    url: 'https://instagram.com/nicolafadoul',\n    icon: '📸',\n    description: 'Follow my journey'\n  }, {\n    name: 'Facebook',\n    url: 'https://facebook.com/nicolafadoul',\n    icon: '📘',\n    description: 'Connect with me'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-main\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Nicola Fadoul\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Junior Developer passionate about creating innovative solutions and building meaningful digital experiences.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-quote\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\\"Code is poetry written in logic\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-social\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Let's Connect\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"social-links\",\n            children: socialLinks.map((social, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n              href: social.url,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"social-link\",\n              title: social.description,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"social-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: social.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"social-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"social-name\",\n                  children: social.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"social-desc\",\n                  children: social.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-bottom\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-divider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer-copyright\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\xA9 \", currentYear, \" Nicola Fadoul. All rights reserved.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Built with React & passion \\u2764\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "socialLinks", "name", "url", "icon", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "social", "index", "href", "target", "rel", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/components/Footer/Footer.jsx"], "sourcesContent": ["import React from 'react';\nimport './Footer.css';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  const socialLinks = [\n    {\n      name: 'GitHub',\n      url: 'https://github.com/nicolafadoul',\n      icon: './images/github.png',\n      description: 'Check out my code'\n    },\n    {\n      name: 'GitLab',\n      url: 'https://gitlab.com/nicolafadoul',\n      icon: '🦊',\n      description: 'View my projects'\n    },\n    {\n      name: 'Instagram',\n      url: 'https://instagram.com/nicolafadoul',\n      icon: '📸',\n      description: 'Follow my journey'\n    },\n    {\n      name: 'Facebook',\n      url: 'https://facebook.com/nicolafadoul',\n      icon: '📘',\n      description: 'Connect with me'\n    }\n  ];\n\n  return (\n    <footer className=\"footer-container\">\n      <div className=\"footer-content\">\n        <div className=\"footer-main\">\n          <div className=\"footer-info\">\n            <h3><PERSON></h3>\n            <p><PERSON> passionate about creating innovative solutions and building meaningful digital experiences.</p>\n            <div className=\"footer-quote\">\n              <p>\"Code is poetry written in logic\"</p>\n            </div>\n          </div>\n          \n          <div className=\"footer-social\">\n            <h4>Let's Connect</h4>\n            <div className=\"social-links\">\n              {socialLinks.map((social, index) => (\n                <a\n                  key={index}\n                  href={social.url}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"social-link\"\n                  title={social.description}\n                >\n                  <div className=\"social-icon\">\n                    <span>{social.icon}</span>\n                  </div>\n                  <div className=\"social-info\">\n                    <span className=\"social-name\">{social.name}</span>\n                    <span className=\"social-desc\">{social.description}</span>\n                  </div>\n                </a>\n              ))}\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"footer-bottom\">\n          <div className=\"footer-divider\"></div>\n          <div className=\"footer-copyright\">\n            <p>&copy; {currentYear} Nicola Fadoul. All rights reserved.</p>\n            <p>Built with React & passion ❤️</p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,MAAMC,WAAW,GAAG,CAClB;IACEC,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,iCAAiC;IACtCC,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,iCAAiC;IACtCC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBC,GAAG,EAAE,oCAAoC;IACzCC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE;EACf,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,GAAG,EAAE,mCAAmC;IACxCC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACET,OAAA;IAAQU,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAClCX,OAAA;MAAKU,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BX,OAAA;QAAKU,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BX,OAAA;UAAKU,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BX,OAAA;YAAAW,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBf,OAAA;YAAAW,QAAA,EAAG;UAA4G;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnHf,OAAA;YAAKU,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BX,OAAA;cAAAW,QAAA,EAAG;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UAAKU,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BX,OAAA;YAAAW,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBf,OAAA;YAAKU,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BN,WAAW,CAACW,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC7BlB,OAAA;cAEEmB,IAAI,EAAEF,MAAM,CAACV,GAAI;cACjBa,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBX,SAAS,EAAC,aAAa;cACvBY,KAAK,EAAEL,MAAM,CAACR,WAAY;cAAAE,QAAA,gBAE1BX,OAAA;gBAAKU,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BX,OAAA;kBAAAW,QAAA,EAAOM,MAAM,CAACT;gBAAI;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACNf,OAAA;gBAAKU,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BX,OAAA;kBAAMU,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEM,MAAM,CAACX;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDf,OAAA;kBAAMU,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEM,MAAM,CAACR;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA,GAbDG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcT,CACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENf,OAAA;QAAKU,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BX,OAAA;UAAKU,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtCf,OAAA;UAAKU,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BX,OAAA;YAAAW,QAAA,GAAG,OAAO,EAACT,WAAW,EAAC,sCAAoC;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC/Df,OAAA;YAAAW,QAAA,EAAG;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACQ,EAAA,GA7EItB,MAAM;AA+EZ,eAAeA,MAAM;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}