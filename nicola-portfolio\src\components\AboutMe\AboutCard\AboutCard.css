.about-card {
  border-radius: 0.65rem;
  border: 1px solid var(--primary-color);
  padding: 2rem;
  background: rgba(22, 17, 47, 0.398);
  backdrop-filter: blur(1rem);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.about-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 123, 255, 0.2);
  border-color: var(--secondary-color);
}

.about-card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.about-card-icon {
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--gradient);
}

.about-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.about-card-content {
  position: relative;
}

.about-card-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.about-card-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  font-size: 0.95rem;
  color: var(--text-color);
  transition: all 0.2s ease;
}

.about-card-item:hover {
  transform: translateX(5px);
  color: var(--primary-color);
}

.bullet {
  color: var(--primary-color);
  font-weight: bold;
  font-size: 1.2rem;
  min-width: 1rem;
}

@media (max-width: 768px) {
  .about-card {
    padding: 1.5rem;
  }
  
  .about-card-header {
    gap: 0.75rem;
    margin-bottom: 1rem;
  }
  
  .about-card-icon {
    font-size: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .about-card-title {
    font-size: 1.1rem;
  }
  
  .about-card-item {
    font-size: 0.9rem;
    padding: 0.4rem 0;
  }
}

@media (max-width: 480px) {
  .about-card {
    padding: 1.25rem;
  }
  
  .about-card-header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .about-card-title {
    font-size: 1rem;
  }
  
  .about-card-item {
    font-size: 0.85rem;
  }
}
