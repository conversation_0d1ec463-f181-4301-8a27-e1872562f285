{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\nicola-portfolio\\\\nicola-portfolio\\\\src\\\\components\\\\Hero\\\\Hero.jsx\";\nimport React from 'react';\nimport './Hero.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"home\",\n    className: \"hero-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Hi, I'm <PERSON>\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"typewriter-text\",\n        children: \"Junior Developer | Enthusiastic Learner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-img\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: `${process.env.PUBLIC_URL}/images/profile-picture.png`,\n          alt: \"Profile\",\n          className: \"profile-pic\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tech-icons\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-1\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/react1.png`,\n              alt: \"React\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-2\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/flutter-icon.webp`,\n              alt: \"Flutter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-3\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/html2.png`,\n              alt: \"HTML\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-4\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/css-icon.png`,\n              alt: \"CSS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-5\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/js.png`,\n              alt: \"JavaScript\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-6\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/laravel-icon.png`,\n              alt: \"Laravel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Hero", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "process", "env", "PUBLIC_URL", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/components/Hero/Hero.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './Hero.css'\r\n\r\nconst Hero = () => {\r\n  return (\r\n    <section id=\"home\" className=\"hero-container\">\r\n        <div className=\"hero-content\">\r\n            <h2>Hi, I'm <PERSON></h2>\r\n            <p className=\"typewriter-text\"><PERSON> | Enthusiastic Learner</p>\r\n        </div>\r\n\r\n        <div className=\"hero-img\">\r\n            <div className=\"profile-container\">\r\n                <img src={`${process.env.PUBLIC_URL}/images/profile-picture.png`} alt=\"Profile\" className=\"profile-pic\" />\r\n                <div className=\"tech-icons\">\r\n                    <div className=\"tech-icon tech-icon-1\">\r\n                        <img src={`${process.env.PUBLIC_URL}/images/react1.png`} alt=\"React\" />\r\n                    </div>\r\n                    <div className=\"tech-icon tech-icon-2\">\r\n                        <img src={`${process.env.PUBLIC_URL}/images/flutter-icon.webp`} alt=\"Flutter\" />\r\n                    </div>\r\n                    <div className=\"tech-icon tech-icon-3\">\r\n                        <img src={`${process.env.PUBLIC_URL}/images/html2.png`} alt=\"HTML\" />\r\n                    </div>\r\n                    <div className=\"tech-icon tech-icon-4\">\r\n                        <img src={`${process.env.PUBLIC_URL}/images/css-icon.png`} alt=\"CSS\" />\r\n                    </div>\r\n                    <div className=\"tech-icon tech-icon-5\">\r\n                        <img src={`${process.env.PUBLIC_URL}/images/js.png`} alt=\"JavaScript\" />\r\n                    </div>\r\n                    <div className=\"tech-icon tech-icon-6\">\r\n                        <img src={`${process.env.PUBLIC_URL}/images/laravel-icon.png`} alt=\"Laravel\" />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default Hero;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,YAAY;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACED,OAAA;IAASE,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBACzCJ,OAAA;MAAKG,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACzBJ,OAAA;QAAAI,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9BR,OAAA;QAAGG,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,eAENR,OAAA;MAAKG,SAAS,EAAC,UAAU;MAAAC,QAAA,eACrBJ,OAAA;QAAKG,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC9BJ,OAAA;UAAKS,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,6BAA8B;UAACC,GAAG,EAAC,SAAS;UAACV,SAAS,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1GR,OAAA;UAAKG,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBJ,OAAA;YAAKG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAClCJ,OAAA;cAAKS,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,oBAAqB;cAACC,GAAG,EAAC;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACNR,OAAA;YAAKG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAClCJ,OAAA;cAAKS,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,2BAA4B;cAACC,GAAG,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACNR,OAAA;YAAKG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAClCJ,OAAA;cAAKS,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,mBAAoB;cAACC,GAAG,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACNR,OAAA;YAAKG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAClCJ,OAAA;cAAKS,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,sBAAuB;cAACC,GAAG,EAAC;YAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACNR,OAAA;YAAKG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAClCJ,OAAA;cAAKS,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,gBAAiB;cAACC,GAAG,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNR,OAAA;YAAKG,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eAClCJ,OAAA;cAAKS,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,0BAA2B;cAACC,GAAG,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAAM,EAAA,GAnCKb,IAAI;AAqCV,eAAeA,IAAI;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}