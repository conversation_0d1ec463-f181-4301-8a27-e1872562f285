/* index.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

* {
  font-family: "Poppins", sans-serif;
  margin: 0;
  box-sizing: border-box;
}

.light-theme {
  --background-color: #ffffff;
  --text-color: #000000;
  --primary-color: #007bff; 
  --secondary-color: #0056b3; 
  --navbar-background: rgba(255, 255, 255, 0.1); 
  --navbar-blur: blur(50px); 
  --gradient: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}
/* Default Dark Theme */
:root {
  --background-color: #060417;
  --text-color: #ffffff;
  --primary-color: #a993fe; /* Purple for buttons and accents */
  --secondary-color: #7e61e7; /* Darker purple for hover effects */
  --navbar-background: rgba(0, 0, 0, 0.1); /* Semi-transparent black for navbar */
  --navbar-blur: blur(50px); /* Blur effect for navbar */
  --gradient: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

/* Light Theme */

body {
  background-color: var(--background-color);
  color: var(--text-color);
  transition: --background-color 0.3s ease, color 0.3s ease;
}