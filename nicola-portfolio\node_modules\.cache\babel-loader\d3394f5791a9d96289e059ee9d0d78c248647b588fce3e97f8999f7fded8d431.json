{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\nicola-portfolio\\\\nicola-portfolio\\\\src\\\\components\\\\Navbar\\\\Navbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport \"./Navbar.css\";\nimport MobileNavbar from \"./MobileNavbar/MobileNavbar\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = ({\n  toggleTheme,\n  isDarkMode\n}) => {\n  _s();\n  const [openMenu, setOpenMenu] = useState(false);\n  const [activeLink, setActiveLink] = useState(\"home\");\n  const toggleMenu = () => {\n    setOpenMenu(!openMenu);\n  };\n  const downloadButton = () => {\n    try {\n      const link = document.createElement(\"a\");\n      link.href = \"/images/Nicola-Fadoul-Resume.pdf\";\n      link.download = \"Nicola-Fadoul-Resume.pdf\";\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    } catch (error) {\n      console.error(\"Error downloading the file:\", error);\n      window.open(\"/images/Nicola-Fadoul-Resume.pdf\", \"_blank\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(MobileNavbar, {\n      isOpen: openMenu,\n      toggleMenu: toggleMenu,\n      activeLink: activeLink,\n      setActiveLink: setActiveLink\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"nav-wrapper\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleTheme,\n          className: \"theme-toggle\",\n          children: isDarkMode ? \"🌞 Light Mode\" : \"🌙 Dark Mode\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#home\",\n              className: `menu-item ${activeLink === \"home\" ? \"active\" : \"\"}`,\n              onClick: () => setActiveLink(\"home\"),\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#skills\",\n              className: `menu-item ${activeLink === \"skills\" ? \"active\" : \"\"}`,\n              onClick: () => setActiveLink(\"skills\"),\n              children: \"Skills\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#projects\",\n              className: `menu-item ${activeLink === \"projects\" ? \"active\" : \"\"}`,\n              onClick: () => setActiveLink(\"projects\"),\n              children: \"Projects\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#contact-me\",\n              className: `menu-item ${activeLink === \"contact-me\" ? \"active\" : \"\"}`,\n              onClick: () => setActiveLink(\"contact-me\"),\n              children: \"Contact Me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"resume-dropdown\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"contact-btn\",\n              onClick: downloadButton,\n              children: \"Resume\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"menu-btn\",\n          onClick: toggleMenu,\n          \"aria-label\": \"Toggle Menu\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"material-symbols-outlined\",\n            style: {\n              fontSize: \"1.8rem\"\n            },\n            children: \"menu\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Navbar, \"ThJFjOOLtRWJYOfCf8but0Tqcv4=\");\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "MobileNavbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "toggleTheme", "isDarkMode", "_s", "openMenu", "setOpenMenu", "activeLink", "setActiveLink", "toggleMenu", "downloadButton", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "console", "window", "open", "children", "isOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "style", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/components/Navbar/Navbar.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"./Navbar.css\";\r\nimport MobileNavbar from \"./MobileNavbar/MobileNavbar\";\r\n\r\nconst Navbar = ({toggleTheme, isDarkMode}) => {\r\n  const [openMenu, setOpenMenu] = useState(false);\r\n  const [activeLink, setActiveLink] = useState(\"home\");\r\n\r\n  const toggleMenu = () => {\r\n    setOpenMenu(!openMenu);\r\n  };\r\n\r\n  const downloadButton = () => {\r\n    try {\r\n\r\n      const link = document.createElement(\"a\");\r\n      link.href = \"/images/Nicola-Fadoul-Resume.pdf\";\r\n      link.download = \"Nicola-Fadoul-Resume.pdf\";\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    } catch (error) {\r\n      console.error(\"Error downloading the file:\", error);\r\n      window.open(\"/images/Nicola-Fadoul-Resume.pdf\", \"_blank\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <MobileNavbar\r\n        isOpen={openMenu}\r\n        toggleMenu={toggleMenu}\r\n        activeLink={activeLink}\r\n        setActiveLink={setActiveLink}\r\n      />\r\n      <nav className=\"nav-wrapper\">\r\n        <div className=\"nav-content\">\r\n        <button onClick={toggleTheme} className=\"theme-toggle\">\r\n          {isDarkMode ? \"🌞 Light Mode\" : \"🌙 Dark Mode\"}\r\n        </button>\r\n          <ul>\r\n            <li>\r\n              <a\r\n                href=\"#home\"\r\n                className={`menu-item ${activeLink === \"home\" ? \"active\" : \"\"}`}\r\n                onClick={() => setActiveLink(\"home\")}\r\n              >\r\n                Home\r\n              </a>\r\n            </li>\r\n            <li>\r\n              <a\r\n                href=\"#skills\"\r\n                className={`menu-item ${activeLink === \"skills\" ? \"active\" : \"\"}`}\r\n                onClick={() => setActiveLink(\"skills\")}\r\n              >\r\n                Skills\r\n              </a>\r\n            </li>\r\n            <li>\r\n              <a\r\n                href=\"#projects\"\r\n                className={`menu-item ${activeLink === \"projects\" ? \"active\" : \"\"}`}\r\n                onClick={() => setActiveLink(\"projects\")}\r\n              >\r\n                Projects\r\n              </a>\r\n            </li>\r\n            <li>\r\n              <a\r\n                href=\"#contact-me\"\r\n                className={`menu-item ${activeLink === \"contact-me\" ? \"active\" : \"\"}`}\r\n                onClick={() => setActiveLink(\"contact-me\")}\r\n              >\r\n                Contact Me\r\n              </a>\r\n            </li>\r\n            <div className=\"resume-dropdown\">\r\n              <button className=\"contact-btn\" onClick={downloadButton}>\r\n              Resume\r\n              </button>\r\n            </div>\r\n          </ul>\r\n          <button className=\"menu-btn\" onClick={toggleMenu} aria-label=\"Toggle Menu\">\r\n            <span className=\"material-symbols-outlined\" style={{ fontSize: \"1.8rem\" }}>\r\n              menu\r\n            </span>\r\n          </button>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Navbar;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,cAAc;AACrB,OAAOC,YAAY,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,MAAM,GAAGA,CAAC;EAACC,WAAW;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,MAAM,CAAC;EAEpD,MAAMc,UAAU,GAAGA,CAAA,KAAM;IACvBH,WAAW,CAAC,CAACD,QAAQ,CAAC;EACxB,CAAC;EAED,MAAMK,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI;MAEF,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAG,kCAAkC;MAC9CH,IAAI,CAACI,QAAQ,GAAG,0BAA0B;MAC1CH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDE,MAAM,CAACC,IAAI,CAAC,kCAAkC,EAAE,QAAQ,CAAC;IAC3D;EACF,CAAC;EAED,oBACEzB,OAAA,CAAAE,SAAA;IAAAwB,QAAA,gBACE1B,OAAA,CAACF,YAAY;MACX6B,MAAM,EAAEpB,QAAS;MACjBI,UAAU,EAAEA,UAAW;MACvBF,UAAU,EAAEA,UAAW;MACvBC,aAAa,EAAEA;IAAc;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eACF/B,OAAA;MAAKgC,SAAS,EAAC,aAAa;MAAAN,QAAA,eAC1B1B,OAAA;QAAKgC,SAAS,EAAC,aAAa;QAAAN,QAAA,gBAC5B1B,OAAA;UAAQiC,OAAO,EAAE7B,WAAY;UAAC4B,SAAS,EAAC,cAAc;UAAAN,QAAA,EACnDrB,UAAU,GAAG,eAAe,GAAG;QAAc;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACP/B,OAAA;UAAA0B,QAAA,gBACE1B,OAAA;YAAA0B,QAAA,eACE1B,OAAA;cACEgB,IAAI,EAAC,OAAO;cACZgB,SAAS,EAAE,aAAavB,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;cAChEwB,OAAO,EAAEA,CAAA,KAAMvB,aAAa,CAAC,MAAM,CAAE;cAAAgB,QAAA,EACtC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL/B,OAAA;YAAA0B,QAAA,eACE1B,OAAA;cACEgB,IAAI,EAAC,SAAS;cACdgB,SAAS,EAAE,aAAavB,UAAU,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;cAClEwB,OAAO,EAAEA,CAAA,KAAMvB,aAAa,CAAC,QAAQ,CAAE;cAAAgB,QAAA,EACxC;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL/B,OAAA;YAAA0B,QAAA,eACE1B,OAAA;cACEgB,IAAI,EAAC,WAAW;cAChBgB,SAAS,EAAE,aAAavB,UAAU,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;cACpEwB,OAAO,EAAEA,CAAA,KAAMvB,aAAa,CAAC,UAAU,CAAE;cAAAgB,QAAA,EAC1C;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL/B,OAAA;YAAA0B,QAAA,eACE1B,OAAA;cACEgB,IAAI,EAAC,aAAa;cAClBgB,SAAS,EAAE,aAAavB,UAAU,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;cACtEwB,OAAO,EAAEA,CAAA,KAAMvB,aAAa,CAAC,YAAY,CAAE;cAAAgB,QAAA,EAC5C;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACL/B,OAAA;YAAKgC,SAAS,EAAC,iBAAiB;YAAAN,QAAA,eAC9B1B,OAAA;cAAQgC,SAAS,EAAC,aAAa;cAACC,OAAO,EAAErB,cAAe;cAAAc,QAAA,EAAC;YAEzD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACL/B,OAAA;UAAQgC,SAAS,EAAC,UAAU;UAACC,OAAO,EAAEtB,UAAW;UAAC,cAAW,aAAa;UAAAe,QAAA,eACxE1B,OAAA;YAAMgC,SAAS,EAAC,2BAA2B;YAACE,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAS,CAAE;YAAAT,QAAA,EAAC;UAE3E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACzB,EAAA,CAxFIH,MAAM;AAAAiC,EAAA,GAANjC,MAAM;AA0FZ,eAAeA,MAAM;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}