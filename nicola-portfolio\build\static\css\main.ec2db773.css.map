{"version": 3, "file": "static/css/main.ec2db773.css", "mappings": "iMAEA,EAGE,qBAAsB,CAFtB,8BAAkC,CAClC,QAEF,CAGA,aACE,0BAA2B,CAC3B,iBAAqB,CACrB,uBAAwB,CACxB,yBAA0B,CAC1B,6BAA6C,CAC7C,wBAAyB,CACzB,qFACF,CAEA,MACE,0BAA2B,CAC3B,iBAAqB,CACrB,uBAAwB,CACxB,yBAA0B,CAC1B,6BAAuC,CACvC,wBAAyB,CACzB,qFACF,CAGA,KACE,wBAAyC,CAAzC,wCAAyC,CACzC,UAAwB,CAAxB,uBAAwB,CACxB,qDACF,CClCA,WAEE,aAAc,CADd,0BAA4B,CAE5B,iBAGF,CAEA,KACE,sBACF,CAEA,0BACE,WACI,gBAEJ,CACF,CAEA,yBACE,WACE,cACF,CACF,CCtBA,aAME,0CAAmC,CAAnC,kCAAmC,CALnC,mCAAoC,CACpC,SAAY,CACZ,eAAgB,CAChB,KAAM,CACN,UAEF,CAEA,aAGE,kBAAmB,CADnB,YAAa,CAEb,wBAAyB,CAEzB,aAAc,CALd,gBAAiB,CAIjB,cAEF,CAEA,MAGE,YAAa,CAFb,gBAAiB,CACjB,WAEF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,SAAW,CACX,eACF,CAEA,gBACE,eACF,CAYA,kBAQE,MAAQ,CACR,OAGF,CAOA,yBAEE,0BACF,CAkBA,cACE,eAAgB,CAIhB,cAAe,CACf,iBACF,CAQA,wBAbE,WAAY,CACZ,uBAAwB,CACxB,cA0BF,CAfA,UAKE,kBAAmB,CAKnB,0BAA2B,CAF3B,mBAAqB,CAJrB,YAAa,CAUb,YAAa,CAXb,gBAAiB,CADjB,aAAc,CAId,sBAAuB,CAKvB,aAAc,CAEd,uBAAyB,CAZzB,YAcF,CAEA,gBAEE,kCAAmC,CACnC,qCAAsC,CAFtC,0BAGF,CAEA,qCACE,gBACF,CAEA,yBACE,UACE,aACF,CAEA,gBACE,YACF,CACF,CAEA,0BACE,aACE,cACF,CACF,CC9IA,aAIE,0BAAoC,CAMpC,gCAA4C,CAP5C,YAAa,CADb,YAAa,CAIb,MAAO,CAGP,SAAU,CAJV,cAAe,CAEf,KAAM,CAKN,2BAA4B,CAD5B,uBAAyB,CAVzB,WAAY,CAOZ,qBAKF,CAEA,uBAGE,wCAAyC,CADzC,YAAa,CAGb,gBAAgB,CAChB,cAAe,CAFf,YAAa,CAHb,UAMF,CAEA,oBACE,SAAU,CACV,uBACF,CAMA,iCAHE,kBAUF,CAPA,gBACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,eAAgB,CAChB,iBAEF,CAEA,WAIE,uBAAwB,CAExB,cAAe,CAJf,eAAiB,CACjB,eAAgB,CAEhB,iBAAkB,CAJlB,oBAAqB,CAMrB,yBACF,CAEA,kBAIE,0BAA2B,CAC3B,mBAAqB,CAErB,aAAe,CANf,UAAW,CAEX,YAAc,CAKd,SAAU,CAFV,iBAAkB,CAGlB,6BAA8B,CAC9B,uBAAyB,CARzB,UASF,CAEA,wBAGE,SAAU,CADV,uBAAwB,CADxB,UAGF,CAEA,kBACE,0BACF,CAEA,yBACE,SAAU,CACV,uBACF,CAEA,aAKE,kBAAmB,CAEnB,0BAA2B,CAE3B,WAAY,CAEZ,mBAAqB,CALrB,uBAAwB,CAMxB,cAAe,CATf,YAAa,CAFb,eAAiB,CACjB,eAAgB,CAEhB,sBAAuB,CAMvB,yBAAgC,CAFhC,kBAAoB,CAKpB,uBACF,CAEA,mBAEE,kCAAmC,CADnC,0BAA2B,CAE3B,0CACF,CAEA,yBACE,aACE,aACF,CACF,CC1GA,gBAEI,kBAAmB,CAGnB,+BAAgC,CAJhC,YAAa,CAGb,eAAgB,CADhB,iBAGF,CAEA,6CAUE,uCAAwC,CAJxC,gBAAuB,CADvB,uBAAwB,CAHxB,UAAW,CAOX,kBAAmB,CALnB,gBAAiB,CAGjB,iBAAkB,CAJlB,eAAgB,CAKhB,UAGF,CAEA,sBAEE,UAAW,CADX,SAEF,CAEA,uBACE,gBAAuB,CACvB,WAAY,CACZ,OACF,CAEA,cAIE,6CAA+C,CAH/C,QAAO,CACP,SAAU,CACV,0BAEF,CAEA,iBAKE,uBAAwB,CAJxB,gBAAiB,CACjB,eAAgB,CAChB,gBAAiB,CACjB,kBAEF,CAEA,gBAKE,uBAAwB,CAHxB,cAAe,CACf,eAAgB,CAChB,gBAAiB,CAHjB,SAKF,CAEA,UAIE,kBAAmB,CAKnB,4CAA6C,CAP7C,YAAa,CADb,QAAO,CAEP,qBAAsB,CAEtB,QAAS,CACT,eAAgB,CAChB,SAAU,CACV,mBAEF,CAEA,cAEE,oBAAqB,CADrB,YAAa,CAEb,QACF,CAEA,kBAEE,uBAAyB,CADzB,WAEF,CAEA,WAKE,kBAAmB,CAMnB,mDAAqD,CAHrD,uCAAwC,CADxC,oBAAsB,CAJtB,YAAa,CADb,WAAY,CAEZ,sBAAuB,CAKvB,SAAU,CAHV,iBAAkB,CAIlB,0BAA2B,CAT3B,UAWF,CAEA,aAIE,oFAAuF,CADvF,SAAU,CAFV,iBAAkB,CAClB,QAGF,CAEA,aAIE,wFAA2F,CAH3F,iBAAkB,CAElB,UAAW,CADX,QAGF,CAEA,aAIE,sFAAyF,CAFzF,YAAa,CACb,UAAW,CAFX,iBAIF,CAEA,aAIE,wFAA2F,CAF3F,WAAY,CADZ,iBAAkB,CAElB,UAEF,CAEA,aAKE,yFAA4F,CAH5F,WAAY,CACZ,SAAU,CAFV,iBAAkB,CAGlB,0BAEF,CAEA,aAIE,wFAA2F,CAH3F,iBAAkB,CAElB,UAAW,CADX,SAGF,CAEA,eAEE,aAAc,CADd,YAEF,CAEA,yCAEE,+BAAgC,CAChC,0BACF,CAEA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAEA,mBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,uBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,mBACE,GACE,SAAU,CACV,mBACF,CACA,GACE,SAAU,CACV,kBACF,CACF,CAEA,iBACE,GACE,kBACF,CACA,IACE,oBACF,CACA,GACE,kBACF,CACF,CAEA,iBACE,MACE,uBACF,CACA,IACE,2BACF,CACF,CAEA,sBACE,MACE,wCACF,CACA,IACE,4CACF,CACF,CAEA,0BACE,iBACE,cAAe,CACf,gBACF,CAEA,gBACE,eAAiB,CACjB,kBACF,CAEA,kBACE,WACF,CAEA,WAEE,WAAY,CADZ,UAEF,CAEA,eACE,YACF,CACF,CAEA,yBACE,gBACE,qBACF,CAEA,UACE,aACF,CAEA,mBACE,eACF,CAEA,iCAGE,iBAAkB,CADlB,UAEF,CAEA,6CAEE,UAAW,CAEX,YAAa,CADb,WAEF,CAEA,sBAEE,MAAU,CADV,KAEF,CAEA,uBACE,WAAY,CACZ,OACF,CACF,CAEA,yBACE,iBACE,gBAAiB,CACjB,kBACF,CAEA,gBAEE,eAAiB,CADjB,UAEF,CAEA,kBACE,WACF,CAEA,WAEE,WAAY,CADZ,UAEF,CAEA,eAEE,aAAc,CADd,YAEF,CAEA,iBACE,cAAe,CACf,kBAAmB,CACnB,iBACF,CACF,CAEA,iBAOE,kBAAmB,CAHnB,uBAAwB,CAExB,YAAa,CALb,gBAAiB,CACjB,eAAgB,CAChB,gBAAiB,CAEjB,eAGF,CAEA,QAEE,0BAA2B,CAE3B,gBAAiB,CAHjB,eAAgB,CAEhB,eAAgB,CAEhB,2BACF,CAEA,gBACE,SACF,CAEA,eACE,SACF,CAEA,0BACE,iBACE,gBAAiB,CACjB,kBACF,CAEA,QACE,gBACF,CACF,CAEA,yBACE,iBACE,cAAe,CAGf,sBAAuB,CAFvB,kBAAmB,CACnB,iBAEF,CAEA,QACE,cACF,CACF,CAEA,yBACE,iBACE,eAAiB,CACjB,kBACF,CAEA,QACE,eACF,CACF,CCnXF,iBACE,aAAc,CACd,iBACF,CAEA,+CAME,+BAAgC,CADhC,uBAAwB,CAHxB,WAAY,CAOZ,kBAAmB,CALnB,YAAa,CAGb,iBAAkB,CAJlB,WAAY,CAKZ,UAEF,CAEA,uBAEE,WAAY,CADZ,SAEF,CAEA,wBACE,iCAAkC,CAClC,QAAY,CACZ,MACF,CAEA,oBAIE,uBAAwB,CAHxB,gBAAiB,CACjB,eAAgB,CAChB,oBAEF,CAEA,eAGE,sBAAuB,CAFvB,YAAa,CACb,QAEF,CAEA,mBACE,QAAO,CACP,aACF,CAEA,eAOE,6BAAoC,CAFpC,0BAA2B,CAC3B,4BAA6B,CAE7B,oBAAqB,CAPrB,cAAe,CACf,eAOF,CAEA,6BAPE,uBAAwB,CADxB,oBAcF,CANA,cACE,cAAe,CACf,eAAgB,CAGhB,kBACF,CAEA,mBAGE,8BAA8C,CAF9C,eAAgB,CAChB,gBAEF,CAEA,sBAOE,6BAAoC,CAFpC,0BAA2B,CAC3B,4BAA6B,CAE7B,oBAAqB,CAJrB,uBAAwB,CAHxB,gBAAiB,CACjB,eAAgB,CAChB,oBAMF,CAEA,iBAGE,kBAAmB,CAInB,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAiC,CAGjC,0BAA0C,CAD1C,kBAAmB,CAPnB,YAAa,CACb,qBAAsB,CAEtB,QAAS,CACT,cAAe,CAKf,uBACF,CAEA,uBAGE,iCAAkC,CADlC,gCAA0C,CAD1C,0BAGF,CAEA,gBAGE,mBAAqB,CACrB,eAAgB,CAHhB,oBAAqB,CACrB,uBAGF,CAEA,sBACE,qBACF,CAEA,eAIE,mBAAqB,CAHrB,aAAc,CAEd,WAAY,CADZ,cAAe,CAGf,uBACF,CAEA,uBAEE,uBAAwB,CADxB,eAAiB,CAKjB,eAAgB,CAFhB,QAAS,CACT,UAAY,CAFZ,iBAIF,CAEA,aAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,QACF,CAEA,0BACE,eACE,QACF,CAEA,eACE,gBACF,CAEA,cACE,gBACF,CAEA,sBACE,gBACF,CAEA,iBACE,cACF,CACF,CAEA,yBACE,eACE,qBAAsB,CACtB,QACF,CAEA,mBACE,cACF,CAEA,oBACE,gBAAiB,CACjB,kBACF,CAEA,eACE,gBAAiB,CACjB,iBACF,CAEA,cACE,eACF,CAEA,mBACE,iBAAkB,CAClB,kBACF,CAEA,sBACE,gBAAiB,CACjB,iBACF,CAEA,iBACE,YACF,CAEA,uBACE,gBACF,CAEA,+CAGE,YAAa,CADb,WAEF,CACF,CAEA,yBACE,iBACE,aACF,CAEA,eACE,QAEF,CAEA,4BAHE,kBAKF,CAEA,eACE,gBACF,CAEA,cACE,eAAiB,CACjB,eACF,CAEA,mBACE,eAAgB,CAChB,gBACF,CAEA,sBACE,cAAe,CACf,kBACF,CAEA,iBAEE,SAAW,CADX,aAEF,CAEA,uBACE,eACF,CACF,CCxPA,YAKE,kCAA2B,CAA3B,0BAA2B,CAD3B,8BAAmC,CAFnC,qCAAsC,CADtC,oBAAsB,CAOtB,eAAgB,CALhB,YAAa,CAIb,iBAAkB,CADlB,uBAGF,CAEA,kBAGE,mCAAoC,CADpC,gCAA8C,CAD9C,0BAGF,CAEA,mBAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,oBACF,CAEA,iBAGE,kBAAmB,CAKnB,0BAA2B,CAD3B,iBAAkB,CALlB,YAAa,CADb,cAAe,CAKf,WAAY,CAFZ,sBAAuB,CACvB,UAIF,CAEA,kBAGE,uBAAwB,CAFxB,gBAAiB,CACjB,eAAgB,CAEhB,QACF,CAEA,oBACE,iBACF,CAEA,iBACE,eAAgB,CAEhB,QAAS,CADT,SAEF,CAEA,iBAEE,kBAAmB,CAInB,uBAAwB,CALxB,YAAa,CAIb,gBAAkB,CAFlB,UAAY,CACZ,eAAiB,CAGjB,uBACF,CAEA,uBAEE,0BAA2B,CAD3B,yBAEF,CAEA,QACE,0BAA2B,CAE3B,gBAAiB,CADjB,eAAiB,CAEjB,cACF,CAEA,yBACE,YACE,cACF,CAEA,mBACE,UAAY,CACZ,kBACF,CAEA,iBACE,gBAAiB,CAEjB,aAAc,CADd,YAEF,CAEA,kBACE,gBACF,CAEA,iBACE,eAAiB,CACjB,eACF,CACF,CAEA,yBACE,YACE,eACF,CAEA,mBACE,qBAAsB,CAEtB,SAAW,CADX,iBAEF,CAEA,kBACE,cACF,CAEA,iBACE,gBACF,CACF,CCtHA,kBACI,aAAc,CACd,iBACF,CAEA,iDAME,+BAAgC,CADhC,uBAAwB,CAHxB,WAAY,CAOZ,kBAAmB,CALnB,YAAa,CAGb,iBAAkB,CAJlB,WAAY,CAKZ,UAEF,CAEA,wBAEE,UAAW,CADX,SAEF,CAEA,yBACE,iCAAkC,CAClC,QAAY,CACZ,OACF,CAEA,qBAIE,uBAAwB,CAHxB,gBAAiB,CACjB,eAAgB,CAChB,oBAEF,CAEA,gBAEE,sBAAuB,CADvB,YAAa,CAEb,QACF,CAEA,QAEE,aAAc,CADd,YAAa,CAGb,mCAAqC,CADrC,gCAEF,CAEA,aACE,QACF,CAEF,0BACI,QACI,iBACJ,CACA,wBACI,aACJ,CACJ,CAEA,yBACI,gBACI,qBAAsB,CACtB,QACJ,CAEA,qBACI,UACJ,CACA,qBACI,gBAAiB,CACjB,kBAEJ,CACA,iDAEI,YAAa,CADb,WAEJ,CAEJ,CAEA,yBACI,kBACI,SACJ,CACJ,CAEA,yBACE,QAEE,eAAgB,CADhB,yBAEF,CAEA,kBACE,aACF,CAEA,iDAGE,YAAa,CADb,WAEF,CACF,CCvGA,aAMI,kCAA2B,CAA3B,0BAA2B,CAD3B,8BAAmC,CAHnC,qCAAsC,CADtC,oBAAsB,CAOtB,cAAe,CALf,cAAe,CAIf,iBAAkB,CAHlB,iBAAkB,CAKlB,uBACF,CAEA,uCAEE,0BACF,CAEA,kBAGE,uBAAwB,CADxB,gBAAiB,CADjB,eAGF,CAEA,YAIE,kBAAmB,CADnB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CACvB,kBAAmB,CALnB,UAMF,CAEA,gBAIE,WAAY,CAFZ,eAAgB,CADhB,cAAe,CAIf,kBAAmB,CAFnB,UAGF,CCtCF,kBAKI,kCAA2B,CAA3B,0BAA2B,CAD3B,8BAAgC,CADhC,uCAAwC,CADxC,oBAAsB,CADtB,gBAKJ,CAEA,qBAOI,6BAAoC,CAHpC,0BAA2B,CAC3B,oBAAqB,CACrB,4BAA6B,CAE7B,8CAA+C,CAP/C,gBAAiB,CACjB,eAAgB,CAChB,kBAMJ,CAEA,oBACI,iBACJ,CAEA,YAEI,kBAAmB,CADnB,YAAa,CAEb,6BAEJ,CAEA,cACI,cAAe,CACf,eACJ,CAEA,wBACI,0BACJ,CAEA,mBAGI,kBAAmB,CACnB,mBAAqB,CAFrB,YAAc,CAGd,aAAc,CACd,eAAgB,CALhB,UAMJ,CAEA,gBAGI,0BAA2B,CAC3B,mBAAqB,CAFrB,YAAc,CAGd,8BAAgC,CAJhC,OAKJ,CCtDA,mBACI,aAAc,CACd,iBACJ,CAEA,sBACI,gBAAiB,CACjB,eAAgB,CAChB,kBACJ,CAEA,iBAKI,kBAAmB,CAJnB,YAAa,CACb,kBAAmB,CACnB,QAAS,CACT,sBAEJ,CAEA,mBAGI,iBAAkB,CAElB,qBAAsB,CAJtB,YAAa,CACb,oCAAsC,CAEtC,WAEJ,CAEA,yBACI,uCAAwC,CACxC,cACJ,CAEA,yBACI,iBACI,qBAAsB,CACtB,UACJ,CAEA,sBACI,gBAAiB,CACjB,eACJ,CACJ,CAEA,yBACE,mBACE,aACF,CAEA,mBAEE,aAAe,CADf,UAEF,CACF,CCrDA,sBAKI,kCAAmC,CAEnC,uCAAwC,CADxC,oBAAsB,CAJtB,qBAAsB,CAOtB,oBAAqB,CADrB,cAEJ,CAEA,kDARI,kBAAmB,CAHnB,YAAa,CAEb,sBAkBJ,CATA,4BAOI,iCAAkC,CADlC,mBAAqB,CAJrB,WAAY,CAMZ,mBAAqB,CAPrB,UAQJ,CAEA,UAEI,WAAY,CACZ,kBAAmB,CAFnB,YAGJ,CAEA,wBAGI,uBAAwB,CAFxB,cAAe,CACf,eAEJ,CCjCA,oBACI,aAAc,CACd,iBACJ,CAEA,uBACI,gBAAiB,CACjB,eAAgB,CAChB,kBACJ,CAEA,kBACI,YAAa,CACb,qBAAsB,CACtB,QACJ,CAGA,yBACI,kBACI,6BACJ,CAEA,uBACI,gBAAiB,CACjB,eACJ,CACJ,CAEA,yBACE,oBACE,aACF,CAEA,kBAEE,kBAAmB,CADnB,QAEF,CACF,CCtCA,cAEI,kBAAmB,CADnB,YAAa,CAGb,0BAA2B,CAD3B,kBAEF,CAEA,sBACE,0BACF,CAEA,gBAOE,kBAAmB,CACnB,qCAAsC,CALtC,kBAAmB,CACnB,YAAa,CACb,qBAAsB,CAHtB,YAAa,CAIb,sBAAuB,CAGvB,2EAAiF,CARjF,WASF,CAEA,oCAGE,uCAAwC,CADxC,+BAAyC,CADzC,2BAGF,CAEA,eAKE,WAAY,CAFZ,kBAAmB,CADnB,YAAa,CAEb,gBAAiB,CAHjB,WAKF,CAEA,eAKE,uBAAwB,CAJxB,gBAAiB,CACjB,eAAgB,CAChB,aAAe,CACf,iBAEF,CAEA,qBAGE,uBAAwB,CACxB,QAAO,CAFP,cAAe,CADf,gBAIF,CAEA,yBACE,cAEE,sBACF,CAEA,oCAJE,qBAMF,CAEA,qBACE,aAAc,CACd,eACF,CACF,CAEA,yBACE,gBAEE,YAAa,CADb,WAEF,CAEA,eAEE,YAAa,CADb,WAEF,CAEA,eACE,cACF,CAEA,qBACE,eACF,CACF", "sources": ["index.css", "App.css", "components/Navbar/Navbar.css", "components/Navbar/MobileNavbar/MobileNavbar.css", "components/Hero/Hero.css", "components/AboutMe/AboutMe.css", "components/AboutMe/AboutCard/AboutCard.css", "components/Skills/Skills.css", "components/Skills/SkillsCard/SkillsCard.css", "components/Skills/SkillsCard/SkillCardInfo/SkillCardInfo.css", "components/ContactMe/ContactMe.css", "components/ContactMe/ContactInfoCard/ContactInfoCard.css", "components/Projects/Projects.css", "components/Projects/ProjectCard/ProjectCard.css"], "sourcesContent": ["@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');\r\n\r\n* {\r\n  font-family: \"Poppins\", sans-serif;\r\n  margin: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* Light Theme */\r\n.light-theme {\r\n  --background-color: #dbd4d4;\r\n  --text-color: #000000;\r\n  --primary-color: #007bff; \r\n  --secondary-color: #0056b3; \r\n  --navbar-background: rgba(255, 255, 255, 0.1); \r\n  --navbar-blur: blur(50px); \r\n  --gradient: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);\r\n}\r\n/* Default Dark Theme */\r\n:root {\r\n  --background-color: #060417;\r\n  --text-color: #ffffff;\r\n  --primary-color: #a993fe; /* Purple for buttons and accents */\r\n  --secondary-color: #7e61e7; /* Darker purple for hover effects */\r\n  --navbar-background: rgba(0, 0, 0, 0.1); /* Semi-transparent black for navbar */\r\n  --navbar-blur: blur(50px); /* Blur effect for navbar */\r\n  --gradient: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);\r\n}\r\n\r\n\r\nbody {\r\n  background-color: var(--background-color);\r\n  color: var(--text-color);\r\n  transition: --background-color 0.3s ease, color 0.3s ease;\r\n}", ".container{\r\n  max-width: 1300px !important;\r\n  margin: 0 auto;   \r\n  position: relative;\r\n  \r\n\r\n}\r\n\r\nhtml{\r\n  scroll-behavior: smooth;\r\n}\r\n\r\n@media (max-width: 1300px){\r\n  .container{\r\n      padding: 0 1.5rem;\r\n\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n}\r\n", "/* Navbar.css */\r\n.nav-wrapper {\r\n  background: var(--navbar-background);\r\n  padding: 0 0;\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 30;\r\n  backdrop-filter: var(--navbar-blur);\r\n}\r\n\r\n.nav-content {\r\n  max-width: 1300px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  padding: 1rem 0;\r\n  margin: 0 auto;\r\n}\r\n\r\n.logo {\r\n  padding-top: 20px;\r\n  width: 14rem;\r\n  height: 200px;\r\n}\r\n\r\n.nav-content ul {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  list-style: none;\r\n}\r\n\r\n.nav-content li {\r\n  margin: 0 1.5rem;\r\n}\r\n\r\n.menu-item {\r\n  text-decoration: none;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  color: var(--text-color);\r\n  position: relative;\r\n  cursor: pointer;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.menu-item::before {\r\n  content: \"\";\r\n  width: 2rem;\r\n  height: 0.2rem;\r\n  background: var(--gradient);\r\n  border-radius: 0.5rem;\r\n  position: absolute;\r\n  bottom: -0.6rem;\r\n  left: 0%;\r\n  right: 0%;\r\n  opacity: 0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.menu-item:hover::before {\r\n  width: 100%;\r\n  opacity: 1;\r\n}\r\n\r\n.menu-item.active::before {\r\n  opacity: 1;\r\n  transform: translateX(-40%);\r\n}\r\n\r\n.contact-btn {\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: var(--text-color);\r\n  background: var(--gradient);\r\n  padding: 0.6rem 2rem;\r\n  border: none;\r\n  outline: 1.5px solid transparent;\r\n  border-radius: 0.2rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.theme-toggle {\r\n  background: none;\r\n  border: none;\r\n  color: var(--text-color);\r\n  cursor: pointer;\r\n  font-size: 1rem;\r\n  margin-right: 1rem;\r\n}\r\n\r\n.contact-btn:hover {\r\n  color: var(--primary-color);\r\n  background: var(--background-color);\r\n  outline: 1.5px solid var(--secondary-color);\r\n}\r\n\r\n.menu-btn {\r\n  width: 2.5rem;\r\n  height: 2.5rem;\r\n  font-size: 1.5rem;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: none;\r\n  border-radius: 0.4rem;\r\n  color: var(--text-color);\r\n  background: var(--gradient);\r\n  line-height: 0;\r\n  cursor: pointer;\r\n  transition: all 0.4s ease;\r\n  display: none;\r\n}\r\n\r\n.menu-btn:hover {\r\n  color: var(--primary-color);\r\n  background: var(--background-color);\r\n  border: 1px solid var(--primary-color);\r\n}\r\n\r\n.menu-btn .material-symbols-outlined {\r\n  font-size: 1.8rem;\r\n}\r\n\r\n@media (max-width: 769px) {\r\n  .menu-btn {\r\n    display: block;\r\n  }\r\n\r\n  .nav-content ul {\r\n    display: none;\r\n  }\r\n}\r\n\r\n@media (max-width: 1325px) {\r\n  .nav-wrapper {\r\n    padding: 0 2rem;\r\n  }\r\n}", ".mobile-menu {\r\n  width: 100vw;\r\n  height: 100vh;\r\n  display: none;\r\n  background-color: rgba(0, 0, 0, 0.3); \r\n  position: fixed;\r\n  left: 0;\r\n  top: 0;\r\n  z-index: 999 !important;\r\n  opacity: 0;\r\n  box-shadow: 0px 30px 80px rgba(0, 0, 0, 0.3);\r\n  transition: all 0.3s ease;\r\n  transform: translateX(100vw);\r\n}\r\n\r\n.mobile-menu-container {\r\n  width: 60vw;\r\n  height: 100vh;\r\n  background-color: var(--background-color); /* Use background color variable */\r\n  padding: 2rem;\r\n  margin-left:auto;\r\n  margin-right: 0;\r\n}\r\n\r\n.mobile-menu.active {\r\n  opacity: 1;\r\n  transform: translateX(0);\r\n}\r\n\r\n.mobile-menu img {\r\n  margin-bottom: 3rem;\r\n}\r\n\r\n.mobile-menu ul {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n  list-style: none;\r\n  margin-left: -2rem;\r\n  margin-bottom: 3rem;\r\n}\r\n\r\n.menu-item {\r\n  text-decoration: none;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  color: var(--text-color); /* Use text color variable */\r\n  position: relative;\r\n  cursor: pointer;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.menu-item::before {\r\n  content: \"\";\r\n  width: 2rem;\r\n  height: 0.2rem;\r\n  background: var(--gradient); /* Use gradient variable */\r\n  border-radius: 0.5rem;\r\n  position: absolute;\r\n  bottom: -0.6rem;\r\n  opacity: 0;\r\n  transform: translateX(-1.5rem);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.menu-item:hover::before {\r\n  width: 100%;\r\n  transform: translateX(0);\r\n  opacity: 1;\r\n}\r\n\r\n.menu-item.active {\r\n  color: var(--primary-color); /* Use primary color variable */\r\n}\r\n\r\n.menu-item.active::before {\r\n  opacity: 1;\r\n  transform: translateX(0);\r\n}\r\n\r\n.contact-btn {\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: var(--text-color); /* Use text color variable */\r\n  background: var(--gradient); /* Use gradient variable */\r\n  padding: 0.6rem 2rem;\r\n  border: none;\r\n  outline: 1.5px solid transparent;\r\n  border-radius: 0.2rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.contact-btn:hover {\r\n  color: var(--primary-color); /* Use primary color variable */\r\n  background: var(--background-color); /* Use background color variable */\r\n  outline: 1.5px solid var(--secondary-color); /* Use secondary color variable */\r\n}\r\n\r\n@media (max-width: 769px) {\r\n  .mobile-menu {\r\n    display: block;\r\n  }\r\n}", ".hero-container {\r\n    display: flex;\r\n    align-items: center;\r\n    position: relative;\r\n    overflow: hidden;\r\n    animation: fadeIn 1s ease-in-out;\r\n  }\r\n  \r\n  .hero-container::after,\r\n  .hero-container::before {\r\n    content: \"\";\r\n    width: 28.125rem;\r\n    height: 28.125rem;\r\n    border-radius: 28.125rem;\r\n    background: transparent; /* Use primary color variable */\r\n    position: absolute;\r\n    z-index: -1;\r\n    filter: blur(255px);\r\n    animation: pulse 6s infinite ease-in-out;\r\n  }\r\n  \r\n  .hero-container::after {\r\n    top: -3rem;\r\n    left: -5rem;\r\n  }\r\n  \r\n  .hero-container::before {\r\n    background: transparent; /* Use secondary color variable */\r\n    bottom: 2rem;\r\n    right: 0rem;\r\n  }\r\n  \r\n  .hero-content {\r\n    flex: 1;\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n    animation: slideUp 1s ease-in-out forwards 0.5s;\r\n  }\r\n  \r\n  .hero-content h2 {\r\n    font-size: 3.8rem;\r\n    font-weight: 600;\r\n    line-height: 5rem;\r\n    margin-bottom: 1rem;\r\n    color: var(--text-color); /* Use text color variable */\r\n  }\r\n  \r\n  .hero-content p {\r\n    width: 80%;\r\n    font-size: 1rem;\r\n    font-weight: 400;\r\n    line-height: 2rem;\r\n    color: var(--text-color); /* Use text color variable */\r\n  }\r\n  \r\n  .hero-img {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 2rem;\r\n    margin-top: 5rem;\r\n    opacity: 0;\r\n    transform: scale(0.9);\r\n    animation: scaleIn 1s ease-in-out forwards 1s;\r\n  }\r\n  \r\n  .hero-img > div {\r\n    display: flex;\r\n    align-items: flex-end;\r\n    gap: 2rem;\r\n  }\r\n  \r\n  .hero-img > div > img {\r\n    width: 25rem;\r\n    transition: all 0.3s ease;\r\n  }\r\n  \r\n  .tech-icon {\r\n    width: 4rem;\r\n    height: 4rem;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    text-align: center;\r\n    border-radius: 0.65rem;\r\n    border: 1.5px solid var(--primary-color); /* Use primary color variable */\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n    animation: fadeSlideUp 0.8s ease-in-out forwards 1.2s;\r\n  }\r\n  \r\n  .tech-icon-1 {\r\n    position: absolute;\r\n    top: 20px;\r\n    left: 30px;\r\n    animation: fadeSlideUp 0.8s ease-in-out forwards 1.2s, float 3s ease-in-out infinite 2s;\r\n  }\r\n\r\n  .tech-icon-2 {\r\n    position: absolute;\r\n    top: 60px;\r\n    right: 60px;\r\n    animation: fadeSlideUp 0.8s ease-in-out forwards 1.4s, float 3.2s ease-in-out infinite 2.5s;\r\n  }\r\n\r\n  .tech-icon-3 {\r\n    position: absolute;\r\n    bottom: 170px;\r\n    left: -20px;\r\n    animation: fadeSlideUp 0.8s ease-in-out forwards 1.6s, float 2.8s ease-in-out infinite 3s;\r\n  }\r\n\r\n  .tech-icon-4 {\r\n    position: absolute;\r\n    bottom: 70px;\r\n    right: 30px;\r\n    animation: fadeSlideUp 0.8s ease-in-out forwards 1.8s, float 3.5s ease-in-out infinite 3.5s;\r\n  }\r\n\r\n  .tech-icon-5 {\r\n    position: absolute;\r\n    bottom: 15px;\r\n    left: 30px;\r\n    transform: translateX(-50%);\r\n    animation: fadeSlideUp 0.8s ease-in-out forwards 2s, floatWithX 2.9s ease-in-out infinite 4s;\r\n  }\r\n\r\n  .tech-icon-6 {\r\n    position: absolute;\r\n    top: 190px;\r\n    right: 20px;\r\n    animation: fadeSlideUp 0.8s ease-in-out forwards 2.2s, float 3.3s ease-in-out infinite 4.5s;\r\n  }\r\n  \r\n  .tech-icon img {\r\n    width: 2.5rem;\r\n    height: 2.5rem;\r\n  }\r\n  \r\n  .hero-img > div > img:hover,\r\n  .tech-icon:hover {\r\n    background: var(--primary-color); /* Use primary color variable */\r\n    transition: all 0.2s ease-in;\r\n  }\r\n  \r\n  @keyframes fadeIn {\r\n    0% {\r\n      opacity: 0;\r\n    }\r\n    100% {\r\n      opacity: 1;\r\n    }\r\n  }\r\n  \r\n  @keyframes slideUp {\r\n    0% {\r\n      opacity: 0;\r\n      transform: translateY(30px);\r\n    }\r\n    100% {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n  \r\n  @keyframes fadeSlideUp {\r\n    0% {\r\n      opacity: 0;\r\n      transform: translateY(30px);\r\n    }\r\n    100% {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n  \r\n  @keyframes scaleIn {\r\n    0% {\r\n      opacity: 0;\r\n      transform: scale(0.9);\r\n    }\r\n    100% {\r\n      opacity: 1;\r\n      transform: scale(1);\r\n    }\r\n  }\r\n  \r\n  @keyframes pulse {\r\n    0% {\r\n      transform: scale(1);\r\n    }\r\n    50% {\r\n      transform: scale(1.1);\r\n    }\r\n    100% {\r\n      transform: scale(1);\r\n    }\r\n  }\r\n\r\n  @keyframes float {\r\n    0%, 100% {\r\n      transform: translateY(0px);\r\n    }\r\n    50% {\r\n      transform: translateY(-10px);\r\n    }\r\n  }\r\n\r\n  @keyframes floatWithX {\r\n    0%, 100% {\r\n      transform: translateX(-50%) translateY(0px);\r\n    }\r\n    50% {\r\n      transform: translateX(-50%) translateY(-10px);\r\n    }\r\n  }\r\n  \r\n  @media (max-width: 1025px) {\r\n    .hero-content h2 {\r\n      font-size: 3rem;\r\n      line-height: 4rem;\r\n    }\r\n  \r\n    .hero-content p {\r\n      font-size: 0.9rem;\r\n      line-height: 1.6rem;\r\n    }\r\n  \r\n    .hero-img > div > img {\r\n      width: 20rem;\r\n    }\r\n  \r\n    .tech-icon {\r\n      width: 4rem;\r\n      height: 4rem;\r\n    }\r\n  \r\n    .tech-icon img {\r\n      width: 2.5rem;\r\n    }\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    .hero-container {\r\n      flex-direction: column;\r\n    }\r\n  \r\n    .hero-img {\r\n      margin: 2rem 0 2rem 0;\r\n    }\r\n  \r\n    .hero-container h2 {\r\n      margin-top: 3rem;\r\n    }\r\n  \r\n    .hero-content h2,\r\n    .hero-content p {\r\n      width: auto;\r\n      text-align: center;\r\n    }\r\n  \r\n    .hero-container::after,\r\n    .hero-container::before {\r\n      content: \"\";\r\n      width: 18rem;\r\n      height: 18rem;\r\n    }\r\n  \r\n    .hero-container::after {\r\n      top: 0rem;\r\n      left: 0rem;\r\n    }\r\n  \r\n    .hero-container::before {\r\n      bottom: 2rem;\r\n      right: 0rem;\r\n    }\r\n  }\r\n  \r\n  @media (max-width: 480px) {\r\n    .hero-content h2 {\r\n      font-size: 2.5rem;\r\n      line-height: 3.2rem;\r\n    }\r\n  \r\n    .hero-content p {\r\n      width: 100%;\r\n      font-size: 0.9rem;\r\n    }\r\n  \r\n    .hero-img > div > img {\r\n      width: 18rem;\r\n    }\r\n  \r\n    .tech-icon {\r\n      width: 3rem;\r\n      height: 3rem;\r\n    }\r\n  \r\n    .tech-icon img {\r\n      width: 1.8rem;\r\n      height: 1.8rem;\r\n    }\r\n  \r\n    .typewriter-text {\r\n      font-size: 1rem;\r\n      line-height: 1.5rem;\r\n      text-align: center;\r\n    }\r\n  }\r\n  \r\n  .typewriter-text {\r\n    font-size: 1.2rem;\r\n    font-weight: 400;\r\n    line-height: 2rem;\r\n    color: var(--text-color);\r\n    min-height: 2rem;\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .cursor {\r\n    font-weight: 100;\r\n    color: var(--primary-color);\r\n    margin-left: 2px;\r\n    font-size: 1.2rem;\r\n    transition: opacity 0.1s ease;\r\n  }\r\n\r\n  .cursor.visible {\r\n    opacity: 1;\r\n  }\r\n\r\n  .cursor.hidden {\r\n    opacity: 0;\r\n  }\r\n\r\n  @media (max-width: 1025px) {\r\n    .typewriter-text {\r\n      font-size: 1.1rem;\r\n      line-height: 1.8rem;\r\n    }\r\n\r\n    .cursor {\r\n      font-size: 1.1rem;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 768px) {\r\n    .typewriter-text {\r\n      font-size: 1rem;\r\n      line-height: 1.6rem;\r\n      text-align: center;\r\n      justify-content: center;\r\n    }\r\n\r\n    .cursor {\r\n      font-size: 1rem;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    .typewriter-text {\r\n      font-size: 0.9rem;\r\n      line-height: 1.4rem;\r\n    }\r\n\r\n    .cursor {\r\n      font-size: 0.9rem;\r\n    }\r\n  }\r\n", ".about-container {\n  margin: 4rem 0;\n  position: relative;\n}\n\n.about-container::after,\n.about-container::before {\n  content: \" \";\n  width: 28rem;\n  height: 28rem;\n  border-radius: 28.125rem;\n  background: var(--primary-color);\n  position: absolute;\n  z-index: -1;\n  filter: blur(200px);\n}\n\n.about-container::after {\n  top: -3rem;\n  right: -5rem;\n}\n\n.about-container::before {\n  background: var(--secondary-color);\n  bottom: 0rem;\n  left: 0rem;\n}\n\n.about-container h5 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 3.5rem;\n  color: var(--text-color);\n}\n\n.about-content {\n  display: flex;\n  gap: 4rem;\n  align-items: flex-start;\n}\n\n.about-description {\n  flex: 1;\n  max-width: 50%;\n}\n\n.about-text h3 {\n  font-size: 2rem;\n  font-weight: 600;\n  margin-bottom: 1.5rem;\n  color: var(--text-color);\n  background: var(--gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.about-text p {\n  font-size: 1rem;\n  line-height: 1.8;\n  margin-bottom: 1.5rem;\n  color: var(--text-color);\n  text-align: justify;\n}\n\n.typeracer-section {\n  margin-top: 2rem;\n  padding-top: 2rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.typeracer-section h4 {\n  font-size: 1.3rem;\n  font-weight: 600;\n  margin-bottom: 1.5rem;\n  color: var(--text-color);\n  background: var(--gradient);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.typeracer-badge {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n  padding: 1.5rem;\n  background: rgba(22, 17, 47, 0.3);\n  backdrop-filter: blur(10px);\n  border-radius: 1rem;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  transition: all 0.3s ease;\n}\n\n.typeracer-badge:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n  border-color: var(--primary-color);\n}\n\n.typeracer-link {\n  text-decoration: none;\n  transition: all 0.3s ease;\n  border-radius: 0.5rem;\n  overflow: hidden;\n}\n\n.typeracer-link:hover {\n  transform: scale(1.05);\n}\n\n.typeracer-img {\n  display: block;\n  max-width: 100%;\n  height: auto;\n  border-radius: 0.5rem;\n  transition: all 0.3s ease;\n}\n\n.typeracer-description {\n  font-size: 0.9rem;\n  color: var(--text-color);\n  text-align: center;\n  margin: 0;\n  opacity: 0.8;\n  line-height: 1.5;\n}\n\n.about-cards {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n\n@media (max-width: 1025px) {\n  .about-content {\n    gap: 3rem;\n  }\n  \n  .about-text h3 {\n    font-size: 1.8rem;\n  }\n  \n  .about-text p {\n    font-size: 0.95rem;\n  }\n\n  .typeracer-section h4 {\n    font-size: 1.2rem;\n  }\n\n  .typeracer-badge {\n    padding: 1.2rem;\n  }\n}\n\n@media (max-width: 768px) {\n  .about-content {\n    flex-direction: column;\n    gap: 3rem;\n  }\n  \n  .about-description {\n    max-width: 100%;\n  }\n  \n  .about-container h5 {\n    font-size: 1.3rem;\n    margin-bottom: 2rem;\n  }\n  \n  .about-text h3 {\n    font-size: 1.6rem;\n    text-align: center;\n  }\n  \n  .about-text p {\n    text-align: left;\n  }\n\n  .typeracer-section {\n    margin-top: 1.5rem;\n    padding-top: 1.5rem;\n  }\n\n  .typeracer-section h4 {\n    font-size: 1.1rem;\n    text-align: center;\n  }\n\n  .typeracer-badge {\n    padding: 1rem;\n  }\n\n  .typeracer-description {\n    font-size: 0.85rem;\n  }\n  \n  .about-container::after,\n  .about-container::before {\n    width: 18rem;\n    height: 18rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .about-container {\n    margin: 2rem 0;\n  }\n\n  .about-content {\n    gap: 2rem;\n    align-items: center;\n  }\n\n  .about-cards {\n    align-items: center;\n  }\n  \n  .about-text h3 {\n    font-size: 1.4rem;\n  }\n  \n  .about-text p {\n    font-size: 0.9rem;\n    line-height: 1.6;\n  }\n\n  .typeracer-section {\n    margin-top: 1rem;\n    padding-top: 1rem;\n  }\n\n  .typeracer-section h4 {\n    font-size: 1rem;\n    margin-bottom: 1rem;\n  }\n\n  .typeracer-badge {\n    padding: 0.8rem;\n    gap: 0.8rem;\n  }\n\n  .typeracer-description {\n    font-size: 0.8rem;\n  }\n}\n", ".about-card {\n  border-radius: 0.65rem;\n  border: 1px solid var(--primary-color);\n  padding: 2rem;\n  background: rgba(22, 17, 47, 0.398);\n  backdrop-filter: blur(1rem);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.about-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 10px 30px rgba(0, 123, 255, 0.2);\n  border-color: var(--secondary-color);\n}\n\n.about-card-header {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n}\n\n.about-card-icon {\n  font-size: 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 3rem;\n  height: 3rem;\n  border-radius: 50%;\n  background: var(--gradient);\n}\n\n.about-card-title {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: var(--text-color);\n  margin: 0;\n}\n\n.about-card-content {\n  position: relative;\n}\n\n.about-card-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.about-card-item {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 0.5rem 0;\n  font-size: 0.95rem;\n  color: var(--text-color);\n  transition: all 0.2s ease;\n}\n\n.about-card-item:hover {\n  transform: translateX(5px);\n  color: var(--primary-color);\n}\n\n.bullet {\n  color: var(--primary-color);\n  font-weight: bold;\n  font-size: 1.2rem;\n  min-width: 1rem;\n}\n\n@media (max-width: 768px) {\n  .about-card {\n    padding: 1.5rem;\n  }\n  \n  .about-card-header {\n    gap: 0.75rem;\n    margin-bottom: 1rem;\n  }\n  \n  .about-card-icon {\n    font-size: 1.5rem;\n    width: 2.5rem;\n    height: 2.5rem;\n  }\n  \n  .about-card-title {\n    font-size: 1.1rem;\n  }\n  \n  .about-card-item {\n    font-size: 0.9rem;\n    padding: 0.4rem 0;\n  }\n}\n\n@media (max-width: 480px) {\n  .about-card {\n    padding: 1.25rem;\n  }\n  \n  .about-card-header {\n    flex-direction: column;\n    text-align: center;\n    gap: 0.5rem;\n  }\n  \n  .about-card-title {\n    font-size: 1rem;\n  }\n  \n  .about-card-item {\n    font-size: 0.85rem;\n  }\n}\n", ".skills-container {\r\n    margin: 4rem 0;\r\n    position: relative;\r\n  }\r\n  \r\n  .skills-container::after,\r\n  .skills-container::before {\r\n    content: \" \";\r\n    width: 28rem;\r\n    height: 28rem;\r\n    border-radius: 28.125rem;\r\n    background: var(--primary-color); /* Use primary color variable */\r\n    position: absolute;\r\n    z-index: -1;\r\n    filter: blur(200px);\r\n  }\r\n  \r\n  .skills-container::after {\r\n    top: -3rem;\r\n    left: -5rem;\r\n  }\r\n  \r\n  .skills-container::before {\r\n    background: var(--secondary-color); /* Use secondary color variable */\r\n    bottom: 0rem;\r\n    right: 0rem;\r\n  }\r\n  \r\n  .skills-container h5 {\r\n    font-size: 1.5rem;\r\n    font-weight: 600;\r\n    margin-bottom: 3.5rem;\r\n    color: var(--text-color); /* Use text color variable */\r\n  }\r\n  \r\n  .skills-content {\r\n    display: flex;\r\n    align-items: flex-start;\r\n    gap: 3rem;\r\n  }\r\n  \r\n  .skills {\r\n    display: grid;\r\n    grid-gap: 3rem;\r\n    grid-template-rows: repeat(2, 1fr);\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n  \r\n  .skills-info {\r\n    flex: 1;\r\n  }\r\n\r\n@media (max-width: 1025px){\r\n    .skills{\r\n        padding-left: 1rem;\r\n    }\r\n    .skills,.skills-content{\r\n        grid-gap: 2rem;\r\n    }\r\n}\r\n\r\n@media (max-width : 768px){\r\n    .skills-content{\r\n        flex-direction: column;\r\n        gap: 3rem;\r\n    }\r\n\r\n    .skills,.skills-info{\r\n        width: 100%;\r\n    }\r\n    .skills-container h5{\r\n        font-size: 1.3rem;\r\n        margin-bottom: 2rem;\r\n\r\n    }\r\n    .skills-container::after,.skills-container::before{\r\n        width: 18rem;\r\n        height: 18rem;\r\n    }\r\n\r\n}\r\n\r\n@media (max-width:600px){\r\n    .skills-container{\r\n        padding: 0;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .skills {\r\n    grid-template-columns: 1fr;\r\n    grid-gap: 1.5rem;\r\n  }\r\n  \r\n  .skills-container {\r\n    margin: 2rem 0;\r\n  }\r\n  \r\n  .skills-container::after,\r\n  .skills-container::before {\r\n    width: 15rem;\r\n    height: 15rem;\r\n  }\r\n}\r\n", ".skills-card {\r\n    border-radius: 0.65rem;\r\n    border: 1px solid var(--primary-color);\r\n    padding: 2.5rem;\r\n    text-align: center;\r\n    background: rgba(22, 17, 47, 0.398);\r\n    backdrop-filter: blur(1rem);\r\n    position: relative;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n  }\r\n  \r\n  .skills-card:hover,\r\n  .skills-card.active {\r\n    background: var(--gradient);\r\n  }\r\n  \r\n  .skills-card span {\r\n    font-weight: 500;\r\n    font-size: 1.3rem;\r\n    color: var(--text-color); /* Use text color variable */\r\n  }\r\n  \r\n  .skill-icon {\r\n    width: 6rem;\r\n    height: 6rem;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin: 0 auto 1rem; /* Center the icon */\r\n  }\r\n\r\n  .skill-icon img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n    width: auto;\r\n    height: auto;\r\n    object-fit: contain;\r\n  }\r\n", ".skills-info-card{\r\n    min-height: 23rem;\r\n    border-radius: 0.65rem;\r\n    border: 1.5px solid var(--primary-color);\r\n    background: rgba(22,17,47,0.398);\r\n    backdrop-filter: blur(1rem);\r\n}\r\n\r\n.skills-info-card h6{\r\n    font-size: 1.2rem;\r\n    font-weight: 500;\r\n    padding: 0.8rem 2rem;\r\n    background: var(--gradient);\r\n    background-clip: text;\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    border-bottom: 1.5px solid var(--primary-color);\r\n}\r\n\r\n.skill-info-content{\r\n    padding: 2rem 4rem;\r\n}\r\n\r\n.skill-info{\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n}\r\n\r\n.skill-info p{\r\n    font-size: 1rem;\r\n    font-weight: 500;\r\n}\r\n\r\n.skill-info .percentage{\r\n    color: var(--primary-color);\r\n}\r\n\r\n.skill-progress-bg{\r\n    width: 100%;\r\n    height: 0.5rem;\r\n    background: #382e68;\r\n    border-radius: 0.5rem;\r\n    margin: 1rem 0;\r\n    overflow: hidden;\r\n}\r\n\r\n.skill-progress{\r\n    width: 0%;\r\n    height: 0.5rem;\r\n    background: var(--gradient);\r\n    border-radius: 0.5rem;\r\n    transition: all 0.5s ease-in-out;\r\n}\r\n", ".contact-container {\r\n    margin: 4rem 0;\r\n    position: relative;\r\n}\r\n\r\n.contact-container h5 {\r\n    font-size: 1.5rem;\r\n    font-weight: 500;\r\n    margin-bottom: 3rem;\r\n}\r\n\r\n.contact-content {\r\n    display: flex; \r\n    flex-direction: row; \r\n    gap: 4rem; \r\n    justify-content: center; \r\n    align-items: center; \r\n}\r\n\r\n.contact-info-card {\r\n    padding: 1rem;\r\n    transition: background-color 0.3s ease;\r\n    border-radius: 8px;\r\n    width: 400px; \r\n    box-sizing: border-box;\r\n}\r\n\r\n.contact-info-card:hover {\r\n    background-color: var(--secondary-color);\r\n    cursor: pointer;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .contact-content {\r\n        flex-direction: column; \r\n        gap: 1.5rem;\r\n    }\r\n\r\n    .contact-container h5 {\r\n        font-size: 1.3rem;\r\n        margin-bottom: 0rem;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .contact-container {\r\n    margin: 2rem 0;\r\n  }\r\n  \r\n  .contact-info-card {\r\n    width: 100%;\r\n    padding: 0.5rem;\r\n  }\r\n}\r\n", ".contact-details-card{\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background: var(--background-color);\r\n    border-radius: 0.65rem;\r\n    border: 1.5px solid var(--primary-color);\r\n    padding: 3.5rem;\r\n    margin-bottom: 2.5rem;\r\n}\r\n\r\n.contact-details-card .icon{\r\n    width: 5rem;\r\n    height: 5rem;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 0.5rem;\r\n    background: var(--secondary-color);\r\n    margin-bottom: 0.8rem;\r\n}\r\n\r\n.icon img{\r\n    width: 1.8rem;\r\n    height: auto;\r\n    object-fit: contain;\r\n}\r\n\r\n.contact-details-card p{\r\n    font-size: 1rem;\r\n    font-weight: 400;\r\n    color: var(--text-color);\r\n}\r\n", ".projects-container {\r\n    margin: 4rem 0;\r\n    position: relative;\r\n}\r\n\r\n.projects-container h5 {\r\n    font-size: 1.5rem;\r\n    font-weight: 500;\r\n    margin-bottom: 3rem;\r\n}\r\n\r\n.projects-content {\r\n    display: flex;\r\n    flex-direction: column; \r\n    gap: 3rem; \r\n}\r\n\r\n\r\n@media (max-width: 768px) {\r\n    .projects-content {\r\n        flex-direction: column-reverse;\r\n    }\r\n\r\n    .projects-container h5 {\r\n        font-size: 1.3rem;\r\n        margin-bottom: 0rem;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .projects-container {\r\n    margin: 2rem 0;\r\n  }\r\n\r\n  .projects-content {\r\n    gap: 2rem;\r\n    align-items: center;\r\n  }\r\n}\r\n", ".project-card {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 2rem;\r\n    justify-content: flex-start;\r\n  }\r\n  \r\n  .project-card.reverse {\r\n    flex-direction: row-reverse;\r\n  }\r\n  \r\n  .project-square {\r\n    width: 250px;\r\n    height: 250px;\r\n    border-radius: 16px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background-color: var(--primary-color); \r\n    transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;\r\n  }\r\n  \r\n  .project-card:hover .project-square {\r\n    transform: translateY(-10px);\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n    background-color: var(--secondary-color); \r\n  }\r\n  \r\n  .project-image {\r\n    width: 250px;\r\n    height: 200px;\r\n    border-radius: 16px;\r\n    object-fit: cover;\r\n    border: none;\r\n  }\r\n  \r\n  .project-title {\r\n    font-size: 1.2rem;\r\n    font-weight: 600;\r\n    padding: 0.5rem;\r\n    text-align: center;\r\n    color: var(--text-color); /* Use text color variable */\r\n  }\r\n  \r\n  .project-description {\r\n    margin-left: 2rem;\r\n    font-size: 1rem;\r\n    color: var(--text-color); /* Use text color variable */\r\n    flex: 1;\r\n  }\r\n  \r\n  @media (max-width: 768px) {\r\n    .project-card {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n    }\r\n  \r\n    .project-card.reverse {\r\n      flex-direction: column;\r\n    }\r\n  \r\n    .project-description {\r\n      margin-left: 0;\r\n      margin-top: 1rem;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 480px) {\r\n    .project-square {\r\n      width: 200px;\r\n      height: 200px;\r\n    }\r\n    \r\n    .project-image {\r\n      width: 200px;\r\n      height: 160px;\r\n    }\r\n    \r\n    .project-title {\r\n      font-size: 1rem;\r\n    }\r\n    \r\n    .project-description {\r\n      font-size: 0.9rem;\r\n    }\r\n  }\r\n"], "names": [], "sourceRoot": ""}