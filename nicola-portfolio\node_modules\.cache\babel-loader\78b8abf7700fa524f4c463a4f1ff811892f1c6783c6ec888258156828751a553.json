{"ast": null, "code": "import { CompressedTexture, CubeReflectionMapping } from \"three\";\nclass CompressedCubeTexture extends CompressedTexture {\n  constructor(images, format, type) {\n    super(void 0, images[0].width, images[0].height, format, type, CubeReflectionMapping);\n    this.isCompressedCubeTexture = true;\n    this.isCubeTexture = true;\n    this.image = images;\n  }\n}\nexport { CompressedCubeTexture };", "map": {"version": 3, "names": ["CompressedCubeTexture", "CompressedTexture", "constructor", "images", "format", "type", "width", "height", "CubeReflectionMapping", "isCompressedCubeTexture", "isCubeTexture", "image"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\_polyfill\\CompressedCubeTexture.js"], "sourcesContent": ["import { CompressedTexture, CubeReflectionMapping } from 'three'\n\nclass CompressedCubeTexture extends CompressedTexture {\n  constructor(images, format, type) {\n    super(undefined, images[0].width, images[0].height, format, type, CubeReflectionMapping)\n\n    this.isCompressedCubeTexture = true\n    this.isCubeTexture = true\n\n    this.image = images\n  }\n}\n\nexport { CompressedCubeTexture }\n"], "mappings": ";AAEA,MAAMA,qBAAA,SAA8BC,iBAAA,CAAkB;EACpDC,YAAYC,MAAA,EAAQC,MAAA,EAAQC,IAAA,EAAM;IAChC,MAAM,QAAWF,MAAA,CAAO,CAAC,EAAEG,KAAA,EAAOH,MAAA,CAAO,CAAC,EAAEI,MAAA,EAAQH,MAAA,EAAQC,IAAA,EAAMG,qBAAqB;IAEvF,KAAKC,uBAAA,GAA0B;IAC/B,KAAKC,aAAA,GAAgB;IAErB,KAAKC,KAAA,GAAQR,MAAA;EACd;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}