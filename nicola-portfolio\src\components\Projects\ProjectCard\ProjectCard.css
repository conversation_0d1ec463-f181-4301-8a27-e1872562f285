.project-card {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    justify-content: flex-start;
  }
  
  .project-card.reverse {
    flex-direction: row-reverse;
  }
  
  .project-square {
    width: 250px;
    height: 250px;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: var(--primary-color); 
    transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
  }
  
  .project-card:hover .project-square {
    transform: translateY(-10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    background-color: var(--secondary-color); 
  }
  
  .project-image {
    width: 250px;
    height: 200px;
    border-radius: 16px;
    object-fit: cover;
    border: none;
  }
  
  .project-title {
    font-size: 1.2rem;
    font-weight: 600;
    padding: 0.5rem;
    text-align: center;
    color: var(--text-color); /* Use text color variable */
  }
  
  .project-description {
    margin-left: 2rem;
    font-size: 1rem;
    color: var(--text-color); /* Use text color variable */
    flex: 1;
  }
  
  @media (max-width: 768px) {
    .project-card {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .project-card.reverse {
      flex-direction: column;
    }
  
    .project-description {
      margin-left: 0;
      margin-top: 1rem;
    }
  }

  @media (max-width: 480px) {
    .project-square {
      width: 200px;
      height: 200px;
    }
    
    .project-image {
      width: 200px;
      height: 160px;
    }
    
    .project-title {
      font-size: 1rem;
    }
    
    .project-description {
      font-size: 0.9rem;
    }
  }
