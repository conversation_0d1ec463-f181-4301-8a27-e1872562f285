{"homepage": "https://Nicholass206.github.io/nicola-portfolio", "name": "nicola-portfolio", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "gh-pages": "^6.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-scripts": "5.0.1", "react-scroll": "^1.9.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "predeploy": "npm run build", "deploy": "gh-pages -d build", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}