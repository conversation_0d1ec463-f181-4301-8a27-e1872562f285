{"ast": null, "code": "import { OrthographicCamera, Scene, StereoCamera, WebGLRenderTarget, ShaderMaterial, Mesh, PlaneGeometry, LinearFilter, NearestFilter, RGBAFormat } from \"three\";\nimport { version } from \"../_polyfill/constants.js\";\nclass ParallaxBarrierEffect {\n  constructor(renderer) {\n    const _camera = new OrthographicCamera(-1, 1, 1, -1, 0, 1);\n    const _scene = new Scene();\n    const _stereo = new StereoCamera();\n    const _params = {\n      minFilter: LinearFilter,\n      magFilter: NearestFilter,\n      format: RGBAFormat\n    };\n    const _renderTargetL = new WebGLRenderTarget(512, 512, _params);\n    const _renderTargetR = new WebGLRenderTarget(512, 512, _params);\n    const _material = new ShaderMaterial({\n      uniforms: {\n        mapLeft: {\n          value: _renderTargetL.texture\n        },\n        mapRight: {\n          value: _renderTargetR.texture\n        }\n      },\n      vertexShader: [\"varying vec2 vUv;\", \"void main() {\", \"\tvUv = vec2( uv.x, uv.y );\", \"\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\", \"}\"].join(\"\\n\"),\n      fragmentShader: [\"uniform sampler2D mapLeft;\", \"uniform sampler2D mapRight;\", \"varying vec2 vUv;\", \"void main() {\", \"\tvec2 uv = vUv;\", \"\tif ( ( mod( gl_FragCoord.y, 2.0 ) ) > 1.00 ) {\", \"\t\tgl_FragColor = texture2D( mapLeft, uv );\", \"\t} else {\", \"\t\tgl_FragColor = texture2D( mapRight, uv );\", \"\t}\", \"\t#include <tonemapping_fragment>\", `\t#include <${version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\"}>`, \"}\"].join(\"\\n\")\n    });\n    const mesh = new Mesh(new PlaneGeometry(2, 2), _material);\n    _scene.add(mesh);\n    this.setSize = function (width, height) {\n      renderer.setSize(width, height);\n      const pixelRatio = renderer.getPixelRatio();\n      _renderTargetL.setSize(width * pixelRatio, height * pixelRatio);\n      _renderTargetR.setSize(width * pixelRatio, height * pixelRatio);\n    };\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld();\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld();\n      _stereo.update(camera);\n      renderer.setRenderTarget(_renderTargetL);\n      renderer.clear();\n      renderer.render(scene, _stereo.cameraL);\n      renderer.setRenderTarget(_renderTargetR);\n      renderer.clear();\n      renderer.render(scene, _stereo.cameraR);\n      renderer.setRenderTarget(null);\n      renderer.render(_scene, _camera);\n    };\n  }\n}\nexport { ParallaxBarrierEffect };", "map": {"version": 3, "names": ["ParallaxBarrierEffect", "constructor", "renderer", "_camera", "OrthographicCamera", "_scene", "Scene", "_stereo", "StereoCamera", "_params", "minFilter", "LinearFilter", "magFilter", "NearestFilter", "format", "RGBAFormat", "_renderTargetL", "WebGLRenderTarget", "_renderTargetR", "_material", "ShaderMaterial", "uniforms", "mapLeft", "value", "texture", "mapRight", "vertexShader", "join", "fragmentShader", "version", "mesh", "<PERSON><PERSON>", "PlaneGeometry", "add", "setSize", "width", "height", "pixelRatio", "getPixelRatio", "render", "scene", "camera", "matrixWorldAutoUpdate", "updateMatrixWorld", "parent", "update", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "cameraL", "cameraR"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\effects\\ParallaxBarrierEffect.js"], "sourcesContent": ["import {\n  LinearFilter,\n  Mesh,\n  NearestFilter,\n  OrthographicCamera,\n  PlaneGeometry,\n  RGBAFormat,\n  Scene,\n  ShaderMaterial,\n  StereoCamera,\n  WebGLRenderTarget,\n} from 'three'\nimport { version } from '../_polyfill/constants'\n\nclass ParallaxBarrierEffect {\n  constructor(renderer) {\n    const _camera = new OrthographicCamera(-1, 1, 1, -1, 0, 1)\n\n    const _scene = new Scene()\n\n    const _stereo = new StereoCamera()\n\n    const _params = { minFilter: LinearFilter, magFilter: NearestFilter, format: RGBAFormat }\n\n    const _renderTargetL = new WebGLRenderTarget(512, 512, _params)\n    const _renderTargetR = new WebGLRenderTarget(512, 512, _params)\n\n    const _material = new ShaderMaterial({\n      uniforms: {\n        mapLeft: { value: _renderTargetL.texture },\n        mapRight: { value: _renderTargetR.texture },\n      },\n\n      vertexShader: [\n        'varying vec2 vUv;',\n\n        'void main() {',\n\n        '\tvUv = vec2( uv.x, uv.y );',\n        '\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );',\n\n        '}',\n      ].join('\\n'),\n\n      fragmentShader: [\n        'uniform sampler2D mapLeft;',\n        'uniform sampler2D mapRight;',\n        'varying vec2 vUv;',\n\n        'void main() {',\n\n        '\tvec2 uv = vUv;',\n\n        '\tif ( ( mod( gl_FragCoord.y, 2.0 ) ) > 1.00 ) {',\n\n        '\t\tgl_FragColor = texture2D( mapLeft, uv );',\n\n        '\t} else {',\n\n        '\t\tgl_FragColor = texture2D( mapRight, uv );',\n\n        '\t}',\n\n        '\t#include <tonemapping_fragment>',\n        `\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>`,\n\n        '}',\n      ].join('\\n'),\n    })\n\n    const mesh = new Mesh(new PlaneGeometry(2, 2), _material)\n    _scene.add(mesh)\n\n    this.setSize = function (width, height) {\n      renderer.setSize(width, height)\n\n      const pixelRatio = renderer.getPixelRatio()\n\n      _renderTargetL.setSize(width * pixelRatio, height * pixelRatio)\n      _renderTargetR.setSize(width * pixelRatio, height * pixelRatio)\n    }\n\n    this.render = function (scene, camera) {\n      if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld()\n\n      if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld()\n\n      _stereo.update(camera)\n\n      renderer.setRenderTarget(_renderTargetL)\n      renderer.clear()\n      renderer.render(scene, _stereo.cameraL)\n\n      renderer.setRenderTarget(_renderTargetR)\n      renderer.clear()\n      renderer.render(scene, _stereo.cameraR)\n\n      renderer.setRenderTarget(null)\n      renderer.render(_scene, _camera)\n    }\n  }\n}\n\nexport { ParallaxBarrierEffect }\n"], "mappings": ";;AAcA,MAAMA,qBAAA,CAAsB;EAC1BC,YAAYC,QAAA,EAAU;IACpB,MAAMC,OAAA,GAAU,IAAIC,kBAAA,CAAmB,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC;IAEzD,MAAMC,MAAA,GAAS,IAAIC,KAAA,CAAO;IAE1B,MAAMC,OAAA,GAAU,IAAIC,YAAA,CAAc;IAElC,MAAMC,OAAA,GAAU;MAAEC,SAAA,EAAWC,YAAA;MAAcC,SAAA,EAAWC,aAAA;MAAeC,MAAA,EAAQC;IAAY;IAEzF,MAAMC,cAAA,GAAiB,IAAIC,iBAAA,CAAkB,KAAK,KAAKR,OAAO;IAC9D,MAAMS,cAAA,GAAiB,IAAID,iBAAA,CAAkB,KAAK,KAAKR,OAAO;IAE9D,MAAMU,SAAA,GAAY,IAAIC,cAAA,CAAe;MACnCC,QAAA,EAAU;QACRC,OAAA,EAAS;UAAEC,KAAA,EAAOP,cAAA,CAAeQ;QAAS;QAC1CC,QAAA,EAAU;UAAEF,KAAA,EAAOL,cAAA,CAAeM;QAAS;MAC5C;MAEDE,YAAA,EAAc,CACZ,qBAEA,iBAEA,8BACA,8EAEA,IACR,CAAQC,IAAA,CAAK,IAAI;MAEXC,cAAA,EAAgB,CACd,8BACA,+BACA,qBAEA,iBAEA,mBAEA,mDAEA,8CAEA,aAEA,+CAEA,MAEA,oCACA,cAAcC,OAAA,IAAW,MAAM,wBAAwB,yBAEvD,IACR,CAAQF,IAAA,CAAK,IAAI;IACjB,CAAK;IAED,MAAMG,IAAA,GAAO,IAAIC,IAAA,CAAK,IAAIC,aAAA,CAAc,GAAG,CAAC,GAAGb,SAAS;IACxDd,MAAA,CAAO4B,GAAA,CAAIH,IAAI;IAEf,KAAKI,OAAA,GAAU,UAAUC,KAAA,EAAOC,MAAA,EAAQ;MACtClC,QAAA,CAASgC,OAAA,CAAQC,KAAA,EAAOC,MAAM;MAE9B,MAAMC,UAAA,GAAanC,QAAA,CAASoC,aAAA,CAAe;MAE3CtB,cAAA,CAAekB,OAAA,CAAQC,KAAA,GAAQE,UAAA,EAAYD,MAAA,GAASC,UAAU;MAC9DnB,cAAA,CAAegB,OAAA,CAAQC,KAAA,GAAQE,UAAA,EAAYD,MAAA,GAASC,UAAU;IAC/D;IAED,KAAKE,MAAA,GAAS,UAAUC,KAAA,EAAOC,MAAA,EAAQ;MACrC,IAAID,KAAA,CAAME,qBAAA,KAA0B,MAAMF,KAAA,CAAMG,iBAAA,CAAmB;MAEnE,IAAIF,MAAA,CAAOG,MAAA,KAAW,QAAQH,MAAA,CAAOC,qBAAA,KAA0B,MAAMD,MAAA,CAAOE,iBAAA,CAAmB;MAE/FpC,OAAA,CAAQsC,MAAA,CAAOJ,MAAM;MAErBvC,QAAA,CAAS4C,eAAA,CAAgB9B,cAAc;MACvCd,QAAA,CAAS6C,KAAA,CAAO;MAChB7C,QAAA,CAASqC,MAAA,CAAOC,KAAA,EAAOjC,OAAA,CAAQyC,OAAO;MAEtC9C,QAAA,CAAS4C,eAAA,CAAgB5B,cAAc;MACvChB,QAAA,CAAS6C,KAAA,CAAO;MAChB7C,QAAA,CAASqC,MAAA,CAAOC,KAAA,EAAOjC,OAAA,CAAQ0C,OAAO;MAEtC/C,QAAA,CAAS4C,eAAA,CAAgB,IAAI;MAC7B5C,QAAA,CAASqC,MAAA,CAAOlC,MAAA,EAAQF,OAAO;IAChC;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}