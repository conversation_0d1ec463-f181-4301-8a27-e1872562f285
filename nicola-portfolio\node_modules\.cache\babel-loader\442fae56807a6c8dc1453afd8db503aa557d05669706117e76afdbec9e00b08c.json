{"ast": null, "code": "const LuminosityShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    #include <common>\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tfloat l = linearToRelativeLuminance( texel.rgb );\n\n    \tgl_FragColor = vec4( l, l, l, texel.w );\n\n    }\n  `)\n};\nexport { LuminosityShader };", "map": {"version": 3, "names": ["Luminosity<PERSON><PERSON>er", "uniforms", "tDiffuse", "value", "vertexShader", "fragmentShader"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\shaders\\LuminosityShader.ts"], "sourcesContent": ["/**\n * Luminosity\n * http://en.wikipedia.org/wiki/Luminosity\n */\n\nexport const LuminosityShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #include <common>\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tfloat l = linearToRelativeLuminance( texel.rgb );\n\n    \tgl_FragColor = vec4( l, l, l, texel.w );\n\n    }\n  `,\n}\n"], "mappings": "AAKO,MAAMA,gBAAA,GAAmB;EAC9BC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;EAC1B;EAEAC,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}