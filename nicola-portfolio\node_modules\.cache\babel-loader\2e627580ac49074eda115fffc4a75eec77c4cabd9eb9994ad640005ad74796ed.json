{"ast": null, "code": "import React from 'react';\nimport { create } from 'zustand';\nvar _window$document, _window$navigator;\n/**\n * An SSR-friendly useLayoutEffect.\n *\n * React currently throws a warning when using useLayoutEffect on the server.\n * To get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect elsewhere.\n *\n * @see https://github.com/facebook/react/issues/14927\n */\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? React.useLayoutEffect : React.useEffect;\nfunction tunnel() {\n  const useStore = create(set => ({\n    current: new Array(),\n    version: 0,\n    set\n  }));\n  return {\n    In: ({\n      children\n    }) => {\n      const set = useStore(state => state.set);\n      const version = useStore(state => state.version);\n      /* When this component mounts, we increase the store's version number.\n      This will cause all existing rats to re-render (just like if the Out component\n      were mapping items to a list.) The re-rendering will cause the final \n      order of rendered components to match what the user is expecting. */\n\n      useIsomorphicLayoutEffect(() => {\n        set(state => ({\n          version: state.version + 1\n        }));\n      }, []);\n      /* Any time the children _or_ the store's version number change, insert\n      the specified React children into the list of rats. */\n\n      useIsomorphicLayoutEffect(() => {\n        set(({\n          current\n        }) => ({\n          current: [...current, children]\n        }));\n        return () => set(({\n          current\n        }) => ({\n          current: current.filter(c => c !== children)\n        }));\n      }, [children, version]);\n      return null;\n    },\n    Out: () => {\n      const current = useStore(state => state.current);\n      return /*#__PURE__*/React.createElement(React.Fragment, null, current);\n    }\n  };\n}\nexport { tunnel as default };", "map": {"version": 3, "names": ["React", "create", "_window$document", "_window$navigator", "useIsomorphicLayoutEffect", "window", "document", "createElement", "navigator", "product", "useLayoutEffect", "useEffect", "tunnel", "useStore", "set", "current", "Array", "version", "In", "children", "state", "filter", "c", "Out", "Fragment", "default"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/node_modules/tunnel-rat/dist/index.js"], "sourcesContent": ["import React from 'react';\nimport { create } from 'zustand';\n\nvar _window$document, _window$navigator;\n/**\n * An SSR-friendly useLayoutEffect.\n *\n * React currently throws a warning when using useLayoutEffect on the server.\n * To get around it, we can conditionally useEffect on the server (no-op) and\n * useLayoutEffect elsewhere.\n *\n * @see https://github.com/facebook/react/issues/14927\n */\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? React.useLayoutEffect : React.useEffect;\n\nfunction tunnel() {\n  const useStore = create(set => ({\n    current: new Array(),\n    version: 0,\n    set\n  }));\n  return {\n    In: ({\n      children\n    }) => {\n      const set = useStore(state => state.set);\n      const version = useStore(state => state.version);\n      /* When this component mounts, we increase the store's version number.\n      This will cause all existing rats to re-render (just like if the Out component\n      were mapping items to a list.) The re-rendering will cause the final \n      order of rendered components to match what the user is expecting. */\n\n      useIsomorphicLayoutEffect(() => {\n        set(state => ({\n          version: state.version + 1\n        }));\n      }, []);\n      /* Any time the children _or_ the store's version number change, insert\n      the specified React children into the list of rats. */\n\n      useIsomorphicLayoutEffect(() => {\n        set(({\n          current\n        }) => ({\n          current: [...current, children]\n        }));\n        return () => set(({\n          current\n        }) => ({\n          current: current.filter(c => c !== children)\n        }));\n      }, [children, version]);\n      return null;\n    },\n    Out: () => {\n      const current = useStore(state => state.current);\n      return /*#__PURE__*/React.createElement(React.Fragment, null, current);\n    }\n  };\n}\n\nexport { tunnel as default };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,SAAS;AAEhC,IAAIC,gBAAgB,EAAEC,iBAAiB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,yBAAyB,GAAG,OAAOC,MAAM,KAAK,WAAW,KAAK,CAACH,gBAAgB,GAAGG,MAAM,CAACC,QAAQ,KAAK,IAAI,IAAIJ,gBAAgB,CAACK,aAAa,IAAI,CAAC,CAACJ,iBAAiB,GAAGE,MAAM,CAACG,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGL,iBAAiB,CAACM,OAAO,MAAM,aAAa,CAAC,GAAGT,KAAK,CAACU,eAAe,GAAGV,KAAK,CAACW,SAAS;AAExS,SAASC,MAAMA,CAAA,EAAG;EAChB,MAAMC,QAAQ,GAAGZ,MAAM,CAACa,GAAG,KAAK;IAC9BC,OAAO,EAAE,IAAIC,KAAK,CAAC,CAAC;IACpBC,OAAO,EAAE,CAAC;IACVH;EACF,CAAC,CAAC,CAAC;EACH,OAAO;IACLI,EAAE,EAAEA,CAAC;MACHC;IACF,CAAC,KAAK;MACJ,MAAML,GAAG,GAAGD,QAAQ,CAACO,KAAK,IAAIA,KAAK,CAACN,GAAG,CAAC;MACxC,MAAMG,OAAO,GAAGJ,QAAQ,CAACO,KAAK,IAAIA,KAAK,CAACH,OAAO,CAAC;MAChD;AACN;AACA;AACA;;MAEMb,yBAAyB,CAAC,MAAM;QAC9BU,GAAG,CAACM,KAAK,KAAK;UACZH,OAAO,EAAEG,KAAK,CAACH,OAAO,GAAG;QAC3B,CAAC,CAAC,CAAC;MACL,CAAC,EAAE,EAAE,CAAC;MACN;AACN;;MAEMb,yBAAyB,CAAC,MAAM;QAC9BU,GAAG,CAAC,CAAC;UACHC;QACF,CAAC,MAAM;UACLA,OAAO,EAAE,CAAC,GAAGA,OAAO,EAAEI,QAAQ;QAChC,CAAC,CAAC,CAAC;QACH,OAAO,MAAML,GAAG,CAAC,CAAC;UAChBC;QACF,CAAC,MAAM;UACLA,OAAO,EAAEA,OAAO,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKH,QAAQ;QAC7C,CAAC,CAAC,CAAC;MACL,CAAC,EAAE,CAACA,QAAQ,EAAEF,OAAO,CAAC,CAAC;MACvB,OAAO,IAAI;IACb,CAAC;IACDM,GAAG,EAAEA,CAAA,KAAM;MACT,MAAMR,OAAO,GAAGF,QAAQ,CAACO,KAAK,IAAIA,KAAK,CAACL,OAAO,CAAC;MAChD,OAAO,aAAaf,KAAK,CAACO,aAAa,CAACP,KAAK,CAACwB,QAAQ,EAAE,IAAI,EAAET,OAAO,CAAC;IACxE;EACF,CAAC;AACH;AAEA,SAASH,MAAM,IAAIa,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}