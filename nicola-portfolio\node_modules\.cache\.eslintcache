[{"C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\App.jsx": "3", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\Navbar.jsx": "4", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\Skills.jsx": "5", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Hero\\Hero.jsx": "6", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactMe.jsx": "7", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactForm\\ContactForm.jsx": "8", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\Projects.jsx": "9", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\utils\\data.js": "10", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillsCard.jsx": "11", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillCardInfo\\SkillCardInfo.jsx": "12", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\MobileNavbar\\MobileNavbar.jsx": "13", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\ProjectCard\\ProjectCard.jsx": "14", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactInfoCard\\ContactInfoCard.jsx": "15", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\AboutMe\\AboutMe.jsx": "16", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\AboutMe\\AboutCard\\AboutCard.jsx": "17", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Hero\\Avatar3D\\Avatar3D.jsx": "18"}, {"size": 552, "mtime": 1738007576904, "results": "19", "hashOfConfig": "20"}, {"size": 375, "mtime": 1738007576905, "results": "21", "hashOfConfig": "20"}, {"size": 938, "mtime": 1751290898364, "results": "22", "hashOfConfig": "20"}, {"size": 3222, "mtime": 1751202281437, "results": "23", "hashOfConfig": "20"}, {"size": 1279, "mtime": 1738007576901, "results": "24", "hashOfConfig": "20"}, {"size": 3092, "mtime": 1751291260497, "results": "25", "hashOfConfig": "20"}, {"size": 896, "mtime": 1738007576895, "results": "26", "hashOfConfig": "20"}, {"size": 646, "mtime": 1729780676641, "results": "27", "hashOfConfig": "28"}, {"size": 2895, "mtime": 1751198151285, "results": "29", "hashOfConfig": "20"}, {"size": 1325, "mtime": 1738362434028, "results": "30", "hashOfConfig": "20"}, {"size": 411, "mtime": 1738007576904, "results": "31", "hashOfConfig": "20"}, {"size": 771, "mtime": 1738007576903, "results": "32", "hashOfConfig": "20"}, {"size": 2573, "mtime": 1751202305594, "results": "33", "hashOfConfig": "20"}, {"size": 547, "mtime": 1738007576899, "results": "34", "hashOfConfig": "20"}, {"size": 372, "mtime": 1738007576894, "results": "35", "hashOfConfig": "20"}, {"size": 2976, "mtime": 1751204602339, "results": "36", "hashOfConfig": "20"}, {"size": 668, "mtime": 1751202234798, "results": "37", "hashOfConfig": "20"}, {"size": 5164, "mtime": 1751291234707, "results": "38", "hashOfConfig": "20"}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9dxb8u", {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1g89du", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\Skills.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Hero\\Hero.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactMe.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactForm\\ContactForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\Projects.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\utils\\data.js", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillsCard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillCardInfo\\SkillCardInfo.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\MobileNavbar\\MobileNavbar.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\ProjectCard\\ProjectCard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactInfoCard\\ContactInfoCard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\AboutMe\\AboutMe.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\AboutMe\\AboutCard\\AboutCard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Hero\\Avatar3D\\Avatar3D.jsx", ["93"], [], {"ruleId": "94", "severity": 1, "message": "95", "line": 4, "column": 13, "nodeType": "96", "messageId": "97", "endLine": 4, "endColumn": 18}, "no-unused-vars", "'THREE' is defined but never used.", "Identifier", "unusedVar"]