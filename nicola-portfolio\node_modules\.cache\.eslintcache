[{"C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\App.jsx": "3", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\Navbar.jsx": "4", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\Skills.jsx": "5", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Hero\\Hero.jsx": "6", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactMe.jsx": "7", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactForm\\ContactForm.jsx": "8", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\Projects.jsx": "9", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\utils\\data.js": "10", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillsCard.jsx": "11", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillCardInfo\\SkillCardInfo.jsx": "12", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\MobileNavbar\\MobileNavbar.jsx": "13", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\ProjectCard\\ProjectCard.jsx": "14", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactInfoCard\\ContactInfoCard.jsx": "15", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\AboutMe\\AboutMe.jsx": "16", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\AboutMe\\AboutCard\\AboutCard.jsx": "17"}, {"size": 552, "mtime": 1738007576904, "results": "18", "hashOfConfig": "19"}, {"size": 375, "mtime": 1738007576905, "results": "20", "hashOfConfig": "19"}, {"size": 938, "mtime": 1751202265466, "results": "21", "hashOfConfig": "19"}, {"size": 3222, "mtime": 1751202281437, "results": "22", "hashOfConfig": "19"}, {"size": 1279, "mtime": 1738007576901, "results": "23", "hashOfConfig": "19"}, {"size": 3100, "mtime": 1751202817964, "results": "24", "hashOfConfig": "19"}, {"size": 896, "mtime": 1738007576895, "results": "25", "hashOfConfig": "19"}, {"size": 646, "mtime": 1729780676641, "results": "26", "hashOfConfig": "19"}, {"size": 2895, "mtime": 1751198151285, "results": "27", "hashOfConfig": "19"}, {"size": 1325, "mtime": 1738362434028, "results": "28", "hashOfConfig": "19"}, {"size": 411, "mtime": 1738007576904, "results": "29", "hashOfConfig": "19"}, {"size": 771, "mtime": 1738007576903, "results": "30", "hashOfConfig": "19"}, {"size": 2573, "mtime": 1751202305594, "results": "31", "hashOfConfig": "19"}, {"size": 547, "mtime": 1738007576899, "results": "32", "hashOfConfig": "19"}, {"size": 372, "mtime": 1738007576894, "results": "33", "hashOfConfig": "19"}, {"size": 2361, "mtime": 1751202418673, "results": "34", "hashOfConfig": "19"}, {"size": 668, "mtime": 1751202234798, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1g89du", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\Skills.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Hero\\Hero.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactMe.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactForm\\ContactForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\Projects.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\utils\\data.js", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillsCard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillCardInfo\\SkillCardInfo.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\MobileNavbar\\MobileNavbar.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\ProjectCard\\ProjectCard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactInfoCard\\ContactInfoCard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\AboutMe\\AboutMe.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\AboutMe\\AboutCard\\AboutCard.jsx", [], []]