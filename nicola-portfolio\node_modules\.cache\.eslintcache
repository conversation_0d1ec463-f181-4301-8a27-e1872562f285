[{"C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\App.jsx": "3", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\Navbar.jsx": "4", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\Skills.jsx": "5", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Hero\\Hero.jsx": "6", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactMe.jsx": "7", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactForm\\ContactForm.jsx": "8", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\Projects.jsx": "9", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\utils\\data.js": "10", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillsCard.jsx": "11", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillCardInfo\\SkillCardInfo.jsx": "12", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\MobileNavbar\\MobileNavbar.jsx": "13", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\ProjectCard\\ProjectCard.jsx": "14", "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactInfoCard\\ContactInfoCard.jsx": "15"}, {"size": 552, "mtime": 1738007576904, "results": "16", "hashOfConfig": "17"}, {"size": 375, "mtime": 1738007576905, "results": "18", "hashOfConfig": "17"}, {"size": 864, "mtime": 1742460536036, "results": "19", "hashOfConfig": "17"}, {"size": 2954, "mtime": 1751200057204, "results": "20", "hashOfConfig": "17"}, {"size": 1279, "mtime": 1738007576901, "results": "21", "hashOfConfig": "17"}, {"size": 1844, "mtime": 1750863955980, "results": "22", "hashOfConfig": "17"}, {"size": 896, "mtime": 1738007576895, "results": "23", "hashOfConfig": "17"}, {"size": 646, "mtime": 1729780676641, "results": "24", "hashOfConfig": "17"}, {"size": 2895, "mtime": 1751198151285, "results": "25", "hashOfConfig": "17"}, {"size": 1325, "mtime": 1738362434028, "results": "26", "hashOfConfig": "17"}, {"size": 411, "mtime": 1738007576904, "results": "27", "hashOfConfig": "17"}, {"size": 771, "mtime": 1738007576903, "results": "28", "hashOfConfig": "17"}, {"size": 2233, "mtime": 1738359839650, "results": "29", "hashOfConfig": "17"}, {"size": 547, "mtime": 1738007576899, "results": "30", "hashOfConfig": "17"}, {"size": 372, "mtime": 1738007576894, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1g89du", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\Skills.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Hero\\Hero.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactMe.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactForm\\ContactForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\Projects.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\utils\\data.js", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillsCard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Skills\\SkillsCard\\SkillCardInfo\\SkillCardInfo.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Navbar\\MobileNavbar\\MobileNavbar.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\Projects\\ProjectCard\\ProjectCard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\src\\components\\ContactMe\\ContactInfoCard\\ContactInfoCard.jsx", [], []]