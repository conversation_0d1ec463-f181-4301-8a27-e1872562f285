{"ast": null, "code": "import { Lo<PERSON>, FileLoader, DataTexture, RGBAFormat, UnsignedByteType, LinearFilter, ClampToEdgeWrapping } from \"three\";\nimport { Data3DTexture } from \"../_polyfill/Data3DTexture.js\";\nclass LUT3dlLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager);\n    loader.setPath(this.path);\n    loader.setResponseType(\"text\");\n    loader.load(url, text => {\n      try {\n        onLoad(this.parse(text));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        this.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(str) {\n    str = str.replace(/^#.*?(\\n|\\r)/gm, \"\").replace(/^\\s*?(\\n|\\r)/gm, \"\").trim();\n    const lines = str.split(/[\\n\\r]+/g);\n    const gridLines = lines[0].trim().split(/\\s+/g).map(e => parseFloat(e));\n    const gridStep = gridLines[1] - gridLines[0];\n    const size = gridLines.length;\n    for (let i = 1, l = gridLines.length; i < l; i++) {\n      if (gridStep !== gridLines[i] - gridLines[i - 1]) {\n        throw new Error(\"LUT3dlLoader: Inconsistent grid size not supported.\");\n      }\n    }\n    const dataArray = new Array(size * size * size * 4);\n    let index = 0;\n    let maxOutputValue = 0;\n    for (let i = 1, l = lines.length; i < l; i++) {\n      const line = lines[i].trim();\n      const split = line.split(/\\s/g);\n      const r = parseFloat(split[0]);\n      const g = parseFloat(split[1]);\n      const b = parseFloat(split[2]);\n      maxOutputValue = Math.max(maxOutputValue, r, g, b);\n      const bLayer = index % size;\n      const gLayer = Math.floor(index / size) % size;\n      const rLayer = Math.floor(index / (size * size)) % size;\n      const pixelIndex = bLayer * size * size + gLayer * size + rLayer;\n      dataArray[4 * pixelIndex + 0] = r;\n      dataArray[4 * pixelIndex + 1] = g;\n      dataArray[4 * pixelIndex + 2] = b;\n      dataArray[4 * pixelIndex + 3] = 1;\n      index += 1;\n    }\n    const bits = Math.ceil(Math.log2(maxOutputValue));\n    const maxBitValue = Math.pow(2, bits);\n    for (let i = 0, l = dataArray.length; i < l; i += 4) {\n      const r = dataArray[i + 0];\n      const g = dataArray[i + 1];\n      const b = dataArray[i + 2];\n      dataArray[i + 0] = 255 * r / maxBitValue;\n      dataArray[i + 1] = 255 * g / maxBitValue;\n      dataArray[i + 2] = 255 * b / maxBitValue;\n    }\n    const data = new Uint8Array(dataArray);\n    const texture = new DataTexture();\n    texture.image.data = data;\n    texture.image.width = size;\n    texture.image.height = size * size;\n    texture.format = RGBAFormat;\n    texture.type = UnsignedByteType;\n    texture.magFilter = LinearFilter;\n    texture.minFilter = LinearFilter;\n    texture.wrapS = ClampToEdgeWrapping;\n    texture.wrapT = ClampToEdgeWrapping;\n    texture.generateMipmaps = false;\n    texture.needsUpdate = true;\n    const texture3D = new Data3DTexture();\n    texture3D.image.data = data;\n    texture3D.image.width = size;\n    texture3D.image.height = size;\n    texture3D.image.depth = size;\n    texture3D.format = RGBAFormat;\n    texture3D.type = UnsignedByteType;\n    texture3D.magFilter = LinearFilter;\n    texture3D.minFilter = LinearFilter;\n    texture3D.wrapS = ClampToEdgeWrapping;\n    texture3D.wrapT = ClampToEdgeWrapping;\n    texture3D.wrapR = ClampToEdgeWrapping;\n    texture3D.generateMipmaps = false;\n    texture3D.needsUpdate = true;\n    return {\n      size,\n      texture,\n      texture3D\n    };\n  }\n}\nexport { LUT3dlLoader };", "map": {"version": 3, "names": ["LUT3dlLoader", "Loader", "load", "url", "onLoad", "onProgress", "onError", "loader", "<PERSON><PERSON><PERSON><PERSON>", "manager", "set<PERSON>ath", "path", "setResponseType", "text", "parse", "e", "console", "error", "itemError", "str", "replace", "trim", "lines", "split", "gridLines", "map", "parseFloat", "gridStep", "size", "length", "i", "l", "Error", "dataArray", "Array", "index", "maxOutputValue", "line", "r", "g", "b", "Math", "max", "b<PERSON><PERSON><PERSON>", "g<PERSON><PERSON><PERSON>", "floor", "r<PERSON><PERSON><PERSON>", "pixelIndex", "bits", "ceil", "log2", "maxBitValue", "pow", "data", "Uint8Array", "texture", "DataTexture", "image", "width", "height", "format", "RGBAFormat", "type", "UnsignedByteType", "magFilter", "LinearFilter", "minFilter", "wrapS", "ClampToEdgeWrapping", "wrapT", "generateMipmaps", "needsUpdate", "texture3D", "Data3DTexture", "depth", "wrapR"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\loaders\\LUT3dlLoader.js"], "sourcesContent": ["// http://download.autodesk.com/us/systemdocs/help/2011/lustre/index.html?url=./files/WSc4e151a45a3b785a24c3d9a411df9298473-7ffd.htm,topicNumber=d0e9492\n// https://community.foundry.com/discuss/topic/103636/format-spec-for-3dl?mode=Post&postID=895258\nimport { Loader, FileLoader, DataTexture, RGBAFormat, UnsignedByteType, ClampToEdgeWrapping, LinearFilter } from 'three'\nimport { Data3DTexture } from '../_polyfill/Data3DTexture'\n\nexport class LUT3dlLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    const loader = new FileLoader(this.manager)\n    loader.setPath(this.path)\n    loader.setResponseType('text')\n    loader.load(\n      url,\n      (text) => {\n        try {\n          onLoad(this.parse(text))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          this.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(str) {\n    // remove empty lines and comment lints\n    str = str\n      .replace(/^#.*?(\\n|\\r)/gm, '')\n      .replace(/^\\s*?(\\n|\\r)/gm, '')\n      .trim()\n\n    const lines = str.split(/[\\n\\r]+/g)\n\n    // first line is the positions on the grid that are provided by the LUT\n    const gridLines = lines[0]\n      .trim()\n      .split(/\\s+/g)\n      .map((e) => parseFloat(e))\n    const gridStep = gridLines[1] - gridLines[0]\n    const size = gridLines.length\n\n    for (let i = 1, l = gridLines.length; i < l; i++) {\n      if (gridStep !== gridLines[i] - gridLines[i - 1]) {\n        throw new Error('LUT3dlLoader: Inconsistent grid size not supported.')\n      }\n    }\n\n    const dataArray = new Array(size * size * size * 4)\n    let index = 0\n    let maxOutputValue = 0.0\n    for (let i = 1, l = lines.length; i < l; i++) {\n      const line = lines[i].trim()\n      const split = line.split(/\\s/g)\n\n      const r = parseFloat(split[0])\n      const g = parseFloat(split[1])\n      const b = parseFloat(split[2])\n      maxOutputValue = Math.max(maxOutputValue, r, g, b)\n\n      const bLayer = index % size\n      const gLayer = Math.floor(index / size) % size\n      const rLayer = Math.floor(index / (size * size)) % size\n\n      // b grows first, then g, then r\n      const pixelIndex = bLayer * size * size + gLayer * size + rLayer\n      dataArray[4 * pixelIndex + 0] = r\n      dataArray[4 * pixelIndex + 1] = g\n      dataArray[4 * pixelIndex + 2] = b\n      dataArray[4 * pixelIndex + 3] = 1.0\n      index += 1\n    }\n\n    // Find the apparent bit depth of the stored RGB values and map the\n    // values to [ 0, 255 ].\n    const bits = Math.ceil(Math.log2(maxOutputValue))\n    const maxBitValue = Math.pow(2.0, bits)\n    for (let i = 0, l = dataArray.length; i < l; i += 4) {\n      const r = dataArray[i + 0]\n      const g = dataArray[i + 1]\n      const b = dataArray[i + 2]\n      dataArray[i + 0] = (255 * r) / maxBitValue // r\n      dataArray[i + 1] = (255 * g) / maxBitValue // g\n      dataArray[i + 2] = (255 * b) / maxBitValue // b\n    }\n\n    const data = new Uint8Array(dataArray)\n    const texture = new DataTexture()\n    texture.image.data = data\n    texture.image.width = size\n    texture.image.height = size * size\n    texture.format = RGBAFormat\n    texture.type = UnsignedByteType\n    texture.magFilter = LinearFilter\n    texture.minFilter = LinearFilter\n    texture.wrapS = ClampToEdgeWrapping\n    texture.wrapT = ClampToEdgeWrapping\n    texture.generateMipmaps = false\n    texture.needsUpdate = true\n\n    const texture3D = new Data3DTexture()\n    texture3D.image.data = data\n    texture3D.image.width = size\n    texture3D.image.height = size\n    texture3D.image.depth = size\n    texture3D.format = RGBAFormat\n    texture3D.type = UnsignedByteType\n    texture3D.magFilter = LinearFilter\n    texture3D.minFilter = LinearFilter\n    texture3D.wrapS = ClampToEdgeWrapping\n    texture3D.wrapT = ClampToEdgeWrapping\n    texture3D.wrapR = ClampToEdgeWrapping\n    texture3D.generateMipmaps = false\n    texture3D.needsUpdate = true\n\n    return {\n      size,\n      texture,\n      texture3D,\n    }\n  }\n}\n"], "mappings": ";;AAKO,MAAMA,YAAA,SAAqBC,MAAA,CAAO;EACvCC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW,KAAKC,OAAO;IAC1CF,MAAA,CAAOG,OAAA,CAAQ,KAAKC,IAAI;IACxBJ,MAAA,CAAOK,eAAA,CAAgB,MAAM;IAC7BL,MAAA,CAAOL,IAAA,CACLC,GAAA,EACCU,IAAA,IAAS;MACR,IAAI;QACFT,MAAA,CAAO,KAAKU,KAAA,CAAMD,IAAI,CAAC;MACxB,SAAQE,CAAA,EAAP;QACA,IAAIT,OAAA,EAAS;UACXA,OAAA,CAAQS,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAED,KAAKN,OAAA,CAAQS,SAAA,CAAUf,GAAG;MAC3B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDQ,MAAMK,GAAA,EAAK;IAETA,GAAA,GAAMA,GAAA,CACHC,OAAA,CAAQ,kBAAkB,EAAE,EAC5BA,OAAA,CAAQ,kBAAkB,EAAE,EAC5BC,IAAA,CAAM;IAET,MAAMC,KAAA,GAAQH,GAAA,CAAII,KAAA,CAAM,UAAU;IAGlC,MAAMC,SAAA,GAAYF,KAAA,CAAM,CAAC,EACtBD,IAAA,CAAM,EACNE,KAAA,CAAM,MAAM,EACZE,GAAA,CAAKV,CAAA,IAAMW,UAAA,CAAWX,CAAC,CAAC;IAC3B,MAAMY,QAAA,GAAWH,SAAA,CAAU,CAAC,IAAIA,SAAA,CAAU,CAAC;IAC3C,MAAMI,IAAA,GAAOJ,SAAA,CAAUK,MAAA;IAEvB,SAASC,CAAA,GAAI,GAAGC,CAAA,GAAIP,SAAA,CAAUK,MAAA,EAAQC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAChD,IAAIH,QAAA,KAAaH,SAAA,CAAUM,CAAC,IAAIN,SAAA,CAAUM,CAAA,GAAI,CAAC,GAAG;QAChD,MAAM,IAAIE,KAAA,CAAM,qDAAqD;MACtE;IACF;IAED,MAAMC,SAAA,GAAY,IAAIC,KAAA,CAAMN,IAAA,GAAOA,IAAA,GAAOA,IAAA,GAAO,CAAC;IAClD,IAAIO,KAAA,GAAQ;IACZ,IAAIC,cAAA,GAAiB;IACrB,SAASN,CAAA,GAAI,GAAGC,CAAA,GAAIT,KAAA,CAAMO,MAAA,EAAQC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK;MAC5C,MAAMO,IAAA,GAAOf,KAAA,CAAMQ,CAAC,EAAET,IAAA,CAAM;MAC5B,MAAME,KAAA,GAAQc,IAAA,CAAKd,KAAA,CAAM,KAAK;MAE9B,MAAMe,CAAA,GAAIZ,UAAA,CAAWH,KAAA,CAAM,CAAC,CAAC;MAC7B,MAAMgB,CAAA,GAAIb,UAAA,CAAWH,KAAA,CAAM,CAAC,CAAC;MAC7B,MAAMiB,CAAA,GAAId,UAAA,CAAWH,KAAA,CAAM,CAAC,CAAC;MAC7Ba,cAAA,GAAiBK,IAAA,CAAKC,GAAA,CAAIN,cAAA,EAAgBE,CAAA,EAAGC,CAAA,EAAGC,CAAC;MAEjD,MAAMG,MAAA,GAASR,KAAA,GAAQP,IAAA;MACvB,MAAMgB,MAAA,GAASH,IAAA,CAAKI,KAAA,CAAMV,KAAA,GAAQP,IAAI,IAAIA,IAAA;MAC1C,MAAMkB,MAAA,GAASL,IAAA,CAAKI,KAAA,CAAMV,KAAA,IAASP,IAAA,GAAOA,IAAA,CAAK,IAAIA,IAAA;MAGnD,MAAMmB,UAAA,GAAaJ,MAAA,GAASf,IAAA,GAAOA,IAAA,GAAOgB,MAAA,GAAShB,IAAA,GAAOkB,MAAA;MAC1Db,SAAA,CAAU,IAAIc,UAAA,GAAa,CAAC,IAAIT,CAAA;MAChCL,SAAA,CAAU,IAAIc,UAAA,GAAa,CAAC,IAAIR,CAAA;MAChCN,SAAA,CAAU,IAAIc,UAAA,GAAa,CAAC,IAAIP,CAAA;MAChCP,SAAA,CAAU,IAAIc,UAAA,GAAa,CAAC,IAAI;MAChCZ,KAAA,IAAS;IACV;IAID,MAAMa,IAAA,GAAOP,IAAA,CAAKQ,IAAA,CAAKR,IAAA,CAAKS,IAAA,CAAKd,cAAc,CAAC;IAChD,MAAMe,WAAA,GAAcV,IAAA,CAAKW,GAAA,CAAI,GAAKJ,IAAI;IACtC,SAASlB,CAAA,GAAI,GAAGC,CAAA,GAAIE,SAAA,CAAUJ,MAAA,EAAQC,CAAA,GAAIC,CAAA,EAAGD,CAAA,IAAK,GAAG;MACnD,MAAMQ,CAAA,GAAIL,SAAA,CAAUH,CAAA,GAAI,CAAC;MACzB,MAAMS,CAAA,GAAIN,SAAA,CAAUH,CAAA,GAAI,CAAC;MACzB,MAAMU,CAAA,GAAIP,SAAA,CAAUH,CAAA,GAAI,CAAC;MACzBG,SAAA,CAAUH,CAAA,GAAI,CAAC,IAAK,MAAMQ,CAAA,GAAKa,WAAA;MAC/BlB,SAAA,CAAUH,CAAA,GAAI,CAAC,IAAK,MAAMS,CAAA,GAAKY,WAAA;MAC/BlB,SAAA,CAAUH,CAAA,GAAI,CAAC,IAAK,MAAMU,CAAA,GAAKW,WAAA;IAChC;IAED,MAAME,IAAA,GAAO,IAAIC,UAAA,CAAWrB,SAAS;IACrC,MAAMsB,OAAA,GAAU,IAAIC,WAAA,CAAa;IACjCD,OAAA,CAAQE,KAAA,CAAMJ,IAAA,GAAOA,IAAA;IACrBE,OAAA,CAAQE,KAAA,CAAMC,KAAA,GAAQ9B,IAAA;IACtB2B,OAAA,CAAQE,KAAA,CAAME,MAAA,GAAS/B,IAAA,GAAOA,IAAA;IAC9B2B,OAAA,CAAQK,MAAA,GAASC,UAAA;IACjBN,OAAA,CAAQO,IAAA,GAAOC,gBAAA;IACfR,OAAA,CAAQS,SAAA,GAAYC,YAAA;IACpBV,OAAA,CAAQW,SAAA,GAAYD,YAAA;IACpBV,OAAA,CAAQY,KAAA,GAAQC,mBAAA;IAChBb,OAAA,CAAQc,KAAA,GAAQD,mBAAA;IAChBb,OAAA,CAAQe,eAAA,GAAkB;IAC1Bf,OAAA,CAAQgB,WAAA,GAAc;IAEtB,MAAMC,SAAA,GAAY,IAAIC,aAAA,CAAe;IACrCD,SAAA,CAAUf,KAAA,CAAMJ,IAAA,GAAOA,IAAA;IACvBmB,SAAA,CAAUf,KAAA,CAAMC,KAAA,GAAQ9B,IAAA;IACxB4C,SAAA,CAAUf,KAAA,CAAME,MAAA,GAAS/B,IAAA;IACzB4C,SAAA,CAAUf,KAAA,CAAMiB,KAAA,GAAQ9C,IAAA;IACxB4C,SAAA,CAAUZ,MAAA,GAASC,UAAA;IACnBW,SAAA,CAAUV,IAAA,GAAOC,gBAAA;IACjBS,SAAA,CAAUR,SAAA,GAAYC,YAAA;IACtBO,SAAA,CAAUN,SAAA,GAAYD,YAAA;IACtBO,SAAA,CAAUL,KAAA,GAAQC,mBAAA;IAClBI,SAAA,CAAUH,KAAA,GAAQD,mBAAA;IAClBI,SAAA,CAAUG,KAAA,GAAQP,mBAAA;IAClBI,SAAA,CAAUF,eAAA,GAAkB;IAC5BE,SAAA,CAAUD,WAAA,GAAc;IAExB,OAAO;MACL3C,IAAA;MACA2B,OAAA;MACAiB;IACD;EACF;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}