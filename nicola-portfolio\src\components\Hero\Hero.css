.hero-container {
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    animation: fadeIn 1s ease-in-out;
  }
  
  .hero-container::after,
  .hero-container::before {
    content: "";
    width: 28.125rem;
    height: 28.125rem;
    border-radius: 28.125rem;
    background: transparent; /* Use primary color variable */
    position: absolute;
    z-index: -1;
    filter: blur(255px);
    animation: pulse 6s infinite ease-in-out;
  }
  
  .hero-container::after {
    top: -3rem;
    left: -5rem;
  }
  
  .hero-container::before {
    background: transparent; /* Use secondary color variable */
    bottom: 2rem;
    right: 0rem;
  }
  
  .hero-content {
    flex: 1;
    opacity: 0;
    transform: translateY(30px);
    animation: slideUp 1s ease-in-out forwards 0.5s;
  }
  
  .hero-content h2 {
    font-size: 3.8rem;
    font-weight: 600;
    line-height: 5rem;
    margin-bottom: 1rem;
    color: var(--text-color); /* Use text color variable */
  }
  
  .hero-content p {
    width: 80%;
    font-size: 1rem;
    font-weight: 400;
    line-height: 2rem;
    color: var(--text-color); /* Use text color variable */
  }
  
  .hero-img {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    margin-top: 5rem;
    opacity: 0;
    transform: scale(0.9);
    animation: scaleIn 1s ease-in-out forwards 1s;
  }
  
  .hero-img > div {
    display: flex;
    align-items: flex-end;
    gap: 2rem;
  }
  
  .hero-img > div > img {
    width: 25rem;
    transition: all 0.3s ease;
  }
  
  .tech-icon {
    width: 4rem;
    height: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    border-radius: 0.65rem;
    border: 1.5px solid var(--primary-color); /* Use primary color variable */
    opacity: 0;
    transform: translateY(30px);
    animation: fadeSlideUp 0.8s ease-in-out forwards 1.2s;
  }
  
  .tech-icon-1 {
    position: absolute;
    top: 20px;
    left: 30px;
    animation: fadeSlideUp 0.8s ease-in-out forwards 1.2s, float 3s ease-in-out infinite 2s;
  }

  .tech-icon-2 {
    position: absolute;
    top: 60px;
    right: 60px;
    animation: fadeSlideUp 0.8s ease-in-out forwards 1.4s, float 3.2s ease-in-out infinite 2.5s;
  }

  .tech-icon-3 {
    position: absolute;
    bottom: 170px;
    left: -20px;
    animation: fadeSlideUp 0.8s ease-in-out forwards 1.6s, float 2.8s ease-in-out infinite 3s;
  }

  .tech-icon-4 {
    position: absolute;
    bottom: 70px;
    right: 30px;
    animation: fadeSlideUp 0.8s ease-in-out forwards 1.8s, float 3.5s ease-in-out infinite 3.5s;
  }

  .tech-icon-5 {
    position: absolute;
    bottom: 15px;
    left: 30px;
    transform: translateX(-50%);
    animation: fadeSlideUp 0.8s ease-in-out forwards 2s, floatWithX 2.9s ease-in-out infinite 4s;
  }

  .tech-icon-6 {
    position: absolute;
    top: 190px;
    right: 20px;
    animation: fadeSlideUp 0.8s ease-in-out forwards 2.2s, float 3.3s ease-in-out infinite 4.5s;
  }
  
  .tech-icon img {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .hero-img > div > img:hover,
  .tech-icon:hover {
    background: var(--primary-color); /* Use primary color variable */
    transition: all 0.2s ease-in;
  }
  
  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  
  @keyframes slideUp {
    0% {
      opacity: 0;
      transform: translateY(30px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeSlideUp {
    0% {
      opacity: 0;
      transform: translateY(30px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes scaleIn {
    0% {
      opacity: 0;
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes floatWithX {
    0%, 100% {
      transform: translateX(-50%) translateY(0px);
    }
    50% {
      transform: translateX(-50%) translateY(-10px);
    }
  }
  
  @media (max-width: 1025px) {
    .hero-content h2 {
      font-size: 3rem;
      line-height: 4rem;
    }
  
    .hero-content p {
      font-size: 0.9rem;
      line-height: 1.6rem;
    }
  
    .hero-img > div > img {
      width: 20rem;
    }
  
    .tech-icon {
      width: 4rem;
      height: 4rem;
    }
  
    .tech-icon img {
      width: 2.5rem;
    }
  }
  
  @media (max-width: 768px) {
    .hero-container {
      flex-direction: column;
    }
  
    .hero-img {
      margin: 2rem 0 2rem 0;
    }
  
    .hero-container h2 {
      margin-top: 3rem;
    }
  
    .hero-content h2,
    .hero-content p {
      width: auto;
      text-align: center;
    }
  
    .hero-container::after,
    .hero-container::before {
      content: "";
      width: 18rem;
      height: 18rem;
    }
  
    .hero-container::after {
      top: 0rem;
      left: 0rem;
    }
  
    .hero-container::before {
      bottom: 2rem;
      right: 0rem;
    }
  }
  
  @media (max-width: 480px) {
    .hero-content h2 {
      font-size: 2.5rem;
      line-height: 3.2rem;
    }
  
    .hero-content p {
      width: 100%;
      font-size: 0.9rem;
    }
  
    .hero-img > div > img {
      width: 18rem;
    }
  
    .tech-icon {
      width: 3rem;
      height: 3rem;
    }
  
    .tech-icon img {
      width: 1.8rem;
      height: 1.8rem;
    }
  
    .typewriter-text {
      font-size: 1rem;
      line-height: 1.5rem;
      text-align: center;
    }
  }
  
  .typewriter-text {
    font-size: 1.2rem;
    font-weight: 400;
    line-height: 2rem;
    color: var(--text-color);
    min-height: 2rem;
    display: flex;
    align-items: center;
  }

  .cursor {
    font-weight: 100;
    color: var(--primary-color);
    margin-left: 2px;
    font-size: 1.2rem;
    transition: opacity 0.1s ease;
  }

  .cursor.visible {
    opacity: 1;
  }

  .cursor.hidden {
    opacity: 0;
  }

  @media (max-width: 1025px) {
    .typewriter-text {
      font-size: 1.1rem;
      line-height: 1.8rem;
    }

    .cursor {
      font-size: 1.1rem;
    }
  }

  @media (max-width: 768px) {
    .typewriter-text {
      font-size: 1rem;
      line-height: 1.6rem;
      text-align: center;
      justify-content: center;
    }

    .cursor {
      font-size: 1rem;
    }
  }

  @media (max-width: 480px) {
    .typewriter-text {
      font-size: 0.9rem;
      line-height: 1.4rem;
    }

    .cursor {
      font-size: 0.9rem;
    }
  }
