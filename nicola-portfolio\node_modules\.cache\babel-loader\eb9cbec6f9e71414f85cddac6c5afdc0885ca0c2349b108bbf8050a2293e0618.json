{"ast": null, "code": "import { Line, BufferGeometry, Float32BufferAttribute, LineBasicMaterial, Mesh, MeshBasicMaterial, BackSide } from \"three\";\nclass RectAreaLightHelper extends Line {\n  constructor(light, color) {\n    const positions = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, -1, 0, 1, 1, 0];\n    const geometry = new BufferGeometry();\n    geometry.setAttribute(\"position\", new Float32BufferAttribute(positions, 3));\n    geometry.computeBoundingSphere();\n    const material = new LineBasicMaterial({\n      fog: false\n    });\n    super(geometry, material);\n    this.light = light;\n    this.color = color;\n    this.type = \"RectAreaLightHelper\";\n    const positions2 = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, 1, 0, -1, -1, 0, 1, -1, 0];\n    const geometry2 = new BufferGeometry();\n    geometry2.setAttribute(\"position\", new Float32BufferAttribute(positions2, 3));\n    geometry2.computeBoundingSphere();\n    this.add(new Mesh(geometry2, new MeshBasicMaterial({\n      side: BackSide,\n      fog: false\n    })));\n  }\n  updateMatrixWorld() {\n    this.scale.set(0.5 * this.light.width, 0.5 * this.light.height, 1);\n    if (this.color !== void 0) {\n      this.material.color.set(this.color);\n      this.children[0].material.color.set(this.color);\n    } else {\n      this.material.color.copy(this.light.color).multiplyScalar(this.light.intensity);\n      const c = this.material.color;\n      const max = Math.max(c.r, c.g, c.b);\n      if (max > 1) c.multiplyScalar(1 / max);\n      this.children[0].material.color.copy(this.material.color);\n    }\n    this.matrixWorld.extractRotation(this.light.matrixWorld).scale(this.scale).copyPosition(this.light.matrixWorld);\n    this.children[0].matrixWorld.copy(this.matrixWorld);\n  }\n  dispose() {\n    this.geometry.dispose();\n    this.material.dispose();\n    this.children[0].geometry.dispose();\n    this.children[0].material.dispose();\n  }\n}\nexport { RectAreaLightHelper };", "map": {"version": 3, "names": ["RectAreaLightHelper", "Line", "constructor", "light", "color", "positions", "geometry", "BufferGeometry", "setAttribute", "Float32BufferAttribute", "computeBoundingSphere", "material", "LineBasicMaterial", "fog", "type", "positions2", "geometry2", "add", "<PERSON><PERSON>", "MeshBasicMaterial", "side", "BackSide", "updateMatrixWorld", "scale", "set", "width", "height", "children", "copy", "multiplyScalar", "intensity", "c", "max", "Math", "r", "g", "b", "matrixWorld", "extractRotation", "copyPosition", "dispose"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\helpers\\RectAreaLightHelper.js"], "sourcesContent": ["import {\n  BackSide,\n  BufferGeometry,\n  Float32BufferAttribute,\n  Line,\n  LineBasicMaterial,\n  Mesh,\n  MeshBasicMaterial,\n} from 'three'\n\n/**\n *  This helper must be added as a child of the light\n */\n\nclass RectAreaLightHelper extends Line {\n  constructor(light, color) {\n    const positions = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, -1, 0, 1, 1, 0]\n\n    const geometry = new BufferGeometry()\n    geometry.setAttribute('position', new Float32BufferAttribute(positions, 3))\n    geometry.computeBoundingSphere()\n\n    const material = new LineBasicMaterial({ fog: false })\n\n    super(geometry, material)\n\n    this.light = light\n    this.color = color // optional hardwired color for the helper\n    this.type = 'RectAreaLightHelper'\n\n    //\n\n    const positions2 = [1, 1, 0, -1, 1, 0, -1, -1, 0, 1, 1, 0, -1, -1, 0, 1, -1, 0]\n\n    const geometry2 = new BufferGeometry()\n    geometry2.setAttribute('position', new Float32BufferAttribute(positions2, 3))\n    geometry2.computeBoundingSphere()\n\n    this.add(new Mesh(geometry2, new MeshBasicMaterial({ side: BackSide, fog: false })))\n  }\n\n  updateMatrixWorld() {\n    this.scale.set(0.5 * this.light.width, 0.5 * this.light.height, 1)\n\n    if (this.color !== undefined) {\n      this.material.color.set(this.color)\n      this.children[0].material.color.set(this.color)\n    } else {\n      this.material.color.copy(this.light.color).multiplyScalar(this.light.intensity)\n\n      // prevent hue shift\n      const c = this.material.color\n      const max = Math.max(c.r, c.g, c.b)\n      if (max > 1) c.multiplyScalar(1 / max)\n\n      this.children[0].material.color.copy(this.material.color)\n    }\n\n    // ignore world scale on light\n    this.matrixWorld.extractRotation(this.light.matrixWorld).scale(this.scale).copyPosition(this.light.matrixWorld)\n\n    this.children[0].matrixWorld.copy(this.matrixWorld)\n  }\n\n  dispose() {\n    this.geometry.dispose()\n    this.material.dispose()\n    this.children[0].geometry.dispose()\n    this.children[0].material.dispose()\n  }\n}\n\nexport { RectAreaLightHelper }\n"], "mappings": ";AAcA,MAAMA,mBAAA,SAA4BC,IAAA,CAAK;EACrCC,YAAYC,KAAA,EAAOC,KAAA,EAAO;IACxB,MAAMC,SAAA,GAAY,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;IAElE,MAAMC,QAAA,GAAW,IAAIC,cAAA,CAAgB;IACrCD,QAAA,CAASE,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBJ,SAAA,EAAW,CAAC,CAAC;IAC1EC,QAAA,CAASI,qBAAA,CAAuB;IAEhC,MAAMC,QAAA,GAAW,IAAIC,iBAAA,CAAkB;MAAEC,GAAA,EAAK;IAAK,CAAE;IAErD,MAAMP,QAAA,EAAUK,QAAQ;IAExB,KAAKR,KAAA,GAAQA,KAAA;IACb,KAAKC,KAAA,GAAQA,KAAA;IACb,KAAKU,IAAA,GAAO;IAIZ,MAAMC,UAAA,GAAa,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC;IAE9E,MAAMC,SAAA,GAAY,IAAIT,cAAA,CAAgB;IACtCS,SAAA,CAAUR,YAAA,CAAa,YAAY,IAAIC,sBAAA,CAAuBM,UAAA,EAAY,CAAC,CAAC;IAC5EC,SAAA,CAAUN,qBAAA,CAAuB;IAEjC,KAAKO,GAAA,CAAI,IAAIC,IAAA,CAAKF,SAAA,EAAW,IAAIG,iBAAA,CAAkB;MAAEC,IAAA,EAAMC,QAAA;MAAUR,GAAA,EAAK;IAAK,CAAE,CAAC,CAAC;EACpF;EAEDS,kBAAA,EAAoB;IAClB,KAAKC,KAAA,CAAMC,GAAA,CAAI,MAAM,KAAKrB,KAAA,CAAMsB,KAAA,EAAO,MAAM,KAAKtB,KAAA,CAAMuB,MAAA,EAAQ,CAAC;IAEjE,IAAI,KAAKtB,KAAA,KAAU,QAAW;MAC5B,KAAKO,QAAA,CAASP,KAAA,CAAMoB,GAAA,CAAI,KAAKpB,KAAK;MAClC,KAAKuB,QAAA,CAAS,CAAC,EAAEhB,QAAA,CAASP,KAAA,CAAMoB,GAAA,CAAI,KAAKpB,KAAK;IACpD,OAAW;MACL,KAAKO,QAAA,CAASP,KAAA,CAAMwB,IAAA,CAAK,KAAKzB,KAAA,CAAMC,KAAK,EAAEyB,cAAA,CAAe,KAAK1B,KAAA,CAAM2B,SAAS;MAG9E,MAAMC,CAAA,GAAI,KAAKpB,QAAA,CAASP,KAAA;MACxB,MAAM4B,GAAA,GAAMC,IAAA,CAAKD,GAAA,CAAID,CAAA,CAAEG,CAAA,EAAGH,CAAA,CAAEI,CAAA,EAAGJ,CAAA,CAAEK,CAAC;MAClC,IAAIJ,GAAA,GAAM,GAAGD,CAAA,CAAEF,cAAA,CAAe,IAAIG,GAAG;MAErC,KAAKL,QAAA,CAAS,CAAC,EAAEhB,QAAA,CAASP,KAAA,CAAMwB,IAAA,CAAK,KAAKjB,QAAA,CAASP,KAAK;IACzD;IAGD,KAAKiC,WAAA,CAAYC,eAAA,CAAgB,KAAKnC,KAAA,CAAMkC,WAAW,EAAEd,KAAA,CAAM,KAAKA,KAAK,EAAEgB,YAAA,CAAa,KAAKpC,KAAA,CAAMkC,WAAW;IAE9G,KAAKV,QAAA,CAAS,CAAC,EAAEU,WAAA,CAAYT,IAAA,CAAK,KAAKS,WAAW;EACnD;EAEDG,QAAA,EAAU;IACR,KAAKlC,QAAA,CAASkC,OAAA,CAAS;IACvB,KAAK7B,QAAA,CAAS6B,OAAA,CAAS;IACvB,KAAKb,QAAA,CAAS,CAAC,EAAErB,QAAA,CAASkC,OAAA,CAAS;IACnC,KAAKb,QAAA,CAAS,CAAC,EAAEhB,QAAA,CAAS6B,OAAA,CAAS;EACpC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}