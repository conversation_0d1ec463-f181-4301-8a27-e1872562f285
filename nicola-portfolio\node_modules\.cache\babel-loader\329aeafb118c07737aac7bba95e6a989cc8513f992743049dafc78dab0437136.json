{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON><PERSON><PERSON>, BufferAttribute } from \"three\";\nlet bigEndianPlatform = null;\nfunction isBigEndianPlatform() {\n  if (bigEndianPlatform === null) {\n    const buffer = new ArrayBuffer(2),\n      uint8Array = new Uint8Array(buffer),\n      uint16Array = new Uint16Array(buffer);\n    uint8Array[0] = 170;\n    uint8Array[1] = 187;\n    bigEndianPlatform = uint16Array[0] === 43707;\n  }\n  return bigEndianPlatform;\n}\nconst InvertedEncodingTypes = [null, Float32Array, null, Int8Array, Int16Array, null, Int32Array, Uint8Array, Uint16Array, null, Uint32Array];\nconst getMethods = {\n  Uint16Array: \"getUint16\",\n  Uint32Array: \"getUint32\",\n  Int16Array: \"getInt16\",\n  Int32Array: \"getInt32\",\n  Float32Array: \"getFloat32\",\n  Float64Array: \"getFloat64\"\n};\nfunction copyFromBuffer(sourceArrayBuffer, viewType, position, length, fromBigEndian) {\n  const bytesPerElement = viewType.BYTES_PER_ELEMENT;\n  let result;\n  if (fromBigEndian === isBigEndianPlatform() || bytesPerElement === 1) {\n    result = new viewType(sourceArrayBuffer, position, length);\n  } else {\n    const readView = new DataView(sourceArrayBuffer, position, length * bytesPerElement),\n      getMethod = getMethods[viewType.name],\n      littleEndian = !fromBigEndian;\n    result = new viewType(length);\n    for (let i = 0; i < length; i++) {\n      result[i] = readView[getMethod](i * bytesPerElement, littleEndian);\n    }\n  }\n  return result;\n}\nfunction decodePrwm(buffer) {\n  const array = new Uint8Array(buffer),\n    version = array[0];\n  let flags = array[1];\n  const indexedGeometry = !!(flags >> 7 & 1),\n    indicesType = flags >> 6 & 1,\n    bigEndian = (flags >> 5 & 1) === 1,\n    attributesNumber = flags & 31;\n  let valuesNumber = 0,\n    indicesNumber = 0;\n  if (bigEndian) {\n    valuesNumber = (array[2] << 16) + (array[3] << 8) + array[4];\n    indicesNumber = (array[5] << 16) + (array[6] << 8) + array[7];\n  } else {\n    valuesNumber = array[2] + (array[3] << 8) + (array[4] << 16);\n    indicesNumber = array[5] + (array[6] << 8) + (array[7] << 16);\n  }\n  if (version === 0) {\n    throw new Error(\"PRWM decoder: Invalid format version: 0\");\n  } else if (version !== 1) {\n    throw new Error(\"PRWM decoder: Unsupported format version: \" + version);\n  }\n  if (!indexedGeometry) {\n    if (indicesType !== 0) {\n      throw new Error(\"PRWM decoder: Indices type must be set to 0 for non-indexed geometries\");\n    } else if (indicesNumber !== 0) {\n      throw new Error(\"PRWM decoder: Number of indices must be set to 0 for non-indexed geometries\");\n    }\n  }\n  let pos = 8;\n  const attributes = {};\n  for (let i = 0; i < attributesNumber; i++) {\n    let attributeName = \"\";\n    while (pos < array.length) {\n      const char = array[pos];\n      pos++;\n      if (char === 0) {\n        break;\n      } else {\n        attributeName += String.fromCharCode(char);\n      }\n    }\n    flags = array[pos];\n    const attributeType = flags >> 7 & 1;\n    const cardinality = (flags >> 4 & 3) + 1;\n    const encodingType = flags & 15;\n    const arrayType = InvertedEncodingTypes[encodingType];\n    pos++;\n    pos = Math.ceil(pos / 4) * 4;\n    const values = copyFromBuffer(buffer, arrayType, pos, cardinality * valuesNumber, bigEndian);\n    pos += arrayType.BYTES_PER_ELEMENT * cardinality * valuesNumber;\n    attributes[attributeName] = {\n      type: attributeType,\n      cardinality,\n      values\n    };\n  }\n  pos = Math.ceil(pos / 4) * 4;\n  let indices = null;\n  if (indexedGeometry) {\n    indices = copyFromBuffer(buffer, indicesType === 1 ? Uint32Array : Uint16Array, pos, indicesNumber, bigEndian);\n  }\n  return {\n    version,\n    attributes,\n    indices\n  };\n}\nconst PRWMLoader = /* @__PURE__ */(() => {\n  class PRWMLoader2 extends Loader {\n    constructor(manager) {\n      super(manager);\n    }\n    load(url, onLoad, onProgress, onError) {\n      const scope = this;\n      const loader = new FileLoader(scope.manager);\n      loader.setPath(scope.path);\n      loader.setResponseType(\"arraybuffer\");\n      loader.setRequestHeader(scope.requestHeader);\n      loader.setWithCredentials(scope.withCredentials);\n      url = url.replace(/\\*/g, isBigEndianPlatform() ? \"be\" : \"le\");\n      loader.load(url, function (arrayBuffer) {\n        try {\n          onLoad(scope.parse(arrayBuffer));\n        } catch (e) {\n          if (onError) {\n            onError(e);\n          } else {\n            console.error(e);\n          }\n          scope.manager.itemError(url);\n        }\n      }, onProgress, onError);\n    }\n    parse(arrayBuffer) {\n      const data = decodePrwm(arrayBuffer),\n        attributesKey = Object.keys(data.attributes),\n        bufferGeometry = new BufferGeometry();\n      for (let i = 0; i < attributesKey.length; i++) {\n        const attribute = data.attributes[attributesKey[i]];\n        bufferGeometry.setAttribute(attributesKey[i], new BufferAttribute(attribute.values, attribute.cardinality, attribute.normalized));\n      }\n      if (data.indices !== null) {\n        bufferGeometry.setIndex(new BufferAttribute(data.indices, 1));\n      }\n      return bufferGeometry;\n    }\n    static isBigEndianPlatform() {\n      return isBigEndianPlatform();\n    }\n  }\n  return PRWMLoader2;\n})();\nexport { PRWMLoader };", "map": {"version": 3, "names": ["bigEndianPlatform", "isBigEndianPlatform", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uint8Array", "Uint8Array", "uint16Array", "Uint16Array", "InvertedEncodingTypes", "Float32Array", "Int8Array", "Int16Array", "Int32Array", "Uint32Array", "getMethods", "Float64Array", "copyFromBuffer", "sourceArrayBuffer", "viewType", "position", "length", "fromBigEndian", "bytesPerElement", "BYTES_PER_ELEMENT", "result", "readView", "DataView", "getMethod", "name", "littleEndian", "i", "decodePrwm", "array", "version", "flags", "indexedGeometry", "indicesType", "bigEndian", "attributesNumber", "valuesNumber", "indicesNumber", "Error", "pos", "attributes", "attributeName", "char", "String", "fromCharCode", "attributeType", "cardinality", "encodingType", "arrayType", "Math", "ceil", "values", "type", "indices", "PRW<PERSON><PERSON>der", "PRWMLoader2", "Loader", "constructor", "manager", "load", "url", "onLoad", "onProgress", "onError", "scope", "loader", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ath", "path", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "replace", "arrayBuffer", "parse", "e", "console", "error", "itemError", "data", "<PERSON><PERSON><PERSON>", "Object", "keys", "bufferGeometry", "BufferGeometry", "attribute", "setAttribute", "BufferAttribute", "normalized", "setIndex"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\loaders\\PRWMLoader.js"], "sourcesContent": ["import { <PERSON>uffer<PERSON>tt<PERSON><PERSON><PERSON>, <PERSON>ufferGeometry, FileLoader, Loader } from 'three'\n\n/**\n * See https://github.com/kchapelier/PRWM for more informations about this file format\n */\n\nlet bigEndianPlatform = null\n\n/**\n * Check if the endianness of the platform is big-endian (most significant bit first)\n * @returns {boolean} True if big-endian, false if little-endian\n */\nfunction isBigEndianPlatform() {\n  if (bigEndianPlatform === null) {\n    const buffer = new ArrayBuffer(2),\n      uint8Array = new Uint8Array(buffer),\n      uint16Array = new Uint16Array(buffer)\n\n    uint8Array[0] = 0xaa // set first byte\n    uint8Array[1] = 0xbb // set second byte\n    bigEndianPlatform = uint16Array[0] === 0xaabb\n  }\n\n  return bigEndianPlatform\n}\n\n// match the values defined in the spec to the TypedArray types\nconst InvertedEncodingTypes = [\n  null,\n  Float32Array,\n  null,\n  Int8Array,\n  Int16Array,\n  null,\n  Int32<PERSON>rray,\n  U<PERSON>8<PERSON><PERSON>y,\n  Uint16<PERSON>rray,\n  null,\n  Uint32Array,\n]\n\n// define the method to use on a DataView, corresponding the TypedArray type\nconst getMethods = {\n  Uint16Array: 'getUint16',\n  Uint32Array: 'getUint32',\n  Int16Array: 'getInt16',\n  Int32Array: 'getInt32',\n  Float32Array: 'getFloat32',\n  Float64Array: 'getFloat64',\n}\n\nfunction copyFromBuffer(sourceArrayBuffer, viewType, position, length, fromBigEndian) {\n  const bytesPerElement = viewType.BYTES_PER_ELEMENT\n  let result\n\n  if (fromBigEndian === isBigEndianPlatform() || bytesPerElement === 1) {\n    result = new viewType(sourceArrayBuffer, position, length)\n  } else {\n    const readView = new DataView(sourceArrayBuffer, position, length * bytesPerElement),\n      getMethod = getMethods[viewType.name],\n      littleEndian = !fromBigEndian\n\n    result = new viewType(length)\n\n    for (let i = 0; i < length; i++) {\n      result[i] = readView[getMethod](i * bytesPerElement, littleEndian)\n    }\n  }\n\n  return result\n}\n\nfunction decodePrwm(buffer) {\n  const array = new Uint8Array(buffer),\n    version = array[0]\n\n  let flags = array[1]\n\n  const indexedGeometry = !!((flags >> 7) & 0x01),\n    indicesType = (flags >> 6) & 0x01,\n    bigEndian = ((flags >> 5) & 0x01) === 1,\n    attributesNumber = flags & 0x1f\n\n  let valuesNumber = 0,\n    indicesNumber = 0\n\n  if (bigEndian) {\n    valuesNumber = (array[2] << 16) + (array[3] << 8) + array[4]\n    indicesNumber = (array[5] << 16) + (array[6] << 8) + array[7]\n  } else {\n    valuesNumber = array[2] + (array[3] << 8) + (array[4] << 16)\n    indicesNumber = array[5] + (array[6] << 8) + (array[7] << 16)\n  }\n\n  /** PRELIMINARY CHECKS **/\n\n  if (version === 0) {\n    throw new Error('PRWM decoder: Invalid format version: 0')\n  } else if (version !== 1) {\n    throw new Error('PRWM decoder: Unsupported format version: ' + version)\n  }\n\n  if (!indexedGeometry) {\n    if (indicesType !== 0) {\n      throw new Error('PRWM decoder: Indices type must be set to 0 for non-indexed geometries')\n    } else if (indicesNumber !== 0) {\n      throw new Error('PRWM decoder: Number of indices must be set to 0 for non-indexed geometries')\n    }\n  }\n\n  /** PARSING **/\n\n  let pos = 8\n\n  const attributes = {}\n\n  for (let i = 0; i < attributesNumber; i++) {\n    let attributeName = ''\n\n    while (pos < array.length) {\n      const char = array[pos]\n      pos++\n\n      if (char === 0) {\n        break\n      } else {\n        attributeName += String.fromCharCode(char)\n      }\n    }\n\n    flags = array[pos]\n\n    const attributeType = (flags >> 7) & 0x01\n    const cardinality = ((flags >> 4) & 0x03) + 1\n    const encodingType = flags & 0x0f\n    const arrayType = InvertedEncodingTypes[encodingType]\n\n    pos++\n\n    // padding to next multiple of 4\n    pos = Math.ceil(pos / 4) * 4\n\n    const values = copyFromBuffer(buffer, arrayType, pos, cardinality * valuesNumber, bigEndian)\n\n    pos += arrayType.BYTES_PER_ELEMENT * cardinality * valuesNumber\n\n    attributes[attributeName] = {\n      type: attributeType,\n      cardinality: cardinality,\n      values: values,\n    }\n  }\n\n  pos = Math.ceil(pos / 4) * 4\n\n  let indices = null\n\n  if (indexedGeometry) {\n    indices = copyFromBuffer(buffer, indicesType === 1 ? Uint32Array : Uint16Array, pos, indicesNumber, bigEndian)\n  }\n\n  return {\n    version: version,\n    attributes: attributes,\n    indices: indices,\n  }\n}\n\n// Define the public interface\n\nconst PRWMLoader = /* @__PURE__ */ (() => {\n  class PRWMLoader extends Loader {\n    constructor(manager) {\n      super(manager)\n    }\n\n    load(url, onLoad, onProgress, onError) {\n      const scope = this\n\n      const loader = new FileLoader(scope.manager)\n      loader.setPath(scope.path)\n      loader.setResponseType('arraybuffer')\n      loader.setRequestHeader(scope.requestHeader)\n      loader.setWithCredentials(scope.withCredentials)\n\n      url = url.replace(/\\*/g, isBigEndianPlatform() ? 'be' : 'le')\n\n      loader.load(\n        url,\n        function (arrayBuffer) {\n          try {\n            onLoad(scope.parse(arrayBuffer))\n          } catch (e) {\n            if (onError) {\n              onError(e)\n            } else {\n              console.error(e)\n            }\n\n            scope.manager.itemError(url)\n          }\n        },\n        onProgress,\n        onError,\n      )\n    }\n\n    parse(arrayBuffer) {\n      const data = decodePrwm(arrayBuffer),\n        attributesKey = Object.keys(data.attributes),\n        bufferGeometry = new BufferGeometry()\n\n      for (let i = 0; i < attributesKey.length; i++) {\n        const attribute = data.attributes[attributesKey[i]]\n        bufferGeometry.setAttribute(\n          attributesKey[i],\n          new BufferAttribute(attribute.values, attribute.cardinality, attribute.normalized),\n        )\n      }\n\n      if (data.indices !== null) {\n        bufferGeometry.setIndex(new BufferAttribute(data.indices, 1))\n      }\n\n      return bufferGeometry\n    }\n\n    static isBigEndianPlatform() {\n      return isBigEndianPlatform()\n    }\n  }\n\n  return PRWMLoader\n})()\n\nexport { PRWMLoader }\n"], "mappings": ";AAMA,IAAIA,iBAAA,GAAoB;AAMxB,SAASC,oBAAA,EAAsB;EAC7B,IAAID,iBAAA,KAAsB,MAAM;IAC9B,MAAME,MAAA,GAAS,IAAIC,WAAA,CAAY,CAAC;MAC9BC,UAAA,GAAa,IAAIC,UAAA,CAAWH,MAAM;MAClCI,WAAA,GAAc,IAAIC,WAAA,CAAYL,MAAM;IAEtCE,UAAA,CAAW,CAAC,IAAI;IAChBA,UAAA,CAAW,CAAC,IAAI;IAChBJ,iBAAA,GAAoBM,WAAA,CAAY,CAAC,MAAM;EACxC;EAED,OAAON,iBAAA;AACT;AAGA,MAAMQ,qBAAA,GAAwB,CAC5B,MACAC,YAAA,EACA,MACAC,SAAA,EACAC,UAAA,EACA,MACAC,UAAA,EACAP,UAAA,EACAE,WAAA,EACA,MACAM,WAAA,CACF;AAGA,MAAMC,UAAA,GAAa;EACjBP,WAAA,EAAa;EACbM,WAAA,EAAa;EACbF,UAAA,EAAY;EACZC,UAAA,EAAY;EACZH,YAAA,EAAc;EACdM,YAAA,EAAc;AAChB;AAEA,SAASC,eAAeC,iBAAA,EAAmBC,QAAA,EAAUC,QAAA,EAAUC,MAAA,EAAQC,aAAA,EAAe;EACpF,MAAMC,eAAA,GAAkBJ,QAAA,CAASK,iBAAA;EACjC,IAAIC,MAAA;EAEJ,IAAIH,aAAA,KAAkBpB,mBAAA,MAAyBqB,eAAA,KAAoB,GAAG;IACpEE,MAAA,GAAS,IAAIN,QAAA,CAASD,iBAAA,EAAmBE,QAAA,EAAUC,MAAM;EAC7D,OAAS;IACL,MAAMK,QAAA,GAAW,IAAIC,QAAA,CAAST,iBAAA,EAAmBE,QAAA,EAAUC,MAAA,GAASE,eAAe;MACjFK,SAAA,GAAYb,UAAA,CAAWI,QAAA,CAASU,IAAI;MACpCC,YAAA,GAAe,CAACR,aAAA;IAElBG,MAAA,GAAS,IAAIN,QAAA,CAASE,MAAM;IAE5B,SAASU,CAAA,GAAI,GAAGA,CAAA,GAAIV,MAAA,EAAQU,CAAA,IAAK;MAC/BN,MAAA,CAAOM,CAAC,IAAIL,QAAA,CAASE,SAAS,EAAEG,CAAA,GAAIR,eAAA,EAAiBO,YAAY;IAClE;EACF;EAED,OAAOL,MAAA;AACT;AAEA,SAASO,WAAW7B,MAAA,EAAQ;EAC1B,MAAM8B,KAAA,GAAQ,IAAI3B,UAAA,CAAWH,MAAM;IACjC+B,OAAA,GAAUD,KAAA,CAAM,CAAC;EAEnB,IAAIE,KAAA,GAAQF,KAAA,CAAM,CAAC;EAEnB,MAAMG,eAAA,GAAkB,CAAC,EAAGD,KAAA,IAAS,IAAK;IACxCE,WAAA,GAAeF,KAAA,IAAS,IAAK;IAC7BG,SAAA,IAAcH,KAAA,IAAS,IAAK,OAAU;IACtCI,gBAAA,GAAmBJ,KAAA,GAAQ;EAE7B,IAAIK,YAAA,GAAe;IACjBC,aAAA,GAAgB;EAElB,IAAIH,SAAA,EAAW;IACbE,YAAA,IAAgBP,KAAA,CAAM,CAAC,KAAK,OAAOA,KAAA,CAAM,CAAC,KAAK,KAAKA,KAAA,CAAM,CAAC;IAC3DQ,aAAA,IAAiBR,KAAA,CAAM,CAAC,KAAK,OAAOA,KAAA,CAAM,CAAC,KAAK,KAAKA,KAAA,CAAM,CAAC;EAChE,OAAS;IACLO,YAAA,GAAeP,KAAA,CAAM,CAAC,KAAKA,KAAA,CAAM,CAAC,KAAK,MAAMA,KAAA,CAAM,CAAC,KAAK;IACzDQ,aAAA,GAAgBR,KAAA,CAAM,CAAC,KAAKA,KAAA,CAAM,CAAC,KAAK,MAAMA,KAAA,CAAM,CAAC,KAAK;EAC3D;EAID,IAAIC,OAAA,KAAY,GAAG;IACjB,MAAM,IAAIQ,KAAA,CAAM,yCAAyC;EAC7D,WAAaR,OAAA,KAAY,GAAG;IACxB,MAAM,IAAIQ,KAAA,CAAM,+CAA+CR,OAAO;EACvE;EAED,IAAI,CAACE,eAAA,EAAiB;IACpB,IAAIC,WAAA,KAAgB,GAAG;MACrB,MAAM,IAAIK,KAAA,CAAM,wEAAwE;IAC9F,WAAeD,aAAA,KAAkB,GAAG;MAC9B,MAAM,IAAIC,KAAA,CAAM,6EAA6E;IAC9F;EACF;EAID,IAAIC,GAAA,GAAM;EAEV,MAAMC,UAAA,GAAa,CAAE;EAErB,SAASb,CAAA,GAAI,GAAGA,CAAA,GAAIQ,gBAAA,EAAkBR,CAAA,IAAK;IACzC,IAAIc,aAAA,GAAgB;IAEpB,OAAOF,GAAA,GAAMV,KAAA,CAAMZ,MAAA,EAAQ;MACzB,MAAMyB,IAAA,GAAOb,KAAA,CAAMU,GAAG;MACtBA,GAAA;MAEA,IAAIG,IAAA,KAAS,GAAG;QACd;MACR,OAAa;QACLD,aAAA,IAAiBE,MAAA,CAAOC,YAAA,CAAaF,IAAI;MAC1C;IACF;IAEDX,KAAA,GAAQF,KAAA,CAAMU,GAAG;IAEjB,MAAMM,aAAA,GAAiBd,KAAA,IAAS,IAAK;IACrC,MAAMe,WAAA,IAAgBf,KAAA,IAAS,IAAK,KAAQ;IAC5C,MAAMgB,YAAA,GAAehB,KAAA,GAAQ;IAC7B,MAAMiB,SAAA,GAAY3C,qBAAA,CAAsB0C,YAAY;IAEpDR,GAAA;IAGAA,GAAA,GAAMU,IAAA,CAAKC,IAAA,CAAKX,GAAA,GAAM,CAAC,IAAI;IAE3B,MAAMY,MAAA,GAAStC,cAAA,CAAed,MAAA,EAAQiD,SAAA,EAAWT,GAAA,EAAKO,WAAA,GAAcV,YAAA,EAAcF,SAAS;IAE3FK,GAAA,IAAOS,SAAA,CAAU5B,iBAAA,GAAoB0B,WAAA,GAAcV,YAAA;IAEnDI,UAAA,CAAWC,aAAa,IAAI;MAC1BW,IAAA,EAAMP,aAAA;MACNC,WAAA;MACAK;IACD;EACF;EAEDZ,GAAA,GAAMU,IAAA,CAAKC,IAAA,CAAKX,GAAA,GAAM,CAAC,IAAI;EAE3B,IAAIc,OAAA,GAAU;EAEd,IAAIrB,eAAA,EAAiB;IACnBqB,OAAA,GAAUxC,cAAA,CAAed,MAAA,EAAQkC,WAAA,KAAgB,IAAIvB,WAAA,GAAcN,WAAA,EAAamC,GAAA,EAAKF,aAAA,EAAeH,SAAS;EAC9G;EAED,OAAO;IACLJ,OAAA;IACAU,UAAA;IACAa;EACD;AACH;AAIK,MAACC,UAAA,GAA8B,sBAAM;EACxC,MAAMC,WAAA,SAAmBC,MAAA,CAAO;IAC9BC,YAAYC,OAAA,EAAS;MACnB,MAAMA,OAAO;IACd;IAEDC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;MACrC,MAAMC,KAAA,GAAQ;MAEd,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAWF,KAAA,CAAMN,OAAO;MAC3CO,MAAA,CAAOE,OAAA,CAAQH,KAAA,CAAMI,IAAI;MACzBH,MAAA,CAAOI,eAAA,CAAgB,aAAa;MACpCJ,MAAA,CAAOK,gBAAA,CAAiBN,KAAA,CAAMO,aAAa;MAC3CN,MAAA,CAAOO,kBAAA,CAAmBR,KAAA,CAAMS,eAAe;MAE/Cb,GAAA,GAAMA,GAAA,CAAIc,OAAA,CAAQ,OAAO5E,mBAAA,CAAqB,IAAG,OAAO,IAAI;MAE5DmE,MAAA,CAAON,IAAA,CACLC,GAAA,EACA,UAAUe,WAAA,EAAa;QACrB,IAAI;UACFd,MAAA,CAAOG,KAAA,CAAMY,KAAA,CAAMD,WAAW,CAAC;QAChC,SAAQE,CAAA,EAAP;UACA,IAAId,OAAA,EAAS;YACXA,OAAA,CAAQc,CAAC;UACvB,OAAmB;YACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;UAChB;UAEDb,KAAA,CAAMN,OAAA,CAAQsB,SAAA,CAAUpB,GAAG;QAC5B;MACF,GACDE,UAAA,EACAC,OACD;IACF;IAEDa,MAAMD,WAAA,EAAa;MACjB,MAAMM,IAAA,GAAOrD,UAAA,CAAW+C,WAAW;QACjCO,aAAA,GAAgBC,MAAA,CAAOC,IAAA,CAAKH,IAAA,CAAKzC,UAAU;QAC3C6C,cAAA,GAAiB,IAAIC,cAAA,CAAgB;MAEvC,SAAS3D,CAAA,GAAI,GAAGA,CAAA,GAAIuD,aAAA,CAAcjE,MAAA,EAAQU,CAAA,IAAK;QAC7C,MAAM4D,SAAA,GAAYN,IAAA,CAAKzC,UAAA,CAAW0C,aAAA,CAAcvD,CAAC,CAAC;QAClD0D,cAAA,CAAeG,YAAA,CACbN,aAAA,CAAcvD,CAAC,GACf,IAAI8D,eAAA,CAAgBF,SAAA,CAAUpC,MAAA,EAAQoC,SAAA,CAAUzC,WAAA,EAAayC,SAAA,CAAUG,UAAU,CAClF;MACF;MAED,IAAIT,IAAA,CAAK5B,OAAA,KAAY,MAAM;QACzBgC,cAAA,CAAeM,QAAA,CAAS,IAAIF,eAAA,CAAgBR,IAAA,CAAK5B,OAAA,EAAS,CAAC,CAAC;MAC7D;MAED,OAAOgC,cAAA;IACR;IAED,OAAOvF,oBAAA,EAAsB;MAC3B,OAAOA,mBAAA,CAAqB;IAC7B;EACF;EAED,OAAOyD,WAAA;AACT,GAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}