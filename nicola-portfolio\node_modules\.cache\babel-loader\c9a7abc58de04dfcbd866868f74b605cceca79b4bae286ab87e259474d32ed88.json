{"ast": null, "code": "import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>s, FileLoader, TextureLoader, Vector3, Quaternion, Matrix4, Skeleton, BufferGeometry, MeshLambertMaterial, BufferAttribute, Mesh, SkinnedMesh, Object3D, MeshPhongMaterial, Bone, Color } from \"three\";\nclass AssimpLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    var scope = this;\n    var path = scope.path === \"\" ? LoaderUtils.extractUrlBase(url) : scope.path;\n    var loader = new FileLoader(scope.manager);\n    loader.setPath(scope.path);\n    loader.setResponseType(\"arraybuffer\");\n    loader.setRequestHeader(scope.requestHeader);\n    loader.setWithCredentials(scope.withCredentials);\n    loader.load(url, function (buffer) {\n      try {\n        onLoad(scope.parse(buffer, path));\n      } catch (e) {\n        if (onError) {\n          onError(e);\n        } else {\n          console.error(e);\n        }\n        scope.manager.itemError(url);\n      }\n    }, onProgress, onError);\n  }\n  parse(buffer, path) {\n    var textureLoader = new TextureLoader(this.manager);\n    textureLoader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin);\n    var Virtulous = {};\n    Virtulous.KeyFrame = class {\n      constructor(time, matrix) {\n        this.time = time;\n        this.matrix = matrix.clone();\n        this.position = new Vector3();\n        this.quaternion = new Quaternion();\n        this.scale = new Vector3(1, 1, 1);\n        this.matrix.decompose(this.position, this.quaternion, this.scale);\n        this.clone = function () {\n          var n = new Virtulous.KeyFrame(this.time, this.matrix);\n          return n;\n        };\n        this.lerp = function (nextKey, time2) {\n          time2 -= this.time;\n          var dist = nextKey.time - this.time;\n          var l = time2 / dist;\n          var l2 = 1 - l;\n          var keypos = this.position;\n          var keyrot = this.quaternion;\n          var key2pos = nextKey.position;\n          var key2rot = nextKey.quaternion;\n          Virtulous.KeyFrame.tempAniPos.x = keypos.x * l2 + key2pos.x * l;\n          Virtulous.KeyFrame.tempAniPos.y = keypos.y * l2 + key2pos.y * l;\n          Virtulous.KeyFrame.tempAniPos.z = keypos.z * l2 + key2pos.z * l;\n          Virtulous.KeyFrame.tempAniQuat.set(keyrot.x, keyrot.y, keyrot.z, keyrot.w);\n          Virtulous.KeyFrame.tempAniQuat.slerp(key2rot, l);\n          return Virtulous.KeyFrame.tempAniMatrix.compose(Virtulous.KeyFrame.tempAniPos, Virtulous.KeyFrame.tempAniQuat, Virtulous.KeyFrame.tempAniScale);\n        };\n      }\n    };\n    Virtulous.KeyFrame.tempAniPos = new Vector3();\n    Virtulous.KeyFrame.tempAniQuat = new Quaternion();\n    Virtulous.KeyFrame.tempAniScale = new Vector3(1, 1, 1);\n    Virtulous.KeyFrame.tempAniMatrix = new Matrix4();\n    Virtulous.KeyFrameTrack = function () {\n      this.keys = [];\n      this.target = null;\n      this.time = 0;\n      this.length = 0;\n      this._accelTable = {};\n      this.fps = 20;\n      this.addKey = function (key) {\n        this.keys.push(key);\n      };\n      this.init = function () {\n        this.sortKeys();\n        if (this.keys.length > 0) this.length = this.keys[this.keys.length - 1].time;else this.length = 0;\n        if (!this.fps) return;\n        for (let j = 0; j < this.length * this.fps; j++) {\n          for (let i = 0; i < this.keys.length; i++) {\n            if (this.keys[i].time == j) {\n              this._accelTable[j] = i;\n              break;\n            } else if (this.keys[i].time < j / this.fps && this.keys[i + 1] && this.keys[i + 1].time >= j / this.fps) {\n              this._accelTable[j] = i;\n              break;\n            }\n          }\n        }\n      };\n      this.parseFromThree = function (data) {\n        var fps = data.fps;\n        this.target = data.node;\n        var track = data.hierarchy[0].keys;\n        for (let i = 0; i < track.length; i++) {\n          this.addKey(new Virtulous.KeyFrame(i / fps || track[i].time, track[i].targets[0].data));\n        }\n        this.init();\n      };\n      this.parseFromCollada = function (data) {\n        var track = data.keys;\n        var fps = this.fps;\n        for (let i = 0; i < track.length; i++) {\n          this.addKey(new Virtulous.KeyFrame(i / fps || track[i].time, track[i].matrix));\n        }\n        this.init();\n      };\n      this.sortKeys = function () {\n        this.keys.sort(this.keySortFunc);\n      };\n      this.keySortFunc = function (a, b) {\n        return a.time - b.time;\n      };\n      this.clone = function () {\n        var t = new Virtulous.KeyFrameTrack();\n        t.target = this.target;\n        t.time = this.time;\n        t.length = this.length;\n        for (let i = 0; i < this.keys.length; i++) {\n          t.addKey(this.keys[i].clone());\n        }\n        t.init();\n        return t;\n      };\n      this.reTarget = function (root, compareitor) {\n        if (!compareitor) compareitor = Virtulous.TrackTargetNodeNameCompare;\n        this.target = compareitor(root, this.target);\n      };\n      this.keySearchAccel = function (time) {\n        time *= this.fps;\n        time = Math.floor(time);\n        return this._accelTable[time] || 0;\n      };\n      this.setTime = function (time) {\n        time = Math.abs(time);\n        if (this.length) time = time % this.length + 0.05;\n        var key0 = null;\n        var key1 = null;\n        for (let i = this.keySearchAccel(time); i < this.keys.length; i++) {\n          if (this.keys[i].time == time) {\n            key0 = this.keys[i];\n            key1 = this.keys[i];\n            break;\n          } else if (this.keys[i].time < time && this.keys[i + 1] && this.keys[i + 1].time > time) {\n            key0 = this.keys[i];\n            key1 = this.keys[i + 1];\n            break;\n          } else if (this.keys[i].time < time && i == this.keys.length - 1) {\n            key0 = this.keys[i];\n            key1 = this.keys[0].clone();\n            key1.time += this.length + 0.05;\n            break;\n          }\n        }\n        if (key0 && key1 && key0 !== key1) {\n          this.target.matrixAutoUpdate = false;\n          this.target.matrix.copy(key0.lerp(key1, time));\n          this.target.matrixWorldNeedsUpdate = true;\n          return;\n        }\n        if (key0 && key1 && key0 == key1) {\n          this.target.matrixAutoUpdate = false;\n          this.target.matrix.copy(key0.matrix);\n          this.target.matrixWorldNeedsUpdate = true;\n          return;\n        }\n      };\n    };\n    Virtulous.TrackTargetNodeNameCompare = function (root, target) {\n      function find(node, name) {\n        if (node.name == name) return node;\n        for (let i = 0; i < node.children.length; i++) {\n          var r = find(node.children[i], name);\n          if (r) return r;\n        }\n        return null;\n      }\n      return find(root, target.name);\n    };\n    Virtulous.Animation = function () {\n      this.tracks = [];\n      this.length = 0;\n      this.addTrack = function (track) {\n        this.tracks.push(track);\n        this.length = Math.max(track.length, this.length);\n      };\n      this.setTime = function (time) {\n        this.time = time;\n        for (let i = 0; i < this.tracks.length; i++) this.tracks[i].setTime(time);\n      };\n      this.clone = function (target, compareitor) {\n        if (!compareitor) compareitor = Virtulous.TrackTargetNodeNameCompare;\n        var n = new Virtulous.Animation();\n        n.target = target;\n        for (let i = 0; i < this.tracks.length; i++) {\n          var track = this.tracks[i].clone();\n          track.reTarget(target, compareitor);\n          n.addTrack(track);\n        }\n        return n;\n      };\n    };\n    var ASSBIN_CHUNK_AICAMERA = 4660;\n    var ASSBIN_CHUNK_AILIGHT = 4661;\n    var ASSBIN_CHUNK_AITEXTURE = 4662;\n    var ASSBIN_CHUNK_AIMESH = 4663;\n    var ASSBIN_CHUNK_AINODEANIM = 4664;\n    var ASSBIN_CHUNK_AISCENE = 4665;\n    var ASSBIN_CHUNK_AIBONE = 4666;\n    var ASSBIN_CHUNK_AIANIMATION = 4667;\n    var ASSBIN_CHUNK_AINODE = 4668;\n    var ASSBIN_CHUNK_AIMATERIAL = 4669;\n    var ASSBIN_CHUNK_AIMATERIALPROPERTY = 4670;\n    var ASSBIN_MESH_HAS_POSITIONS = 1;\n    var ASSBIN_MESH_HAS_NORMALS = 2;\n    var ASSBIN_MESH_HAS_TANGENTS_AND_BITANGENTS = 4;\n    var ASSBIN_MESH_HAS_TEXCOORD_BASE = 256;\n    var ASSBIN_MESH_HAS_COLOR_BASE = 65536;\n    var AI_MAX_NUMBER_OF_COLOR_SETS = 1;\n    var AI_MAX_NUMBER_OF_TEXTURECOORDS = 4;\n    //! A directional light source has a well-defined direction\n    //! but is infinitely far away. That's quite a good\n    //! approximation for sun light.\n    var aiLightSource_DIRECTIONAL = 1;\n    //! A point light source has a well-defined position\n    //! in space but no direction - it emits light in all\n    //! directions. A normal bulb is a point light.\n    //! A spot light source emits light in a specific\n    //! angle. It has a position and a direction it is pointing to.\n    //! A good example for a spot light is a light spot in\n    //! sport arenas.\n    var aiLightSource_SPOT = 3;\n    //! The generic light level of the world, including the bounces\n    //! of all other lightsources.\n    //! Typically, there's at most one ambient light in a scene.\n    //! This light type doesn't have a valid position, direction, or\n    //! other properties, just a color.\n    var aiTextureType_DIFFUSE = 1;\n    var aiTextureType_NORMALS = 6;\n    var aiTextureType_OPACITY = 8;\n    var aiTextureType_LIGHTMAP = 10;\n    var BONESPERVERT = 4;\n    function ASSBIN_MESH_HAS_TEXCOORD(n) {\n      return ASSBIN_MESH_HAS_TEXCOORD_BASE << n;\n    }\n    function ASSBIN_MESH_HAS_COLOR(n) {\n      return ASSBIN_MESH_HAS_COLOR_BASE << n;\n    }\n    function markBones(scene) {\n      for (let i in scene.mMeshes) {\n        var mesh = scene.mMeshes[i];\n        for (let k in mesh.mBones) {\n          var boneNode = scene.findNode(mesh.mBones[k].mName);\n          if (boneNode) boneNode.isBone = true;\n        }\n      }\n    }\n    function cloneTreeToBones(root, scene) {\n      var rootBone = new Bone();\n      rootBone.matrix.copy(root.matrix);\n      rootBone.matrixWorld.copy(root.matrixWorld);\n      rootBone.position.copy(root.position);\n      rootBone.quaternion.copy(root.quaternion);\n      rootBone.scale.copy(root.scale);\n      scene.nodeCount++;\n      rootBone.name = \"bone_\" + root.name + scene.nodeCount.toString();\n      if (!scene.nodeToBoneMap[root.name]) scene.nodeToBoneMap[root.name] = [];\n      scene.nodeToBoneMap[root.name].push(rootBone);\n      for (let i in root.children) {\n        var child = cloneTreeToBones(root.children[i], scene);\n        rootBone.add(child);\n      }\n      return rootBone;\n    }\n    function sortWeights(indexes, weights) {\n      var pairs = [];\n      for (let i = 0; i < indexes.length; i++) {\n        pairs.push({\n          i: indexes[i],\n          w: weights[i]\n        });\n      }\n      pairs.sort(function (a, b) {\n        return b.w - a.w;\n      });\n      while (pairs.length < 4) {\n        pairs.push({\n          i: 0,\n          w: 0\n        });\n      }\n      if (pairs.length > 4) pairs.length = 4;\n      var sum = 0;\n      for (let i = 0; i < 4; i++) {\n        sum += pairs[i].w * pairs[i].w;\n      }\n      sum = Math.sqrt(sum);\n      for (let i = 0; i < 4; i++) {\n        pairs[i].w = pairs[i].w / sum;\n        indexes[i] = pairs[i].i;\n        weights[i] = pairs[i].w;\n      }\n    }\n    function findMatchingBone(root, name) {\n      if (root.name.indexOf(\"bone_\" + name) == 0) return root;\n      for (let i in root.children) {\n        var ret = findMatchingBone(root.children[i], name);\n        if (ret) return ret;\n      }\n      return void 0;\n    }\n    class aiMesh {\n      constructor() {\n        this.mPrimitiveTypes = 0;\n        this.mNumVertices = 0;\n        this.mNumFaces = 0;\n        this.mNumBones = 0;\n        this.mMaterialIndex = 0;\n        this.mVertices = [];\n        this.mNormals = [];\n        this.mTangents = [];\n        this.mBitangents = [];\n        this.mColors = [[]];\n        this.mTextureCoords = [[]];\n        this.mFaces = [];\n        this.mBones = [];\n        this.hookupSkeletons = function (scene) {\n          if (this.mBones.length == 0) return;\n          var allBones = [];\n          var offsetMatrix = [];\n          var skeletonRoot = scene.findNode(this.mBones[0].mName);\n          while (skeletonRoot.mParent && skeletonRoot.mParent.isBone) {\n            skeletonRoot = skeletonRoot.mParent;\n          }\n          var threeSkeletonRoot = skeletonRoot.toTHREE(scene);\n          var threeSkeletonRootBone = cloneTreeToBones(threeSkeletonRoot, scene);\n          this.threeNode.add(threeSkeletonRootBone);\n          for (let i = 0; i < this.mBones.length; i++) {\n            var bone = findMatchingBone(threeSkeletonRootBone, this.mBones[i].mName);\n            if (bone) {\n              var tbone = bone;\n              allBones.push(tbone);\n              offsetMatrix.push(this.mBones[i].mOffsetMatrix.toTHREE());\n            } else {\n              var skeletonRoot = scene.findNode(this.mBones[i].mName);\n              if (!skeletonRoot) return;\n              var threeSkeletonRoot = skeletonRoot.toTHREE(scene);\n              var threeSkeletonRootBone = cloneTreeToBones(threeSkeletonRoot, scene);\n              this.threeNode.add(threeSkeletonRootBone);\n              var bone = findMatchingBone(threeSkeletonRootBone, this.mBones[i].mName);\n              var tbone = bone;\n              allBones.push(tbone);\n              offsetMatrix.push(this.mBones[i].mOffsetMatrix.toTHREE());\n            }\n          }\n          var skeleton = new Skeleton(allBones, offsetMatrix);\n          this.threeNode.bind(skeleton, new Matrix4());\n          this.threeNode.material.skinning = true;\n        };\n        this.toTHREE = function (scene) {\n          if (this.threeNode) return this.threeNode;\n          var geometry = new BufferGeometry();\n          var mat;\n          if (scene.mMaterials[this.mMaterialIndex]) mat = scene.mMaterials[this.mMaterialIndex].toTHREE(scene);else mat = new MeshLambertMaterial();\n          geometry.setIndex(new BufferAttribute(new Uint32Array(this.mIndexArray), 1));\n          geometry.setAttribute(\"position\", new BufferAttribute(this.mVertexBuffer, 3));\n          if (this.mNormalBuffer && this.mNormalBuffer.length > 0) {\n            geometry.setAttribute(\"normal\", new BufferAttribute(this.mNormalBuffer, 3));\n          }\n          if (this.mColorBuffer && this.mColorBuffer.length > 0) {\n            geometry.setAttribute(\"color\", new BufferAttribute(this.mColorBuffer, 4));\n          }\n          if (this.mTexCoordsBuffers[0] && this.mTexCoordsBuffers[0].length > 0) {\n            geometry.setAttribute(\"uv\", new BufferAttribute(new Float32Array(this.mTexCoordsBuffers[0]), 2));\n          }\n          if (this.mTexCoordsBuffers[1] && this.mTexCoordsBuffers[1].length > 0) {\n            geometry.setAttribute(\"uv1\", new BufferAttribute(new Float32Array(this.mTexCoordsBuffers[1]), 2));\n          }\n          if (this.mTangentBuffer && this.mTangentBuffer.length > 0) {\n            geometry.setAttribute(\"tangents\", new BufferAttribute(this.mTangentBuffer, 3));\n          }\n          if (this.mBitangentBuffer && this.mBitangentBuffer.length > 0) {\n            geometry.setAttribute(\"bitangents\", new BufferAttribute(this.mBitangentBuffer, 3));\n          }\n          if (this.mBones.length > 0) {\n            var weights = [];\n            var bones = [];\n            for (let i = 0; i < this.mBones.length; i++) {\n              for (let j = 0; j < this.mBones[i].mWeights.length; j++) {\n                var weight = this.mBones[i].mWeights[j];\n                if (weight) {\n                  if (!weights[weight.mVertexId]) weights[weight.mVertexId] = [];\n                  if (!bones[weight.mVertexId]) bones[weight.mVertexId] = [];\n                  weights[weight.mVertexId].push(weight.mWeight);\n                  bones[weight.mVertexId].push(parseInt(i));\n                }\n              }\n            }\n            for (let i in bones) {\n              sortWeights(bones[i], weights[i]);\n            }\n            var _weights = [];\n            var _bones = [];\n            for (let i = 0; i < weights.length; i++) {\n              for (let j = 0; j < 4; j++) {\n                if (weights[i] && bones[i]) {\n                  _weights.push(weights[i][j]);\n                  _bones.push(bones[i][j]);\n                } else {\n                  _weights.push(0);\n                  _bones.push(0);\n                }\n              }\n            }\n            geometry.setAttribute(\"skinWeight\", new BufferAttribute(new Float32Array(_weights), BONESPERVERT));\n            geometry.setAttribute(\"skinIndex\", new BufferAttribute(new Float32Array(_bones), BONESPERVERT));\n          }\n          var mesh;\n          if (this.mBones.length == 0) mesh = new Mesh(geometry, mat);\n          if (this.mBones.length > 0) {\n            mesh = new SkinnedMesh(geometry, mat);\n            mesh.normalizeSkinWeights();\n          }\n          this.threeNode = mesh;\n          return mesh;\n        };\n      }\n    }\n    class aiFace {\n      constructor() {\n        this.mNumIndices = 0;\n        this.mIndices = [];\n      }\n    }\n    class aiVector3D {\n      constructor() {\n        this.x = 0;\n        this.y = 0;\n        this.z = 0;\n        this.toTHREE = function () {\n          return new Vector3(this.x, this.y, this.z);\n        };\n      }\n    }\n    class aiColor3D {\n      constructor() {\n        this.r = 0;\n        this.g = 0;\n        this.b = 0;\n        this.a = 0;\n        this.toTHREE = function () {\n          return new Color(this.r, this.g, this.b);\n        };\n      }\n    }\n    class aiQuaternion {\n      constructor() {\n        this.x = 0;\n        this.y = 0;\n        this.z = 0;\n        this.w = 0;\n        this.toTHREE = function () {\n          return new Quaternion(this.x, this.y, this.z, this.w);\n        };\n      }\n    }\n    class aiVertexWeight {\n      constructor() {\n        this.mVertexId = 0;\n        this.mWeight = 0;\n      }\n    }\n    class aiString {\n      constructor() {\n        this.data = [];\n        this.toString = function () {\n          var str = \"\";\n          this.data.forEach(function (i) {\n            str += String.fromCharCode(i);\n          });\n          return str.replace(/[^\\x20-\\x7E]+/g, \"\");\n        };\n      }\n    }\n    class aiVectorKey {\n      constructor() {\n        this.mTime = 0;\n        this.mValue = null;\n      }\n    }\n    class aiQuatKey {\n      constructor() {\n        this.mTime = 0;\n        this.mValue = null;\n      }\n    }\n    class aiNode {\n      constructor() {\n        this.mName = \"\";\n        this.mTransformation = [];\n        this.mNumChildren = 0;\n        this.mNumMeshes = 0;\n        this.mMeshes = [];\n        this.mChildren = [];\n        this.toTHREE = function (scene) {\n          if (this.threeNode) return this.threeNode;\n          var o = new Object3D();\n          o.name = this.mName;\n          o.matrix = this.mTransformation.toTHREE();\n          for (let i = 0; i < this.mChildren.length; i++) {\n            o.add(this.mChildren[i].toTHREE(scene));\n          }\n          for (let i = 0; i < this.mMeshes.length; i++) {\n            o.add(scene.mMeshes[this.mMeshes[i]].toTHREE(scene));\n          }\n          this.threeNode = o;\n          o.matrix.decompose(o.position, o.quaternion, o.scale);\n          return o;\n        };\n      }\n    }\n    class aiBone {\n      constructor() {\n        this.mName = \"\";\n        this.mNumWeights = 0;\n        this.mOffsetMatrix = 0;\n      }\n    }\n    class aiMaterialProperty {\n      constructor() {\n        this.mKey = \"\";\n        this.mSemantic = 0;\n        this.mIndex = 0;\n        this.mData = [];\n        this.mDataLength = 0;\n        this.mType = 0;\n        this.dataAsColor = function () {\n          var array = new Uint8Array(this.mData).buffer;\n          var reader = new DataView(array);\n          var r = reader.getFloat32(0, true);\n          var g = reader.getFloat32(4, true);\n          var b = reader.getFloat32(8, true);\n          return new Color(r, g, b);\n        };\n        this.dataAsFloat = function () {\n          var array = new Uint8Array(this.mData).buffer;\n          var reader = new DataView(array);\n          var r = reader.getFloat32(0, true);\n          return r;\n        };\n        this.dataAsBool = function () {\n          var array = new Uint8Array(this.mData).buffer;\n          var reader = new DataView(array);\n          var r = reader.getFloat32(0, true);\n          return !!r;\n        };\n        this.dataAsString = function () {\n          var s = new aiString();\n          s.data = this.mData;\n          return s.toString();\n        };\n        this.dataAsMap = function () {\n          var s = new aiString();\n          s.data = this.mData;\n          var path2 = s.toString();\n          path2 = path2.replace(/\\\\/g, \"/\");\n          if (path2.indexOf(\"/\") != -1) {\n            path2 = path2.substr(path2.lastIndexOf(\"/\") + 1);\n          }\n          return textureLoader.load(path2);\n        };\n      }\n    }\n    var namePropMapping = {\n      \"?mat.name\": \"name\",\n      \"$mat.shadingm\": \"shading\",\n      \"$mat.twosided\": \"twoSided\",\n      \"$mat.wireframe\": \"wireframe\",\n      \"$clr.ambient\": \"ambient\",\n      \"$clr.diffuse\": \"color\",\n      \"$clr.specular\": \"specular\",\n      \"$clr.emissive\": \"emissive\",\n      \"$clr.transparent\": \"transparent\",\n      \"$clr.reflective\": \"reflect\",\n      \"$mat.shininess\": \"shininess\",\n      \"$mat.reflectivity\": \"reflectivity\",\n      \"$mat.refracti\": \"refraction\",\n      \"$tex.file\": \"map\"\n    };\n    var nameTypeMapping = {\n      \"?mat.name\": \"string\",\n      \"$mat.shadingm\": \"bool\",\n      \"$mat.twosided\": \"bool\",\n      \"$mat.wireframe\": \"bool\",\n      \"$clr.ambient\": \"color\",\n      \"$clr.diffuse\": \"color\",\n      \"$clr.specular\": \"color\",\n      \"$clr.emissive\": \"color\",\n      \"$clr.transparent\": \"color\",\n      \"$clr.reflective\": \"color\",\n      \"$mat.shininess\": \"float\",\n      \"$mat.reflectivity\": \"float\",\n      \"$mat.refracti\": \"float\",\n      \"$tex.file\": \"map\"\n    };\n    class aiMaterial {\n      constructor() {\n        this.mNumAllocated = 0;\n        this.mNumProperties = 0;\n        this.mProperties = [];\n        this.toTHREE = function () {\n          var mat = new MeshPhongMaterial();\n          for (let i = 0; i < this.mProperties.length; i++) {\n            if (nameTypeMapping[this.mProperties[i].mKey] == \"float\") {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsFloat();\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == \"color\") {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsColor();\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == \"bool\") {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsBool();\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == \"string\") {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsString();\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == \"map\") {\n              var prop = this.mProperties[i];\n              if (prop.mSemantic == aiTextureType_DIFFUSE) mat.map = this.mProperties[i].dataAsMap();\n              if (prop.mSemantic == aiTextureType_NORMALS) mat.normalMap = this.mProperties[i].dataAsMap();\n              if (prop.mSemantic == aiTextureType_LIGHTMAP) mat.lightMap = this.mProperties[i].dataAsMap();\n              if (prop.mSemantic == aiTextureType_OPACITY) mat.alphaMap = this.mProperties[i].dataAsMap();\n            }\n          }\n          mat.ambient.r = 0.53;\n          mat.ambient.g = 0.53;\n          mat.ambient.b = 0.53;\n          mat.color.r = 1;\n          mat.color.g = 1;\n          mat.color.b = 1;\n          return mat;\n        };\n      }\n    }\n    function veclerp(v1, v2, l) {\n      var v = new Vector3();\n      var lm1 = 1 - l;\n      v.x = v1.x * l + v2.x * lm1;\n      v.y = v1.y * l + v2.y * lm1;\n      v.z = v1.z * l + v2.z * lm1;\n      return v;\n    }\n    function quatlerp(q1, q2, l) {\n      return q1.clone().slerp(q2, 1 - l);\n    }\n    function sampleTrack(keys, time, lne, lerp) {\n      if (keys.length == 1) return keys[0].mValue.toTHREE();\n      var dist = Infinity;\n      var key = null;\n      var nextKey = null;\n      for (let i = 0; i < keys.length; i++) {\n        var timeDist = Math.abs(keys[i].mTime - time);\n        if (timeDist < dist && keys[i].mTime <= time) {\n          dist = timeDist;\n          key = keys[i];\n          nextKey = keys[i + 1];\n        }\n      }\n      if (!key) {\n        return null;\n      } else if (nextKey) {\n        var dT = nextKey.mTime - key.mTime;\n        var T = key.mTime - time;\n        var l = T / dT;\n        return lerp(key.mValue.toTHREE(), nextKey.mValue.toTHREE(), l);\n      } else {\n        nextKey = keys[0].clone();\n        nextKey.mTime += lne;\n        var dT = nextKey.mTime - key.mTime;\n        var T = key.mTime - time;\n        var l = T / dT;\n        return lerp(key.mValue.toTHREE(), nextKey.mValue.toTHREE(), l);\n      }\n    }\n    class aiNodeAnim {\n      constructor() {\n        this.mNodeName = \"\";\n        this.mNumPositionKeys = 0;\n        this.mNumRotationKeys = 0;\n        this.mNumScalingKeys = 0;\n        this.mPositionKeys = [];\n        this.mRotationKeys = [];\n        this.mScalingKeys = [];\n        this.mPreState = \"\";\n        this.mPostState = \"\";\n        this.init = function (tps) {\n          if (!tps) tps = 1;\n          function t(t2) {\n            t2.mTime /= tps;\n          }\n          this.mPositionKeys.forEach(t);\n          this.mRotationKeys.forEach(t);\n          this.mScalingKeys.forEach(t);\n        };\n        this.sortKeys = function () {\n          function comp(a, b) {\n            return a.mTime - b.mTime;\n          }\n          this.mPositionKeys.sort(comp);\n          this.mRotationKeys.sort(comp);\n          this.mScalingKeys.sort(comp);\n        };\n        this.getLength = function () {\n          return Math.max(Math.max.apply(null, this.mPositionKeys.map(function (a) {\n            return a.mTime;\n          })), Math.max.apply(null, this.mRotationKeys.map(function (a) {\n            return a.mTime;\n          })), Math.max.apply(null, this.mScalingKeys.map(function (a) {\n            return a.mTime;\n          })));\n        };\n        this.toTHREE = function (o) {\n          this.sortKeys();\n          var length = this.getLength();\n          var track = new Virtulous.KeyFrameTrack();\n          for (let i = 0; i < length; i += 0.05) {\n            var matrix = new Matrix4();\n            var time = i;\n            var pos = sampleTrack(this.mPositionKeys, time, length, veclerp);\n            var scale = sampleTrack(this.mScalingKeys, time, length, veclerp);\n            var rotation = sampleTrack(this.mRotationKeys, time, length, quatlerp);\n            matrix.compose(pos, rotation, scale);\n            var key = new Virtulous.KeyFrame(time, matrix);\n            track.addKey(key);\n          }\n          track.target = o.findNode(this.mNodeName).toTHREE();\n          var tracks = [track];\n          if (o.nodeToBoneMap[this.mNodeName]) {\n            for (let i = 0; i < o.nodeToBoneMap[this.mNodeName].length; i++) {\n              var t2 = track.clone();\n              t2.target = o.nodeToBoneMap[this.mNodeName][i];\n              tracks.push(t2);\n            }\n          }\n          return tracks;\n        };\n      }\n    }\n    class aiAnimation {\n      constructor() {\n        this.mName = \"\";\n        this.mDuration = 0;\n        this.mTicksPerSecond = 0;\n        this.mNumChannels = 0;\n        this.mChannels = [];\n        this.toTHREE = function (root) {\n          var animationHandle = new Virtulous.Animation();\n          for (let i in this.mChannels) {\n            this.mChannels[i].init(this.mTicksPerSecond);\n            var tracks = this.mChannels[i].toTHREE(root);\n            for (let j in tracks) {\n              tracks[j].init();\n              animationHandle.addTrack(tracks[j]);\n            }\n          }\n          animationHandle.length = Math.max.apply(null, animationHandle.tracks.map(function (e) {\n            return e.length;\n          }));\n          return animationHandle;\n        };\n      }\n    }\n    class aiTexture {\n      constructor() {\n        this.mWidth = 0;\n        this.mHeight = 0;\n        this.texAchFormatHint = [];\n        this.pcData = [];\n      }\n    }\n    class aiLight {\n      constructor() {\n        this.mName = \"\";\n        this.mType = 0;\n        this.mAttenuationConstant = 0;\n        this.mAttenuationLinear = 0;\n        this.mAttenuationQuadratic = 0;\n        this.mAngleInnerCone = 0;\n        this.mAngleOuterCone = 0;\n        this.mColorDiffuse = null;\n        this.mColorSpecular = null;\n        this.mColorAmbient = null;\n      }\n    }\n    class aiCamera {\n      constructor() {\n        this.mName = \"\";\n        this.mPosition = null;\n        this.mLookAt = null;\n        this.mUp = null;\n        this.mHorizontalFOV = 0;\n        this.mClipPlaneNear = 0;\n        this.mClipPlaneFar = 0;\n        this.mAspect = 0;\n      }\n    }\n    class aiScene {\n      constructor() {\n        this.versionMajor = 0;\n        this.versionMinor = 0;\n        this.versionRevision = 0;\n        this.compileFlags = 0;\n        this.mFlags = 0;\n        this.mNumMeshes = 0;\n        this.mNumMaterials = 0;\n        this.mNumAnimations = 0;\n        this.mNumTextures = 0;\n        this.mNumLights = 0;\n        this.mNumCameras = 0;\n        this.mRootNode = null;\n        this.mMeshes = [];\n        this.mMaterials = [];\n        this.mAnimations = [];\n        this.mLights = [];\n        this.mCameras = [];\n        this.nodeToBoneMap = {};\n        this.findNode = function (name, root) {\n          if (!root) {\n            root = this.mRootNode;\n          }\n          if (root.mName == name) {\n            return root;\n          }\n          for (let i = 0; i < root.mChildren.length; i++) {\n            var ret = this.findNode(name, root.mChildren[i]);\n            if (ret) return ret;\n          }\n          return null;\n        };\n        this.toTHREE = function () {\n          this.nodeCount = 0;\n          markBones(this);\n          var o = this.mRootNode.toTHREE(this);\n          for (let i in this.mMeshes) this.mMeshes[i].hookupSkeletons(this);\n          if (this.mAnimations.length > 0) {\n            var a = this.mAnimations[0].toTHREE(this);\n          }\n          return {\n            object: o,\n            animation: a\n          };\n        };\n      }\n    }\n    class aiMatrix4 {\n      constructor() {\n        this.elements = [[], [], [], []];\n        this.toTHREE = function () {\n          var m = new Matrix4();\n          for (let i = 0; i < 4; ++i) {\n            for (let i2 = 0; i2 < 4; ++i2) {\n              m.elements[i * 4 + i2] = this.elements[i2][i];\n            }\n          }\n          return m;\n        };\n      }\n    }\n    var littleEndian = true;\n    function readFloat(dataview) {\n      var val = dataview.getFloat32(dataview.readOffset, littleEndian);\n      dataview.readOffset += 4;\n      return val;\n    }\n    function Read_double(dataview) {\n      var val = dataview.getFloat64(dataview.readOffset, littleEndian);\n      dataview.readOffset += 8;\n      return val;\n    }\n    function Read_uint8_t(dataview) {\n      var val = dataview.getUint8(dataview.readOffset);\n      dataview.readOffset += 1;\n      return val;\n    }\n    function Read_uint16_t(dataview) {\n      var val = dataview.getUint16(dataview.readOffset, littleEndian);\n      dataview.readOffset += 2;\n      return val;\n    }\n    function Read_unsigned_int(dataview) {\n      var val = dataview.getUint32(dataview.readOffset, littleEndian);\n      dataview.readOffset += 4;\n      return val;\n    }\n    function Read_uint32_t(dataview) {\n      var val = dataview.getUint32(dataview.readOffset, littleEndian);\n      dataview.readOffset += 4;\n      return val;\n    }\n    function Read_aiVector3D(stream) {\n      var v = new aiVector3D();\n      v.x = readFloat(stream);\n      v.y = readFloat(stream);\n      v.z = readFloat(stream);\n      return v;\n    }\n    function Read_aiColor3D(stream) {\n      var c = new aiColor3D();\n      c.r = readFloat(stream);\n      c.g = readFloat(stream);\n      c.b = readFloat(stream);\n      return c;\n    }\n    function Read_aiQuaternion(stream) {\n      var v = new aiQuaternion();\n      v.w = readFloat(stream);\n      v.x = readFloat(stream);\n      v.y = readFloat(stream);\n      v.z = readFloat(stream);\n      return v;\n    }\n    function Read_aiString(stream) {\n      var s = new aiString();\n      var stringlengthbytes = Read_unsigned_int(stream);\n      stream.ReadBytes(s.data, 1, stringlengthbytes);\n      return s.toString();\n    }\n    function Read_aiVertexWeight(stream) {\n      var w = new aiVertexWeight();\n      w.mVertexId = Read_unsigned_int(stream);\n      w.mWeight = readFloat(stream);\n      return w;\n    }\n    function Read_aiMatrix4x4(stream) {\n      var m = new aiMatrix4();\n      for (let i = 0; i < 4; ++i) {\n        for (let i2 = 0; i2 < 4; ++i2) {\n          m.elements[i][i2] = readFloat(stream);\n        }\n      }\n      return m;\n    }\n    function Read_aiVectorKey(stream) {\n      var v = new aiVectorKey();\n      v.mTime = Read_double(stream);\n      v.mValue = Read_aiVector3D(stream);\n      return v;\n    }\n    function Read_aiQuatKey(stream) {\n      var v = new aiQuatKey();\n      v.mTime = Read_double(stream);\n      v.mValue = Read_aiQuaternion(stream);\n      return v;\n    }\n    function ReadArray_aiVertexWeight(stream, data, size) {\n      for (let i = 0; i < size; i++) data[i] = Read_aiVertexWeight(stream);\n    }\n    function ReadArray_aiVectorKey(stream, data, size) {\n      for (let i = 0; i < size; i++) data[i] = Read_aiVectorKey(stream);\n    }\n    function ReadArray_aiQuatKey(stream, data, size) {\n      for (let i = 0; i < size; i++) data[i] = Read_aiQuatKey(stream);\n    }\n    function ReadBounds(stream, T, n) {\n      return stream.Seek(sizeof(T) * n, aiOrigin_CUR);\n    }\n    function ai_assert(bool) {\n      if (!bool) throw \"asset failed\";\n    }\n    function ReadBinaryNode(stream, parent, depth) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AINODE);\n      Read_uint32_t(stream);\n      var node = new aiNode();\n      node.mParent = parent;\n      node.mDepth = depth;\n      node.mName = Read_aiString(stream);\n      node.mTransformation = Read_aiMatrix4x4(stream);\n      node.mNumChildren = Read_unsigned_int(stream);\n      node.mNumMeshes = Read_unsigned_int(stream);\n      if (node.mNumMeshes) {\n        node.mMeshes = [];\n        for (let i = 0; i < node.mNumMeshes; ++i) {\n          node.mMeshes[i] = Read_unsigned_int(stream);\n        }\n      }\n      if (node.mNumChildren) {\n        node.mChildren = [];\n        for (let i = 0; i < node.mNumChildren; ++i) {\n          var node2 = ReadBinaryNode(stream, node, depth++);\n          node.mChildren[i] = node2;\n        }\n      }\n      return node;\n    }\n    function ReadBinaryBone(stream, b) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AIBONE);\n      Read_uint32_t(stream);\n      b.mName = Read_aiString(stream);\n      b.mNumWeights = Read_unsigned_int(stream);\n      b.mOffsetMatrix = Read_aiMatrix4x4(stream);\n      if (shortened) {\n        ReadBounds(stream, b.mWeights, b.mNumWeights);\n      } else {\n        b.mWeights = [];\n        ReadArray_aiVertexWeight(stream, b.mWeights, b.mNumWeights);\n      }\n      return b;\n    }\n    function ReadBinaryMesh(stream, mesh) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AIMESH);\n      Read_uint32_t(stream);\n      mesh.mPrimitiveTypes = Read_unsigned_int(stream);\n      mesh.mNumVertices = Read_unsigned_int(stream);\n      mesh.mNumFaces = Read_unsigned_int(stream);\n      mesh.mNumBones = Read_unsigned_int(stream);\n      mesh.mMaterialIndex = Read_unsigned_int(stream);\n      mesh.mNumUVComponents = [];\n      var c = Read_unsigned_int(stream);\n      if (c & ASSBIN_MESH_HAS_POSITIONS) {\n        if (shortened) {\n          ReadBounds(stream, mesh.mVertices, mesh.mNumVertices);\n        } else {\n          mesh.mVertices = [];\n          mesh.mVertexBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4);\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR);\n        }\n      }\n      if (c & ASSBIN_MESH_HAS_NORMALS) {\n        if (shortened) {\n          ReadBounds(stream, mesh.mNormals, mesh.mNumVertices);\n        } else {\n          mesh.mNormals = [];\n          mesh.mNormalBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4);\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR);\n        }\n      }\n      if (c & ASSBIN_MESH_HAS_TANGENTS_AND_BITANGENTS) {\n        if (shortened) {\n          ReadBounds(stream, mesh.mTangents, mesh.mNumVertices);\n          ReadBounds(stream, mesh.mBitangents, mesh.mNumVertices);\n        } else {\n          mesh.mTangents = [];\n          mesh.mTangentBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4);\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR);\n          mesh.mBitangents = [];\n          mesh.mBitangentBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4);\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR);\n        }\n      }\n      for (let n = 0; n < AI_MAX_NUMBER_OF_COLOR_SETS; ++n) {\n        if (!(c & ASSBIN_MESH_HAS_COLOR(n))) break;\n        if (shortened) {\n          ReadBounds(stream, mesh.mColors[n], mesh.mNumVertices);\n        } else {\n          mesh.mColors[n] = [];\n          mesh.mColorBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 4 * 4);\n          stream.Seek(mesh.mNumVertices * 4 * 4, aiOrigin_CUR);\n        }\n      }\n      mesh.mTexCoordsBuffers = [];\n      for (let n = 0; n < AI_MAX_NUMBER_OF_TEXTURECOORDS; ++n) {\n        if (!(c & ASSBIN_MESH_HAS_TEXCOORD(n))) break;\n        mesh.mNumUVComponents[n] = Read_unsigned_int(stream);\n        if (shortened) {\n          ReadBounds(stream, mesh.mTextureCoords[n], mesh.mNumVertices);\n        } else {\n          mesh.mTextureCoords[n] = [];\n          mesh.mTexCoordsBuffers[n] = [];\n          for (let uv = 0; uv < mesh.mNumVertices; uv++) {\n            mesh.mTexCoordsBuffers[n].push(readFloat(stream));\n            mesh.mTexCoordsBuffers[n].push(readFloat(stream));\n            readFloat(stream);\n          }\n        }\n      }\n      if (shortened) {\n        Read_unsigned_int(stream);\n      } else {\n        mesh.mFaces = [];\n        mesh.mIndexArray = [];\n        for (let i = 0; i < mesh.mNumFaces; ++i) {\n          var f = mesh.mFaces[i] = new aiFace();\n          f.mNumIndices = Read_uint16_t(stream);\n          f.mIndices = [];\n          for (let a = 0; a < f.mNumIndices; ++a) {\n            if (mesh.mNumVertices < 1 << 16) {\n              f.mIndices[a] = Read_uint16_t(stream);\n            } else {\n              f.mIndices[a] = Read_unsigned_int(stream);\n            }\n          }\n          if (f.mNumIndices === 3) {\n            mesh.mIndexArray.push(f.mIndices[0]);\n            mesh.mIndexArray.push(f.mIndices[1]);\n            mesh.mIndexArray.push(f.mIndices[2]);\n          } else if (f.mNumIndices === 4) {\n            mesh.mIndexArray.push(f.mIndices[0]);\n            mesh.mIndexArray.push(f.mIndices[1]);\n            mesh.mIndexArray.push(f.mIndices[2]);\n            mesh.mIndexArray.push(f.mIndices[2]);\n            mesh.mIndexArray.push(f.mIndices[3]);\n            mesh.mIndexArray.push(f.mIndices[0]);\n          } else {\n            throw new Error(\"Sorry, can't currently triangulate polys. Use the triangulate preprocessor in Assimp.\");\n          }\n        }\n      }\n      if (mesh.mNumBones) {\n        mesh.mBones = [];\n        for (let a = 0; a < mesh.mNumBones; ++a) {\n          mesh.mBones[a] = new aiBone();\n          ReadBinaryBone(stream, mesh.mBones[a]);\n        }\n      }\n    }\n    function ReadBinaryMaterialProperty(stream, prop) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AIMATERIALPROPERTY);\n      Read_uint32_t(stream);\n      prop.mKey = Read_aiString(stream);\n      prop.mSemantic = Read_unsigned_int(stream);\n      prop.mIndex = Read_unsigned_int(stream);\n      prop.mDataLength = Read_unsigned_int(stream);\n      prop.mType = Read_unsigned_int(stream);\n      prop.mData = [];\n      stream.ReadBytes(prop.mData, 1, prop.mDataLength);\n    }\n    function ReadBinaryMaterial(stream, mat) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AIMATERIAL);\n      Read_uint32_t(stream);\n      mat.mNumAllocated = mat.mNumProperties = Read_unsigned_int(stream);\n      if (mat.mNumProperties) {\n        if (mat.mProperties) {\n          delete mat.mProperties;\n        }\n        mat.mProperties = [];\n        for (let i = 0; i < mat.mNumProperties; ++i) {\n          mat.mProperties[i] = new aiMaterialProperty();\n          ReadBinaryMaterialProperty(stream, mat.mProperties[i]);\n        }\n      }\n    }\n    function ReadBinaryNodeAnim(stream, nd) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AINODEANIM);\n      Read_uint32_t(stream);\n      nd.mNodeName = Read_aiString(stream);\n      nd.mNumPositionKeys = Read_unsigned_int(stream);\n      nd.mNumRotationKeys = Read_unsigned_int(stream);\n      nd.mNumScalingKeys = Read_unsigned_int(stream);\n      nd.mPreState = Read_unsigned_int(stream);\n      nd.mPostState = Read_unsigned_int(stream);\n      if (nd.mNumPositionKeys) {\n        if (shortened) {\n          ReadBounds(stream, nd.mPositionKeys, nd.mNumPositionKeys);\n        } else {\n          nd.mPositionKeys = [];\n          ReadArray_aiVectorKey(stream, nd.mPositionKeys, nd.mNumPositionKeys);\n        }\n      }\n      if (nd.mNumRotationKeys) {\n        if (shortened) {\n          ReadBounds(stream, nd.mRotationKeys, nd.mNumRotationKeys);\n        } else {\n          nd.mRotationKeys = [];\n          ReadArray_aiQuatKey(stream, nd.mRotationKeys, nd.mNumRotationKeys);\n        }\n      }\n      if (nd.mNumScalingKeys) {\n        if (shortened) {\n          ReadBounds(stream, nd.mScalingKeys, nd.mNumScalingKeys);\n        } else {\n          nd.mScalingKeys = [];\n          ReadArray_aiVectorKey(stream, nd.mScalingKeys, nd.mNumScalingKeys);\n        }\n      }\n    }\n    function ReadBinaryAnim(stream, anim) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AIANIMATION);\n      Read_uint32_t(stream);\n      anim.mName = Read_aiString(stream);\n      anim.mDuration = Read_double(stream);\n      anim.mTicksPerSecond = Read_double(stream);\n      anim.mNumChannels = Read_unsigned_int(stream);\n      if (anim.mNumChannels) {\n        anim.mChannels = [];\n        for (let a = 0; a < anim.mNumChannels; ++a) {\n          anim.mChannels[a] = new aiNodeAnim();\n          ReadBinaryNodeAnim(stream, anim.mChannels[a]);\n        }\n      }\n    }\n    function ReadBinaryTexture(stream, tex) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AITEXTURE);\n      Read_uint32_t(stream);\n      tex.mWidth = Read_unsigned_int(stream);\n      tex.mHeight = Read_unsigned_int(stream);\n      stream.ReadBytes(tex.achFormatHint, 1, 4);\n      if (!shortened) {\n        if (!tex.mHeight) {\n          tex.pcData = [];\n          stream.ReadBytes(tex.pcData, 1, tex.mWidth);\n        } else {\n          tex.pcData = [];\n          stream.ReadBytes(tex.pcData, 1, tex.mWidth * tex.mHeight * 4);\n        }\n      }\n    }\n    function ReadBinaryLight(stream, l) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AILIGHT);\n      Read_uint32_t(stream);\n      l.mName = Read_aiString(stream);\n      l.mType = Read_unsigned_int(stream);\n      if (l.mType != aiLightSource_DIRECTIONAL) {\n        l.mAttenuationConstant = readFloat(stream);\n        l.mAttenuationLinear = readFloat(stream);\n        l.mAttenuationQuadratic = readFloat(stream);\n      }\n      l.mColorDiffuse = Read_aiColor3D(stream);\n      l.mColorSpecular = Read_aiColor3D(stream);\n      l.mColorAmbient = Read_aiColor3D(stream);\n      if (l.mType == aiLightSource_SPOT) {\n        l.mAngleInnerCone = readFloat(stream);\n        l.mAngleOuterCone = readFloat(stream);\n      }\n    }\n    function ReadBinaryCamera(stream, cam) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AICAMERA);\n      Read_uint32_t(stream);\n      cam.mName = Read_aiString(stream);\n      cam.mPosition = Read_aiVector3D(stream);\n      cam.mLookAt = Read_aiVector3D(stream);\n      cam.mUp = Read_aiVector3D(stream);\n      cam.mHorizontalFOV = readFloat(stream);\n      cam.mClipPlaneNear = readFloat(stream);\n      cam.mClipPlaneFar = readFloat(stream);\n      cam.mAspect = readFloat(stream);\n    }\n    function ReadBinaryScene(stream, scene) {\n      var chunkID = Read_uint32_t(stream);\n      ai_assert(chunkID == ASSBIN_CHUNK_AISCENE);\n      Read_uint32_t(stream);\n      scene.mFlags = Read_unsigned_int(stream);\n      scene.mNumMeshes = Read_unsigned_int(stream);\n      scene.mNumMaterials = Read_unsigned_int(stream);\n      scene.mNumAnimations = Read_unsigned_int(stream);\n      scene.mNumTextures = Read_unsigned_int(stream);\n      scene.mNumLights = Read_unsigned_int(stream);\n      scene.mNumCameras = Read_unsigned_int(stream);\n      scene.mRootNode = new aiNode();\n      scene.mRootNode = ReadBinaryNode(stream, null, 0);\n      if (scene.mNumMeshes) {\n        scene.mMeshes = [];\n        for (let i = 0; i < scene.mNumMeshes; ++i) {\n          scene.mMeshes[i] = new aiMesh();\n          ReadBinaryMesh(stream, scene.mMeshes[i]);\n        }\n      }\n      if (scene.mNumMaterials) {\n        scene.mMaterials = [];\n        for (let i = 0; i < scene.mNumMaterials; ++i) {\n          scene.mMaterials[i] = new aiMaterial();\n          ReadBinaryMaterial(stream, scene.mMaterials[i]);\n        }\n      }\n      if (scene.mNumAnimations) {\n        scene.mAnimations = [];\n        for (let i = 0; i < scene.mNumAnimations; ++i) {\n          scene.mAnimations[i] = new aiAnimation();\n          ReadBinaryAnim(stream, scene.mAnimations[i]);\n        }\n      }\n      if (scene.mNumTextures) {\n        scene.mTextures = [];\n        for (let i = 0; i < scene.mNumTextures; ++i) {\n          scene.mTextures[i] = new aiTexture();\n          ReadBinaryTexture(stream, scene.mTextures[i]);\n        }\n      }\n      if (scene.mNumLights) {\n        scene.mLights = [];\n        for (let i = 0; i < scene.mNumLights; ++i) {\n          scene.mLights[i] = new aiLight();\n          ReadBinaryLight(stream, scene.mLights[i]);\n        }\n      }\n      if (scene.mNumCameras) {\n        scene.mCameras = [];\n        for (let i = 0; i < scene.mNumCameras; ++i) {\n          scene.mCameras[i] = new aiCamera();\n          ReadBinaryCamera(stream, scene.mCameras[i]);\n        }\n      }\n    }\n    var aiOrigin_CUR = 0;\n    var aiOrigin_BEG = 1;\n    function extendStream(stream) {\n      stream.readOffset = 0;\n      stream.Seek = function (off, ori) {\n        if (ori == aiOrigin_CUR) {\n          stream.readOffset += off;\n        }\n        if (ori == aiOrigin_BEG) {\n          stream.readOffset = off;\n        }\n      };\n      stream.ReadBytes = function (buff, size, n) {\n        var bytes = size * n;\n        for (let i = 0; i < bytes; i++) buff[i] = Read_uint8_t(this);\n      };\n      stream.subArray32 = function (start, end) {\n        var buff = this.buffer;\n        var newbuff = buff.slice(start, end);\n        return new Float32Array(newbuff);\n      };\n      stream.subArrayUint16 = function (start, end) {\n        var buff = this.buffer;\n        var newbuff = buff.slice(start, end);\n        return new Uint16Array(newbuff);\n      };\n      stream.subArrayUint8 = function (start, end) {\n        var buff = this.buffer;\n        var newbuff = buff.slice(start, end);\n        return new Uint8Array(newbuff);\n      };\n      stream.subArrayUint32 = function (start, end) {\n        var buff = this.buffer;\n        var newbuff = buff.slice(start, end);\n        return new Uint32Array(newbuff);\n      };\n    }\n    var shortened, compressed;\n    function InternReadFile(pFiledata) {\n      var pScene = new aiScene();\n      var stream = new DataView(pFiledata);\n      extendStream(stream);\n      stream.Seek(44, aiOrigin_CUR);\n      pScene.versionMajor = Read_unsigned_int(stream);\n      pScene.versionMinor = Read_unsigned_int(stream);\n      pScene.versionRevision = Read_unsigned_int(stream);\n      pScene.compileFlags = Read_unsigned_int(stream);\n      shortened = Read_uint16_t(stream) > 0;\n      compressed = Read_uint16_t(stream) > 0;\n      if (shortened) throw \"Shortened binaries are not supported!\";\n      stream.Seek(256, aiOrigin_CUR);\n      stream.Seek(128, aiOrigin_CUR);\n      stream.Seek(64, aiOrigin_CUR);\n      if (compressed) {\n        var uncompressedSize = Read_uint32_t(stream);\n        var compressedSize = stream.FileSize() - stream.Tell();\n        var compressedData = [];\n        stream.Read(compressedData, 1, compressedSize);\n        var uncompressedData = [];\n        uncompress(uncompressedData, uncompressedSize, compressedData, compressedSize);\n        var buff = new ArrayBuffer(uncompressedData);\n        ReadBinaryScene(buff, pScene);\n      } else {\n        ReadBinaryScene(stream, pScene);\n      }\n      return pScene.toTHREE();\n    }\n    return InternReadFile(buffer);\n  }\n}\nexport { AssimpLoader };", "map": {"version": 3, "names": ["<PERSON><PERSON>mp<PERSON><PERSON><PERSON>", "Loader", "load", "url", "onLoad", "onProgress", "onError", "scope", "path", "LoaderUtils", "extractUrlBase", "loader", "<PERSON><PERSON><PERSON><PERSON>", "manager", "set<PERSON>ath", "setResponseType", "setRequestHeader", "requestHeader", "setWithCredentials", "withCredentials", "buffer", "parse", "e", "console", "error", "itemError", "textureLoader", "TextureLoader", "resourcePath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "Virtulous", "KeyFrame", "constructor", "time", "matrix", "clone", "position", "Vector3", "quaternion", "Quaternion", "scale", "decompose", "n", "lerp", "<PERSON><PERSON><PERSON>", "time2", "dist", "l", "l2", "keypos", "keyrot", "key2pos", "key2rot", "tempAniPos", "x", "y", "z", "tempAniQuat", "set", "w", "slerp", "tempAniMatrix", "compose", "tempAniScale", "Matrix4", "KeyFrameTrack", "keys", "target", "length", "_accelTable", "fps", "<PERSON><PERSON><PERSON>", "key", "push", "init", "sortKeys", "j", "i", "parseFrom<PERSON><PERSON><PERSON>", "data", "node", "track", "hierarchy", "targets", "parseFromCollada", "sort", "keySortFunc", "a", "b", "t", "re<PERSON>arget", "root", "compareitor", "TrackTargetNodeNameCompare", "keySearchAccel", "Math", "floor", "setTime", "abs", "key0", "key1", "matrixAutoUpdate", "copy", "matrixWorldNeedsUpdate", "find", "name", "children", "r", "Animation", "tracks", "addTrack", "max", "ASSBIN_CHUNK_AICAMERA", "ASSBIN_CHUNK_AILIGHT", "ASSBIN_CHUNK_AITEXTURE", "ASSBIN_CHUNK_AIMESH", "ASSBIN_CHUNK_AINODEANIM", "ASSBIN_CHUNK_AISCENE", "ASSBIN_CHUNK_AIBONE", "ASSBIN_CHUNK_AIANIMATION", "ASSBIN_CHUNK_AINODE", "ASSBIN_CHUNK_AIMATERIAL", "ASSBIN_CHUNK_AIMATERIALPROPERTY", "ASSBIN_MESH_HAS_POSITIONS", "ASSBIN_MESH_HAS_NORMALS", "ASSBIN_MESH_HAS_TANGENTS_AND_BITANGENTS", "ASSBIN_MESH_HAS_TEXCOORD_BASE", "ASSBIN_MESH_HAS_COLOR_BASE", "AI_MAX_NUMBER_OF_COLOR_SETS", "AI_MAX_NUMBER_OF_TEXTURECOORDS", "aiLightSource_DIRECTIONAL", "aiLightSource_SPOT", "aiTextureType_DIFFUSE", "aiTextureType_NORMALS", "aiTextureType_OPACITY", "aiTextureType_LIGHTMAP", "BONESPERVERT", "ASSBIN_MESH_HAS_TEXCOORD", "ASSBIN_MESH_HAS_COLOR", "markBones", "scene", "m<PERSON><PERSON><PERSON>", "mesh", "k", "mBones", "boneNode", "findNode", "mName", "isBone", "cloneTreeToBones", "rootBone", "Bone", "matrixWorld", "nodeCount", "toString", "nodeToBoneMap", "child", "add", "sortWeights", "indexes", "weights", "pairs", "sum", "sqrt", "findMatchingBone", "indexOf", "ret", "<PERSON><PERSON><PERSON><PERSON>", "mPrimitiveTypes", "mNumVertices", "mNumFaces", "mNumBones", "mMaterialIndex", "mVertices", "mNormals", "mTangents", "mBitangents", "mColors", "mTextureCoords", "mFaces", "hookupSkeletons", "allBones", "offsetMatrix", "skeletonRoot", "mParent", "threeSkeletonRoot", "toTHREE", "threeSkeletonRootBone", "threeNode", "bone", "tbone", "mOffsetMatrix", "skeleton", "Skeleton", "bind", "material", "skinning", "geometry", "BufferGeometry", "mat", "mMaterials", "MeshLambertMaterial", "setIndex", "BufferAttribute", "Uint32Array", "mIndexArray", "setAttribute", "mVertexBuffer", "mN<PERSON><PERSON><PERSON><PERSON><PERSON>", "mColorBuffer", "mTexCoordsBuffers", "Float32Array", "mTangentBuffer", "mBitangentBuffer", "bones", "mWeights", "weight", "mVertexId", "mWeight", "parseInt", "_weights", "_bones", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "normalizeSkinWeights", "aiFace", "mNumIndices", "mIndices", "aiVector3D", "aiColor3D", "g", "Color", "aiQuaternion", "aiVertexWeight", "aiString", "str", "for<PERSON>ach", "String", "fromCharCode", "replace", "aiVectorKey", "mTime", "mValue", "aiQuatKey", "aiNode", "mTransformation", "mNumChildren", "mNum<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "o", "Object3D", "aiBone", "mNumWeights", "aiMaterialProperty", "m<PERSON>ey", "mSeman<PERSON>", "mIndex", "mData", "mData<PERSON>ength", "mType", "dataAsColor", "array", "Uint8Array", "reader", "DataView", "getFloat32", "dataAsFloat", "dataAsBool", "dataAsString", "s", "dataAsMap", "path2", "substr", "lastIndexOf", "namePropMapping", "nameTypeMapping", "aiMaterial", "mNumAllocated", "mNumProperties", "mProperties", "MeshPhongMaterial", "prop", "map", "normalMap", "lightMap", "alphaMap", "ambient", "color", "veclerp", "v1", "v2", "v", "lm1", "quatlerp", "q1", "q2", "sampleTrack", "lne", "Infinity", "timeDist", "dT", "T", "aiNodeAnim", "mNodeName", "mNumPositionKeys", "mNumRotationKeys", "mNumScalingKeys", "mPositionKeys", "mRotationKeys", "mScalingKeys", "mPreState", "mPostState", "tps", "t2", "comp", "<PERSON><PERSON><PERSON><PERSON>", "apply", "pos", "rotation", "aiAnimation", "mDuration", "mTicksPerSecond", "mNumChannels", "mChannels", "animationHandle", "aiTexture", "m<PERSON><PERSON><PERSON>", "mHeight", "texAchFormatHint", "pcData", "aiLight", "mAttenuationConstant", "mAttenuationLinear", "mAttenuationQuadratic", "mAngleInnerCone", "mAngleOuterCone", "mColorDiffuse", "mColorSpecular", "mColorAmbient", "aiCamera", "mPosition", "mLookAt", "mUp", "mHorizontalFOV", "mClipPlaneNear", "mClipPlaneFar", "mAspect", "aiScene", "versionMajor", "versionMinor", "versionRevision", "compileFlags", "m<PERSON>lags", "mNumMaterials", "mNumAnimations", "mNumTextures", "mNumLights", "mNumCameras", "mRootNode", "mAnimations", "mLights", "mCameras", "object", "animation", "aiMatrix4", "elements", "m", "i2", "littleEndian", "readFloat", "dataview", "val", "readOffset", "Read_double", "getFloat64", "Read_uint8_t", "getUint8", "Read_uint16_t", "getUint16", "Read_unsigned_int", "getUint32", "Read_uint32_t", "Read_aiVector3D", "stream", "Read_aiColor3D", "c", "Read_aiQuaternion", "Read_aiString", "stringlengthbytes", "ReadBytes", "Read_aiVertexWeight", "Read_aiMatrix4x4", "Read_aiVectorKey", "Read_aiQuatKey", "ReadArray_aiVertexWeight", "size", "ReadArray_aiVectorKey", "ReadArray_aiQuatKey", "ReadBounds", "Seek", "sizeof", "aiOrigin_CUR", "ai_assert", "bool", "ReadBinaryNode", "parent", "depth", "chunkID", "m<PERSON><PERSON>h", "node2", "ReadBinaryBone", "shortened", "Read<PERSON><PERSON><PERSON><PERSON><PERSON>", "mNumUVComponents", "subArray32", "uv", "f", "Error", "ReadBinaryMaterialProperty", "ReadBinaryMaterial", "ReadBinaryNodeAnim", "nd", "ReadBinaryAnim", "anim", "ReadBinaryTexture", "tex", "achFormatHint", "ReadBinaryLight", "ReadBinaryCamera", "cam", "ReadBinaryScene", "mTextures", "aiOrigin_BEG", "extendStream", "off", "ori", "buff", "bytes", "start", "end", "new<PERSON><PERSON>", "slice", "subArrayUint16", "Uint16Array", "subArrayUint8", "subArrayUint32", "compressed", "InternReadFile", "pFiledata", "pScene", "uncompressedSize", "compressedSize", "FileSize", "Tell", "compressedData", "Read", "uncompressedData", "uncompress", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\loaders\\AssimpLoader.js"], "sourcesContent": ["import {\n  Bone,\n  BufferAttribute,\n  BufferGeometry,\n  Color,\n  FileLoader,\n  Loader,\n  LoaderUtils,\n  Matrix4,\n  Mesh,\n  MeshLambertMaterial,\n  MeshPhongMaterial,\n  Object3D,\n  Quaternion,\n  Skeleton,\n  SkinnedMesh,\n  TextureLoader,\n  Vector3,\n} from 'three'\n\nclass AssimpLoader extends Loader {\n  load(url, onLoad, onProgress, onError) {\n    var scope = this\n\n    var path = scope.path === '' ? LoaderUtils.extractUrlBase(url) : scope.path\n\n    var loader = new FileLoader(scope.manager)\n    loader.setPath(scope.path)\n    loader.setResponseType('arraybuffer')\n    loader.setRequestHeader(scope.requestHeader)\n    loader.setWithCredentials(scope.withCredentials)\n\n    loader.load(\n      url,\n      function (buffer) {\n        try {\n          onLoad(scope.parse(buffer, path))\n        } catch (e) {\n          if (onError) {\n            onError(e)\n          } else {\n            console.error(e)\n          }\n\n          scope.manager.itemError(url)\n        }\n      },\n      onProgress,\n      onError,\n    )\n  }\n\n  parse(buffer, path) {\n    var textureLoader = new TextureLoader(this.manager)\n    textureLoader.setPath(this.resourcePath || path).setCrossOrigin(this.crossOrigin)\n\n    var Virtulous = {}\n\n    Virtulous.KeyFrame = class {\n      constructor(time, matrix) {\n        this.time = time\n        this.matrix = matrix.clone()\n        this.position = new Vector3()\n        this.quaternion = new Quaternion()\n        this.scale = new Vector3(1, 1, 1)\n        this.matrix.decompose(this.position, this.quaternion, this.scale)\n        this.clone = function () {\n          var n = new Virtulous.KeyFrame(this.time, this.matrix)\n          return n\n        }\n\n        this.lerp = function (nextKey, time) {\n          time -= this.time\n          var dist = nextKey.time - this.time\n          var l = time / dist\n          var l2 = 1 - l\n          var keypos = this.position\n          var keyrot = this.quaternion\n          //      var keyscl =  key.parentspaceScl || key.scl;\n          var key2pos = nextKey.position\n          var key2rot = nextKey.quaternion\n          //  var key2scl =  key2.parentspaceScl || key2.scl;\n          Virtulous.KeyFrame.tempAniPos.x = keypos.x * l2 + key2pos.x * l\n          Virtulous.KeyFrame.tempAniPos.y = keypos.y * l2 + key2pos.y * l\n          Virtulous.KeyFrame.tempAniPos.z = keypos.z * l2 + key2pos.z * l\n          //     tempAniScale.x = keyscl[0] * l2 + key2scl[0] * l;\n          //     tempAniScale.y = keyscl[1] * l2 + key2scl[1] * l;\n          //     tempAniScale.z = keyscl[2] * l2 + key2scl[2] * l;\n          Virtulous.KeyFrame.tempAniQuat.set(keyrot.x, keyrot.y, keyrot.z, keyrot.w)\n          Virtulous.KeyFrame.tempAniQuat.slerp(key2rot, l)\n          return Virtulous.KeyFrame.tempAniMatrix.compose(\n            Virtulous.KeyFrame.tempAniPos,\n            Virtulous.KeyFrame.tempAniQuat,\n            Virtulous.KeyFrame.tempAniScale,\n          )\n        }\n      }\n    }\n\n    Virtulous.KeyFrame.tempAniPos = new Vector3()\n    Virtulous.KeyFrame.tempAniQuat = new Quaternion()\n    Virtulous.KeyFrame.tempAniScale = new Vector3(1, 1, 1)\n    Virtulous.KeyFrame.tempAniMatrix = new Matrix4()\n    Virtulous.KeyFrameTrack = function () {\n      this.keys = []\n      this.target = null\n      this.time = 0\n      this.length = 0\n      this._accelTable = {}\n      this.fps = 20\n      this.addKey = function (key) {\n        this.keys.push(key)\n      }\n\n      this.init = function () {\n        this.sortKeys()\n\n        if (this.keys.length > 0) this.length = this.keys[this.keys.length - 1].time\n        else this.length = 0\n\n        if (!this.fps) return\n\n        for (let j = 0; j < this.length * this.fps; j++) {\n          for (let i = 0; i < this.keys.length; i++) {\n            if (this.keys[i].time == j) {\n              this._accelTable[j] = i\n              break\n            } else if (this.keys[i].time < j / this.fps && this.keys[i + 1] && this.keys[i + 1].time >= j / this.fps) {\n              this._accelTable[j] = i\n              break\n            }\n          }\n        }\n      }\n\n      this.parseFromThree = function (data) {\n        var fps = data.fps\n        this.target = data.node\n        var track = data.hierarchy[0].keys\n        for (let i = 0; i < track.length; i++) {\n          this.addKey(new Virtulous.KeyFrame(i / fps || track[i].time, track[i].targets[0].data))\n        }\n\n        this.init()\n      }\n\n      this.parseFromCollada = function (data) {\n        var track = data.keys\n        var fps = this.fps\n\n        for (let i = 0; i < track.length; i++) {\n          this.addKey(new Virtulous.KeyFrame(i / fps || track[i].time, track[i].matrix))\n        }\n\n        this.init()\n      }\n\n      this.sortKeys = function () {\n        this.keys.sort(this.keySortFunc)\n      }\n\n      this.keySortFunc = function (a, b) {\n        return a.time - b.time\n      }\n\n      this.clone = function () {\n        var t = new Virtulous.KeyFrameTrack()\n        t.target = this.target\n        t.time = this.time\n        t.length = this.length\n\n        for (let i = 0; i < this.keys.length; i++) {\n          t.addKey(this.keys[i].clone())\n        }\n\n        t.init()\n        return t\n      }\n\n      this.reTarget = function (root, compareitor) {\n        if (!compareitor) compareitor = Virtulous.TrackTargetNodeNameCompare\n        this.target = compareitor(root, this.target)\n      }\n\n      this.keySearchAccel = function (time) {\n        time *= this.fps\n        time = Math.floor(time)\n        return this._accelTable[time] || 0\n      }\n\n      this.setTime = function (time) {\n        time = Math.abs(time)\n        if (this.length) time = (time % this.length) + 0.05\n        var key0 = null\n        var key1 = null\n\n        for (let i = this.keySearchAccel(time); i < this.keys.length; i++) {\n          if (this.keys[i].time == time) {\n            key0 = this.keys[i]\n            key1 = this.keys[i]\n            break\n          } else if (this.keys[i].time < time && this.keys[i + 1] && this.keys[i + 1].time > time) {\n            key0 = this.keys[i]\n            key1 = this.keys[i + 1]\n            break\n          } else if (this.keys[i].time < time && i == this.keys.length - 1) {\n            key0 = this.keys[i]\n            key1 = this.keys[0].clone()\n            key1.time += this.length + 0.05\n            break\n          }\n        }\n\n        if (key0 && key1 && key0 !== key1) {\n          this.target.matrixAutoUpdate = false\n          this.target.matrix.copy(key0.lerp(key1, time))\n          this.target.matrixWorldNeedsUpdate = true\n          return\n        }\n\n        if (key0 && key1 && key0 == key1) {\n          this.target.matrixAutoUpdate = false\n          this.target.matrix.copy(key0.matrix)\n          this.target.matrixWorldNeedsUpdate = true\n          return\n        }\n      }\n    }\n\n    Virtulous.TrackTargetNodeNameCompare = function (root, target) {\n      function find(node, name) {\n        if (node.name == name) return node\n\n        for (let i = 0; i < node.children.length; i++) {\n          var r = find(node.children[i], name)\n          if (r) return r\n        }\n\n        return null\n      }\n\n      return find(root, target.name)\n    }\n\n    Virtulous.Animation = function () {\n      this.tracks = []\n      this.length = 0\n\n      this.addTrack = function (track) {\n        this.tracks.push(track)\n        this.length = Math.max(track.length, this.length)\n      }\n\n      this.setTime = function (time) {\n        this.time = time\n\n        for (let i = 0; i < this.tracks.length; i++) this.tracks[i].setTime(time)\n      }\n\n      this.clone = function (target, compareitor) {\n        if (!compareitor) compareitor = Virtulous.TrackTargetNodeNameCompare\n        var n = new Virtulous.Animation()\n        n.target = target\n        for (let i = 0; i < this.tracks.length; i++) {\n          var track = this.tracks[i].clone()\n          track.reTarget(target, compareitor)\n          n.addTrack(track)\n        }\n\n        return n\n      }\n    }\n\n    var ASSBIN_CHUNK_AICAMERA = 0x1234\n    var ASSBIN_CHUNK_AILIGHT = 0x1235\n    var ASSBIN_CHUNK_AITEXTURE = 0x1236\n    var ASSBIN_CHUNK_AIMESH = 0x1237\n    var ASSBIN_CHUNK_AINODEANIM = 0x1238\n    var ASSBIN_CHUNK_AISCENE = 0x1239\n    var ASSBIN_CHUNK_AIBONE = 0x123a\n    var ASSBIN_CHUNK_AIANIMATION = 0x123b\n    var ASSBIN_CHUNK_AINODE = 0x123c\n    var ASSBIN_CHUNK_AIMATERIAL = 0x123d\n    var ASSBIN_CHUNK_AIMATERIALPROPERTY = 0x123e\n    var ASSBIN_MESH_HAS_POSITIONS = 0x1\n    var ASSBIN_MESH_HAS_NORMALS = 0x2\n    var ASSBIN_MESH_HAS_TANGENTS_AND_BITANGENTS = 0x4\n    var ASSBIN_MESH_HAS_TEXCOORD_BASE = 0x100\n    var ASSBIN_MESH_HAS_COLOR_BASE = 0x10000\n    var AI_MAX_NUMBER_OF_COLOR_SETS = 1\n    var AI_MAX_NUMBER_OF_TEXTURECOORDS = 4\n    //var aiLightSource_UNDEFINED = 0x0;\n    //! A directional light source has a well-defined direction\n    //! but is infinitely far away. That's quite a good\n    //! approximation for sun light.\n    var aiLightSource_DIRECTIONAL = 0x1\n    //! A point light source has a well-defined position\n    //! in space but no direction - it emits light in all\n    //! directions. A normal bulb is a point light.\n    //var aiLightSource_POINT = 0x2;\n    //! A spot light source emits light in a specific\n    //! angle. It has a position and a direction it is pointing to.\n    //! A good example for a spot light is a light spot in\n    //! sport arenas.\n    var aiLightSource_SPOT = 0x3\n    //! The generic light level of the world, including the bounces\n    //! of all other lightsources.\n    //! Typically, there's at most one ambient light in a scene.\n    //! This light type doesn't have a valid position, direction, or\n    //! other properties, just a color.\n    //var aiLightSource_AMBIENT = 0x4;\n    /** Flat shading. Shading is done on per-face base,\n     *  diffuse only. Also known as 'faceted shading'.\n     */\n    //var aiShadingMode_Flat = 0x1;\n    /** Simple Gouraud shading.\n     */\n    //var aiShadingMode_Gouraud = 0x2;\n    /** Phong-Shading -\n     */\n    //var aiShadingMode_Phong = 0x3;\n    /** Phong-Blinn-Shading\n     */\n    //var aiShadingMode_Blinn = 0x4;\n    /** Toon-Shading per pixel\n     *\n     *  Also known as 'comic' shader.\n     */\n    //var aiShadingMode_Toon = 0x5;\n    /** OrenNayar-Shading per pixel\n     *\n     *  Extension to standard Lambertian shading, taking the\n     *  roughness of the material into account\n     */\n    //var aiShadingMode_OrenNayar = 0x6;\n    /** Minnaert-Shading per pixel\n     *\n     *  Extension to standard Lambertian shading, taking the\n     *  \"darkness\" of the material into account\n     */\n    //var aiShadingMode_Minnaert = 0x7;\n    /** CookTorrance-Shading per pixel\n     *\n     *  Special shader for metallic surfaces.\n     */\n    //var aiShadingMode_CookTorrance = 0x8;\n    /** No shading at all. Constant light influence of 1.0.\n     */\n    //var aiShadingMode_NoShading = 0x9;\n    /** Fresnel shading\n     */\n    //var aiShadingMode_Fresnel = 0xa;\n    //var aiTextureType_NONE = 0x0;\n    /** The texture is combined with the result of the diffuse\n     *  lighting equation.\n     */\n    var aiTextureType_DIFFUSE = 0x1\n    /** The texture is combined with the result of the specular\n     *  lighting equation.\n     */\n    //var aiTextureType_SPECULAR = 0x2;\n    /** The texture is combined with the result of the ambient\n     *  lighting equation.\n     */\n    //var aiTextureType_AMBIENT = 0x3;\n    /** The texture is added to the result of the lighting\n     *  calculation. It isn't influenced by incoming light.\n     */\n    //var aiTextureType_EMISSIVE = 0x4;\n    /** The texture is a height map.\n     *\n     *  By convention, higher gray-scale values stand for\n     *  higher elevations from the base height.\n     */\n    //var aiTextureType_HEIGHT = 0x5;\n    /** The texture is a (tangent space) normal-map.\n     *\n     *  Again, there are several conventions for tangent-space\n     *  normal maps. Assimp does (intentionally) not\n     *  distinguish here.\n     */\n    var aiTextureType_NORMALS = 0x6\n    /** The texture defines the glossiness of the material.\n     *\n     *  The glossiness is in fact the exponent of the specular\n     *  (phong) lighting equation. Usually there is a conversion\n     *  function defined to map the linear color values in the\n     *  texture to a suitable exponent. Have fun.\n     */\n    //var aiTextureType_SHININESS = 0x7;\n    /** The texture defines per-pixel opacity.\n     *\n     *  Usually 'white' means opaque and 'black' means\n     *  'transparency'. Or quite the opposite. Have fun.\n     */\n    var aiTextureType_OPACITY = 0x8\n    /** Displacement texture\n     *\n     *  The exact purpose and format is application-dependent.\n     *  Higher color values stand for higher vertex displacements.\n     */\n    //var aiTextureType_DISPLACEMENT = 0x9;\n    /** Lightmap texture (aka Ambient Occlusion)\n     *\n     *  Both 'Lightmaps' and dedicated 'ambient occlusion maps' are\n     *  covered by this material property. The texture contains a\n     *  scaling value for the final color value of a pixel. Its\n     *  intensity is not affected by incoming light.\n     */\n    var aiTextureType_LIGHTMAP = 0xa\n    /** Reflection texture\n     *\n     * Contains the color of a perfect mirror reflection.\n     * Rarely used, almost never for real-time applications.\n     */\n    //var aiTextureType_REFLECTION = 0xB;\n    /** Unknown texture\n     *\n     *  A texture reference that does not match any of the definitions\n     *  above is considered to be 'unknown'. It is still imported,\n     *  but is excluded from any further postprocessing.\n     */\n    //var aiTextureType_UNKNOWN = 0xC;\n    var BONESPERVERT = 4\n\n    function ASSBIN_MESH_HAS_TEXCOORD(n) {\n      return ASSBIN_MESH_HAS_TEXCOORD_BASE << n\n    }\n\n    function ASSBIN_MESH_HAS_COLOR(n) {\n      return ASSBIN_MESH_HAS_COLOR_BASE << n\n    }\n\n    function markBones(scene) {\n      for (let i in scene.mMeshes) {\n        var mesh = scene.mMeshes[i]\n        for (let k in mesh.mBones) {\n          var boneNode = scene.findNode(mesh.mBones[k].mName)\n          if (boneNode) boneNode.isBone = true\n        }\n      }\n    }\n\n    function cloneTreeToBones(root, scene) {\n      var rootBone = new Bone()\n      rootBone.matrix.copy(root.matrix)\n      rootBone.matrixWorld.copy(root.matrixWorld)\n      rootBone.position.copy(root.position)\n      rootBone.quaternion.copy(root.quaternion)\n      rootBone.scale.copy(root.scale)\n      scene.nodeCount++\n      rootBone.name = 'bone_' + root.name + scene.nodeCount.toString()\n\n      if (!scene.nodeToBoneMap[root.name]) scene.nodeToBoneMap[root.name] = []\n      scene.nodeToBoneMap[root.name].push(rootBone)\n      for (let i in root.children) {\n        var child = cloneTreeToBones(root.children[i], scene)\n        rootBone.add(child)\n      }\n\n      return rootBone\n    }\n\n    function sortWeights(indexes, weights) {\n      var pairs = []\n\n      for (let i = 0; i < indexes.length; i++) {\n        pairs.push({\n          i: indexes[i],\n          w: weights[i],\n        })\n      }\n\n      pairs.sort(function (a, b) {\n        return b.w - a.w\n      })\n\n      while (pairs.length < 4) {\n        pairs.push({\n          i: 0,\n          w: 0,\n        })\n      }\n\n      if (pairs.length > 4) pairs.length = 4\n      var sum = 0\n\n      for (let i = 0; i < 4; i++) {\n        sum += pairs[i].w * pairs[i].w\n      }\n\n      sum = Math.sqrt(sum)\n\n      for (let i = 0; i < 4; i++) {\n        pairs[i].w = pairs[i].w / sum\n        indexes[i] = pairs[i].i\n        weights[i] = pairs[i].w\n      }\n    }\n\n    function findMatchingBone(root, name) {\n      if (root.name.indexOf('bone_' + name) == 0) return root\n\n      for (let i in root.children) {\n        var ret = findMatchingBone(root.children[i], name)\n\n        if (ret) return ret\n      }\n\n      return undefined\n    }\n\n    class aiMesh {\n      constructor() {\n        this.mPrimitiveTypes = 0\n        this.mNumVertices = 0\n        this.mNumFaces = 0\n        this.mNumBones = 0\n        this.mMaterialIndex = 0\n        this.mVertices = []\n        this.mNormals = []\n        this.mTangents = []\n        this.mBitangents = []\n        this.mColors = [[]]\n        this.mTextureCoords = [[]]\n        this.mFaces = []\n        this.mBones = []\n        this.hookupSkeletons = function (scene) {\n          if (this.mBones.length == 0) return\n\n          var allBones = []\n          var offsetMatrix = []\n          var skeletonRoot = scene.findNode(this.mBones[0].mName)\n\n          while (skeletonRoot.mParent && skeletonRoot.mParent.isBone) {\n            skeletonRoot = skeletonRoot.mParent\n          }\n\n          var threeSkeletonRoot = skeletonRoot.toTHREE(scene)\n          var threeSkeletonRootBone = cloneTreeToBones(threeSkeletonRoot, scene)\n          this.threeNode.add(threeSkeletonRootBone)\n\n          for (let i = 0; i < this.mBones.length; i++) {\n            var bone = findMatchingBone(threeSkeletonRootBone, this.mBones[i].mName)\n\n            if (bone) {\n              var tbone = bone\n              allBones.push(tbone)\n              //tbone.matrixAutoUpdate = false;\n              offsetMatrix.push(this.mBones[i].mOffsetMatrix.toTHREE())\n            } else {\n              var skeletonRoot = scene.findNode(this.mBones[i].mName)\n              if (!skeletonRoot) return\n              var threeSkeletonRoot = skeletonRoot.toTHREE(scene)\n              var threeSkeletonRootBone = cloneTreeToBones(threeSkeletonRoot, scene)\n              this.threeNode.add(threeSkeletonRootBone)\n              var bone = findMatchingBone(threeSkeletonRootBone, this.mBones[i].mName)\n              var tbone = bone\n              allBones.push(tbone)\n              //tbone.matrixAutoUpdate = false;\n              offsetMatrix.push(this.mBones[i].mOffsetMatrix.toTHREE())\n            }\n          }\n\n          var skeleton = new Skeleton(allBones, offsetMatrix)\n\n          this.threeNode.bind(skeleton, new Matrix4())\n          this.threeNode.material.skinning = true\n        }\n\n        this.toTHREE = function (scene) {\n          if (this.threeNode) return this.threeNode\n          var geometry = new BufferGeometry()\n          var mat\n          if (scene.mMaterials[this.mMaterialIndex]) mat = scene.mMaterials[this.mMaterialIndex].toTHREE(scene)\n          else mat = new MeshLambertMaterial()\n          geometry.setIndex(new BufferAttribute(new Uint32Array(this.mIndexArray), 1))\n          geometry.setAttribute('position', new BufferAttribute(this.mVertexBuffer, 3))\n          if (this.mNormalBuffer && this.mNormalBuffer.length > 0) {\n            geometry.setAttribute('normal', new BufferAttribute(this.mNormalBuffer, 3))\n          }\n          if (this.mColorBuffer && this.mColorBuffer.length > 0) {\n            geometry.setAttribute('color', new BufferAttribute(this.mColorBuffer, 4))\n          }\n          if (this.mTexCoordsBuffers[0] && this.mTexCoordsBuffers[0].length > 0) {\n            geometry.setAttribute('uv', new BufferAttribute(new Float32Array(this.mTexCoordsBuffers[0]), 2))\n          }\n          if (this.mTexCoordsBuffers[1] && this.mTexCoordsBuffers[1].length > 0) {\n            geometry.setAttribute('uv1', new BufferAttribute(new Float32Array(this.mTexCoordsBuffers[1]), 2))\n          }\n          if (this.mTangentBuffer && this.mTangentBuffer.length > 0) {\n            geometry.setAttribute('tangents', new BufferAttribute(this.mTangentBuffer, 3))\n          }\n          if (this.mBitangentBuffer && this.mBitangentBuffer.length > 0) {\n            geometry.setAttribute('bitangents', new BufferAttribute(this.mBitangentBuffer, 3))\n          }\n          if (this.mBones.length > 0) {\n            var weights = []\n            var bones = []\n\n            for (let i = 0; i < this.mBones.length; i++) {\n              for (let j = 0; j < this.mBones[i].mWeights.length; j++) {\n                var weight = this.mBones[i].mWeights[j]\n                if (weight) {\n                  if (!weights[weight.mVertexId]) weights[weight.mVertexId] = []\n                  if (!bones[weight.mVertexId]) bones[weight.mVertexId] = []\n                  weights[weight.mVertexId].push(weight.mWeight)\n                  bones[weight.mVertexId].push(parseInt(i))\n                }\n              }\n            }\n\n            for (let i in bones) {\n              sortWeights(bones[i], weights[i])\n            }\n\n            var _weights = []\n            var _bones = []\n\n            for (let i = 0; i < weights.length; i++) {\n              for (let j = 0; j < 4; j++) {\n                if (weights[i] && bones[i]) {\n                  _weights.push(weights[i][j])\n                  _bones.push(bones[i][j])\n                } else {\n                  _weights.push(0)\n                  _bones.push(0)\n                }\n              }\n            }\n\n            geometry.setAttribute('skinWeight', new BufferAttribute(new Float32Array(_weights), BONESPERVERT))\n            geometry.setAttribute('skinIndex', new BufferAttribute(new Float32Array(_bones), BONESPERVERT))\n          }\n\n          var mesh\n\n          if (this.mBones.length == 0) mesh = new Mesh(geometry, mat)\n\n          if (this.mBones.length > 0) {\n            mesh = new SkinnedMesh(geometry, mat)\n            mesh.normalizeSkinWeights()\n          }\n\n          this.threeNode = mesh\n          //mesh.matrixAutoUpdate = false;\n          return mesh\n        }\n      }\n    }\n\n    class aiFace {\n      constructor() {\n        this.mNumIndices = 0\n        this.mIndices = []\n      }\n    }\n\n    class aiVector3D {\n      constructor() {\n        this.x = 0\n        this.y = 0\n        this.z = 0\n\n        this.toTHREE = function () {\n          return new Vector3(this.x, this.y, this.z)\n        }\n      }\n    }\n\n    class aiColor3D {\n      constructor() {\n        this.r = 0\n        this.g = 0\n        this.b = 0\n        this.a = 0\n        this.toTHREE = function () {\n          return new Color(this.r, this.g, this.b)\n        }\n      }\n    }\n\n    class aiQuaternion {\n      constructor() {\n        this.x = 0\n        this.y = 0\n        this.z = 0\n        this.w = 0\n        this.toTHREE = function () {\n          return new Quaternion(this.x, this.y, this.z, this.w)\n        }\n      }\n    }\n\n    class aiVertexWeight {\n      constructor() {\n        this.mVertexId = 0\n        this.mWeight = 0\n      }\n    }\n\n    class aiString {\n      constructor() {\n        this.data = []\n        this.toString = function () {\n          var str = ''\n          this.data.forEach(function (i) {\n            str += String.fromCharCode(i)\n          })\n          return str.replace(/[^\\x20-\\x7E]+/g, '')\n        }\n      }\n    }\n\n    class aiVectorKey {\n      constructor() {\n        this.mTime = 0\n        this.mValue = null\n      }\n    }\n\n    class aiQuatKey {\n      constructor() {\n        this.mTime = 0\n        this.mValue = null\n      }\n    }\n\n    class aiNode {\n      constructor() {\n        this.mName = ''\n        this.mTransformation = []\n        this.mNumChildren = 0\n        this.mNumMeshes = 0\n        this.mMeshes = []\n        this.mChildren = []\n        this.toTHREE = function (scene) {\n          if (this.threeNode) return this.threeNode\n          var o = new Object3D()\n          o.name = this.mName\n          o.matrix = this.mTransformation.toTHREE()\n\n          for (let i = 0; i < this.mChildren.length; i++) {\n            o.add(this.mChildren[i].toTHREE(scene))\n          }\n\n          for (let i = 0; i < this.mMeshes.length; i++) {\n            o.add(scene.mMeshes[this.mMeshes[i]].toTHREE(scene))\n          }\n\n          this.threeNode = o\n          //o.matrixAutoUpdate = false;\n          o.matrix.decompose(o.position, o.quaternion, o.scale)\n          return o\n        }\n      }\n    }\n\n    class aiBone {\n      constructor() {\n        this.mName = ''\n        this.mNumWeights = 0\n        this.mOffsetMatrix = 0\n      }\n    }\n\n    class aiMaterialProperty {\n      constructor() {\n        this.mKey = ''\n        this.mSemantic = 0\n        this.mIndex = 0\n        this.mData = []\n        this.mDataLength = 0\n        this.mType = 0\n        this.dataAsColor = function () {\n          var array = new Uint8Array(this.mData).buffer\n          var reader = new DataView(array)\n          var r = reader.getFloat32(0, true)\n          var g = reader.getFloat32(4, true)\n          var b = reader.getFloat32(8, true)\n          //var a = reader.getFloat32(12, true);\n          return new Color(r, g, b)\n        }\n\n        this.dataAsFloat = function () {\n          var array = new Uint8Array(this.mData).buffer\n          var reader = new DataView(array)\n          var r = reader.getFloat32(0, true)\n          return r\n        }\n\n        this.dataAsBool = function () {\n          var array = new Uint8Array(this.mData).buffer\n          var reader = new DataView(array)\n          var r = reader.getFloat32(0, true)\n          return !!r\n        }\n\n        this.dataAsString = function () {\n          var s = new aiString()\n          s.data = this.mData\n          return s.toString()\n        }\n\n        this.dataAsMap = function () {\n          var s = new aiString()\n          s.data = this.mData\n          var path = s.toString()\n          path = path.replace(/\\\\/g, '/')\n\n          if (path.indexOf('/') != -1) {\n            path = path.substr(path.lastIndexOf('/') + 1)\n          }\n\n          return textureLoader.load(path)\n        }\n      }\n    }\n\n    var namePropMapping = {\n      '?mat.name': 'name',\n      '$mat.shadingm': 'shading',\n      '$mat.twosided': 'twoSided',\n      '$mat.wireframe': 'wireframe',\n      '$clr.ambient': 'ambient',\n      '$clr.diffuse': 'color',\n      '$clr.specular': 'specular',\n      '$clr.emissive': 'emissive',\n      '$clr.transparent': 'transparent',\n      '$clr.reflective': 'reflect',\n      '$mat.shininess': 'shininess',\n      '$mat.reflectivity': 'reflectivity',\n      '$mat.refracti': 'refraction',\n      '$tex.file': 'map',\n    }\n\n    var nameTypeMapping = {\n      '?mat.name': 'string',\n      '$mat.shadingm': 'bool',\n      '$mat.twosided': 'bool',\n      '$mat.wireframe': 'bool',\n      '$clr.ambient': 'color',\n      '$clr.diffuse': 'color',\n      '$clr.specular': 'color',\n      '$clr.emissive': 'color',\n      '$clr.transparent': 'color',\n      '$clr.reflective': 'color',\n      '$mat.shininess': 'float',\n      '$mat.reflectivity': 'float',\n      '$mat.refracti': 'float',\n      '$tex.file': 'map',\n    }\n\n    class aiMaterial {\n      constructor() {\n        this.mNumAllocated = 0\n        this.mNumProperties = 0\n        this.mProperties = []\n        this.toTHREE = function () {\n          var mat = new MeshPhongMaterial()\n\n          for (let i = 0; i < this.mProperties.length; i++) {\n            if (nameTypeMapping[this.mProperties[i].mKey] == 'float') {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsFloat()\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == 'color') {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsColor()\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == 'bool') {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsBool()\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == 'string') {\n              mat[namePropMapping[this.mProperties[i].mKey]] = this.mProperties[i].dataAsString()\n            }\n            if (nameTypeMapping[this.mProperties[i].mKey] == 'map') {\n              var prop = this.mProperties[i]\n              if (prop.mSemantic == aiTextureType_DIFFUSE) mat.map = this.mProperties[i].dataAsMap()\n              if (prop.mSemantic == aiTextureType_NORMALS) mat.normalMap = this.mProperties[i].dataAsMap()\n              if (prop.mSemantic == aiTextureType_LIGHTMAP) mat.lightMap = this.mProperties[i].dataAsMap()\n              if (prop.mSemantic == aiTextureType_OPACITY) mat.alphaMap = this.mProperties[i].dataAsMap()\n            }\n          }\n\n          mat.ambient.r = 0.53\n          mat.ambient.g = 0.53\n          mat.ambient.b = 0.53\n          mat.color.r = 1\n          mat.color.g = 1\n          mat.color.b = 1\n          return mat\n        }\n      }\n    }\n\n    function veclerp(v1, v2, l) {\n      var v = new Vector3()\n      var lm1 = 1 - l\n      v.x = v1.x * l + v2.x * lm1\n      v.y = v1.y * l + v2.y * lm1\n      v.z = v1.z * l + v2.z * lm1\n      return v\n    }\n\n    function quatlerp(q1, q2, l) {\n      return q1.clone().slerp(q2, 1 - l)\n    }\n\n    function sampleTrack(keys, time, lne, lerp) {\n      if (keys.length == 1) return keys[0].mValue.toTHREE()\n\n      var dist = Infinity\n      var key = null\n      var nextKey = null\n\n      for (let i = 0; i < keys.length; i++) {\n        var timeDist = Math.abs(keys[i].mTime - time)\n\n        if (timeDist < dist && keys[i].mTime <= time) {\n          dist = timeDist\n          key = keys[i]\n          nextKey = keys[i + 1]\n        }\n      }\n\n      if (!key) {\n        return null\n      } else if (nextKey) {\n        var dT = nextKey.mTime - key.mTime\n        var T = key.mTime - time\n        var l = T / dT\n\n        return lerp(key.mValue.toTHREE(), nextKey.mValue.toTHREE(), l)\n      } else {\n        nextKey = keys[0].clone()\n        nextKey.mTime += lne\n\n        var dT = nextKey.mTime - key.mTime\n        var T = key.mTime - time\n        var l = T / dT\n\n        return lerp(key.mValue.toTHREE(), nextKey.mValue.toTHREE(), l)\n      }\n    }\n\n    class aiNodeAnim {\n      constructor() {\n        this.mNodeName = ''\n        this.mNumPositionKeys = 0\n        this.mNumRotationKeys = 0\n        this.mNumScalingKeys = 0\n        this.mPositionKeys = []\n        this.mRotationKeys = []\n        this.mScalingKeys = []\n        this.mPreState = ''\n        this.mPostState = ''\n        this.init = function (tps) {\n          if (!tps) tps = 1\n\n          function t(t) {\n            t.mTime /= tps\n          }\n\n          this.mPositionKeys.forEach(t)\n          this.mRotationKeys.forEach(t)\n          this.mScalingKeys.forEach(t)\n        }\n\n        this.sortKeys = function () {\n          function comp(a, b) {\n            return a.mTime - b.mTime\n          }\n\n          this.mPositionKeys.sort(comp)\n          this.mRotationKeys.sort(comp)\n          this.mScalingKeys.sort(comp)\n        }\n\n        this.getLength = function () {\n          return Math.max(\n            Math.max.apply(\n              null,\n              this.mPositionKeys.map(function (a) {\n                return a.mTime\n              }),\n            ),\n            Math.max.apply(\n              null,\n              this.mRotationKeys.map(function (a) {\n                return a.mTime\n              }),\n            ),\n            Math.max.apply(\n              null,\n              this.mScalingKeys.map(function (a) {\n                return a.mTime\n              }),\n            ),\n          )\n        }\n\n        this.toTHREE = function (o) {\n          this.sortKeys()\n          var length = this.getLength()\n          var track = new Virtulous.KeyFrameTrack()\n\n          for (let i = 0; i < length; i += 0.05) {\n            var matrix = new Matrix4()\n            var time = i\n            var pos = sampleTrack(this.mPositionKeys, time, length, veclerp)\n            var scale = sampleTrack(this.mScalingKeys, time, length, veclerp)\n            var rotation = sampleTrack(this.mRotationKeys, time, length, quatlerp)\n            matrix.compose(pos, rotation, scale)\n\n            var key = new Virtulous.KeyFrame(time, matrix)\n            track.addKey(key)\n          }\n\n          track.target = o.findNode(this.mNodeName).toTHREE()\n\n          var tracks = [track]\n\n          if (o.nodeToBoneMap[this.mNodeName]) {\n            for (let i = 0; i < o.nodeToBoneMap[this.mNodeName].length; i++) {\n              var t2 = track.clone()\n              t2.target = o.nodeToBoneMap[this.mNodeName][i]\n              tracks.push(t2)\n            }\n          }\n\n          return tracks\n        }\n      }\n    }\n\n    class aiAnimation {\n      constructor() {\n        this.mName = ''\n        this.mDuration = 0\n        this.mTicksPerSecond = 0\n        this.mNumChannels = 0\n        this.mChannels = []\n        this.toTHREE = function (root) {\n          var animationHandle = new Virtulous.Animation()\n\n          for (let i in this.mChannels) {\n            this.mChannels[i].init(this.mTicksPerSecond)\n\n            var tracks = this.mChannels[i].toTHREE(root)\n\n            for (let j in tracks) {\n              tracks[j].init()\n              animationHandle.addTrack(tracks[j])\n            }\n          }\n\n          animationHandle.length = Math.max.apply(\n            null,\n            animationHandle.tracks.map(function (e) {\n              return e.length\n            }),\n          )\n          return animationHandle\n        }\n      }\n    }\n\n    class aiTexture {\n      constructor() {\n        this.mWidth = 0\n        this.mHeight = 0\n        this.texAchFormatHint = []\n        this.pcData = []\n      }\n    }\n\n    class aiLight {\n      constructor() {\n        this.mName = ''\n        this.mType = 0\n        this.mAttenuationConstant = 0\n        this.mAttenuationLinear = 0\n        this.mAttenuationQuadratic = 0\n        this.mAngleInnerCone = 0\n        this.mAngleOuterCone = 0\n        this.mColorDiffuse = null\n        this.mColorSpecular = null\n        this.mColorAmbient = null\n      }\n    }\n\n    class aiCamera {\n      constructor() {\n        this.mName = ''\n        this.mPosition = null\n        this.mLookAt = null\n        this.mUp = null\n        this.mHorizontalFOV = 0\n        this.mClipPlaneNear = 0\n        this.mClipPlaneFar = 0\n        this.mAspect = 0\n      }\n    }\n\n    class aiScene {\n      constructor() {\n        this.versionMajor = 0\n        this.versionMinor = 0\n        this.versionRevision = 0\n        this.compileFlags = 0\n        this.mFlags = 0\n        this.mNumMeshes = 0\n        this.mNumMaterials = 0\n        this.mNumAnimations = 0\n        this.mNumTextures = 0\n        this.mNumLights = 0\n        this.mNumCameras = 0\n        this.mRootNode = null\n        this.mMeshes = []\n        this.mMaterials = []\n        this.mAnimations = []\n        this.mLights = []\n        this.mCameras = []\n        this.nodeToBoneMap = {}\n        this.findNode = function (name, root) {\n          if (!root) {\n            root = this.mRootNode\n          }\n\n          if (root.mName == name) {\n            return root\n          }\n\n          for (let i = 0; i < root.mChildren.length; i++) {\n            var ret = this.findNode(name, root.mChildren[i])\n            if (ret) return ret\n          }\n\n          return null\n        }\n\n        this.toTHREE = function () {\n          this.nodeCount = 0\n\n          markBones(this)\n\n          var o = this.mRootNode.toTHREE(this)\n\n          for (let i in this.mMeshes) this.mMeshes[i].hookupSkeletons(this)\n\n          if (this.mAnimations.length > 0) {\n            var a = this.mAnimations[0].toTHREE(this)\n          }\n\n          return { object: o, animation: a }\n        }\n      }\n    }\n\n    class aiMatrix4 {\n      constructor() {\n        this.elements = [[], [], [], []]\n        this.toTHREE = function () {\n          var m = new Matrix4()\n\n          for (let i = 0; i < 4; ++i) {\n            for (let i2 = 0; i2 < 4; ++i2) {\n              m.elements[i * 4 + i2] = this.elements[i2][i]\n            }\n          }\n\n          return m\n        }\n      }\n    }\n\n    var littleEndian = true\n\n    function readFloat(dataview) {\n      var val = dataview.getFloat32(dataview.readOffset, littleEndian)\n      dataview.readOffset += 4\n      return val\n    }\n\n    function Read_double(dataview) {\n      var val = dataview.getFloat64(dataview.readOffset, littleEndian)\n      dataview.readOffset += 8\n      return val\n    }\n\n    function Read_uint8_t(dataview) {\n      var val = dataview.getUint8(dataview.readOffset)\n      dataview.readOffset += 1\n      return val\n    }\n\n    function Read_uint16_t(dataview) {\n      var val = dataview.getUint16(dataview.readOffset, littleEndian)\n      dataview.readOffset += 2\n      return val\n    }\n\n    function Read_unsigned_int(dataview) {\n      var val = dataview.getUint32(dataview.readOffset, littleEndian)\n      dataview.readOffset += 4\n      return val\n    }\n\n    function Read_uint32_t(dataview) {\n      var val = dataview.getUint32(dataview.readOffset, littleEndian)\n      dataview.readOffset += 4\n      return val\n    }\n\n    function Read_aiVector3D(stream) {\n      var v = new aiVector3D()\n      v.x = readFloat(stream)\n      v.y = readFloat(stream)\n      v.z = readFloat(stream)\n      return v\n    }\n\n    function Read_aiColor3D(stream) {\n      var c = new aiColor3D()\n      c.r = readFloat(stream)\n      c.g = readFloat(stream)\n      c.b = readFloat(stream)\n      return c\n    }\n\n    function Read_aiQuaternion(stream) {\n      var v = new aiQuaternion()\n      v.w = readFloat(stream)\n      v.x = readFloat(stream)\n      v.y = readFloat(stream)\n      v.z = readFloat(stream)\n      return v\n    }\n\n    function Read_aiString(stream) {\n      var s = new aiString()\n      var stringlengthbytes = Read_unsigned_int(stream)\n      stream.ReadBytes(s.data, 1, stringlengthbytes)\n      return s.toString()\n    }\n\n    function Read_aiVertexWeight(stream) {\n      var w = new aiVertexWeight()\n      w.mVertexId = Read_unsigned_int(stream)\n      w.mWeight = readFloat(stream)\n      return w\n    }\n\n    function Read_aiMatrix4x4(stream) {\n      var m = new aiMatrix4()\n\n      for (let i = 0; i < 4; ++i) {\n        for (let i2 = 0; i2 < 4; ++i2) {\n          m.elements[i][i2] = readFloat(stream)\n        }\n      }\n\n      return m\n    }\n\n    function Read_aiVectorKey(stream) {\n      var v = new aiVectorKey()\n      v.mTime = Read_double(stream)\n      v.mValue = Read_aiVector3D(stream)\n      return v\n    }\n\n    function Read_aiQuatKey(stream) {\n      var v = new aiQuatKey()\n      v.mTime = Read_double(stream)\n      v.mValue = Read_aiQuaternion(stream)\n      return v\n    }\n\n    function ReadArray_aiVertexWeight(stream, data, size) {\n      for (let i = 0; i < size; i++) data[i] = Read_aiVertexWeight(stream)\n    }\n\n    function ReadArray_aiVectorKey(stream, data, size) {\n      for (let i = 0; i < size; i++) data[i] = Read_aiVectorKey(stream)\n    }\n\n    function ReadArray_aiQuatKey(stream, data, size) {\n      for (let i = 0; i < size; i++) data[i] = Read_aiQuatKey(stream)\n    }\n\n    function ReadBounds(stream, T /*p*/, n) {\n      // not sure what to do here, the data isn't really useful.\n      return stream.Seek(sizeof(T) * n, aiOrigin_CUR)\n    }\n\n    function ai_assert(bool) {\n      if (!bool) throw 'asset failed'\n    }\n\n    function ReadBinaryNode(stream, parent, depth) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AINODE)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      var node = new aiNode()\n      node.mParent = parent\n      node.mDepth = depth\n      node.mName = Read_aiString(stream)\n      node.mTransformation = Read_aiMatrix4x4(stream)\n      node.mNumChildren = Read_unsigned_int(stream)\n      node.mNumMeshes = Read_unsigned_int(stream)\n\n      if (node.mNumMeshes) {\n        node.mMeshes = []\n\n        for (let i = 0; i < node.mNumMeshes; ++i) {\n          node.mMeshes[i] = Read_unsigned_int(stream)\n        }\n      }\n\n      if (node.mNumChildren) {\n        node.mChildren = []\n\n        for (let i = 0; i < node.mNumChildren; ++i) {\n          var node2 = ReadBinaryNode(stream, node, depth++)\n          node.mChildren[i] = node2\n        }\n      }\n\n      return node\n    }\n\n    // -----------------------------------------------------------------------------------\n\n    function ReadBinaryBone(stream, b) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AIBONE)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      b.mName = Read_aiString(stream)\n      b.mNumWeights = Read_unsigned_int(stream)\n      b.mOffsetMatrix = Read_aiMatrix4x4(stream)\n      // for the moment we write dumb min/max values for the bones, too.\n      // maybe I'll add a better, hash-like solution later\n      if (shortened) {\n        ReadBounds(stream, b.mWeights, b.mNumWeights)\n      } else {\n        // else write as usual\n\n        b.mWeights = []\n        ReadArray_aiVertexWeight(stream, b.mWeights, b.mNumWeights)\n      }\n\n      return b\n    }\n\n    function ReadBinaryMesh(stream, mesh) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AIMESH)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      mesh.mPrimitiveTypes = Read_unsigned_int(stream)\n      mesh.mNumVertices = Read_unsigned_int(stream)\n      mesh.mNumFaces = Read_unsigned_int(stream)\n      mesh.mNumBones = Read_unsigned_int(stream)\n      mesh.mMaterialIndex = Read_unsigned_int(stream)\n      mesh.mNumUVComponents = []\n      // first of all, write bits for all existent vertex components\n      var c = Read_unsigned_int(stream)\n\n      if (c & ASSBIN_MESH_HAS_POSITIONS) {\n        if (shortened) {\n          ReadBounds(stream, mesh.mVertices, mesh.mNumVertices)\n        } else {\n          // else write as usual\n\n          mesh.mVertices = []\n          mesh.mVertexBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4)\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR)\n        }\n      }\n\n      if (c & ASSBIN_MESH_HAS_NORMALS) {\n        if (shortened) {\n          ReadBounds(stream, mesh.mNormals, mesh.mNumVertices)\n        } else {\n          // else write as usual\n\n          mesh.mNormals = []\n          mesh.mNormalBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4)\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR)\n        }\n      }\n\n      if (c & ASSBIN_MESH_HAS_TANGENTS_AND_BITANGENTS) {\n        if (shortened) {\n          ReadBounds(stream, mesh.mTangents, mesh.mNumVertices)\n          ReadBounds(stream, mesh.mBitangents, mesh.mNumVertices)\n        } else {\n          // else write as usual\n\n          mesh.mTangents = []\n          mesh.mTangentBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4)\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR)\n          mesh.mBitangents = []\n          mesh.mBitangentBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 3 * 4)\n          stream.Seek(mesh.mNumVertices * 3 * 4, aiOrigin_CUR)\n        }\n      }\n\n      for (let n = 0; n < AI_MAX_NUMBER_OF_COLOR_SETS; ++n) {\n        if (!(c & ASSBIN_MESH_HAS_COLOR(n))) break\n\n        if (shortened) {\n          ReadBounds(stream, mesh.mColors[n], mesh.mNumVertices)\n        } else {\n          // else write as usual\n\n          mesh.mColors[n] = []\n          mesh.mColorBuffer = stream.subArray32(stream.readOffset, stream.readOffset + mesh.mNumVertices * 4 * 4)\n          stream.Seek(mesh.mNumVertices * 4 * 4, aiOrigin_CUR)\n        }\n      }\n\n      mesh.mTexCoordsBuffers = []\n\n      for (let n = 0; n < AI_MAX_NUMBER_OF_TEXTURECOORDS; ++n) {\n        if (!(c & ASSBIN_MESH_HAS_TEXCOORD(n))) break\n\n        // write number of UV components\n        mesh.mNumUVComponents[n] = Read_unsigned_int(stream)\n\n        if (shortened) {\n          ReadBounds(stream, mesh.mTextureCoords[n], mesh.mNumVertices)\n        } else {\n          // else write as usual\n\n          mesh.mTextureCoords[n] = []\n          //note that assbin always writes 3d texcoords\n          mesh.mTexCoordsBuffers[n] = []\n\n          for (let uv = 0; uv < mesh.mNumVertices; uv++) {\n            mesh.mTexCoordsBuffers[n].push(readFloat(stream))\n            mesh.mTexCoordsBuffers[n].push(readFloat(stream))\n            readFloat(stream)\n          }\n        }\n      }\n\n      // write faces. There are no floating-point calculations involved\n      // in these, so we can write a simple hash over the face data\n      // to the dump file. We generate a single 32 Bit hash for 512 faces\n      // using Assimp's standard hashing function.\n      if (shortened) {\n        Read_unsigned_int(stream)\n      } else {\n        // else write as usual\n\n        // if there are less than 2^16 vertices, we can simply use 16 bit integers ...\n        mesh.mFaces = []\n        mesh.mIndexArray = []\n\n        for (let i = 0; i < mesh.mNumFaces; ++i) {\n          var f = (mesh.mFaces[i] = new aiFace())\n          // BOOST_STATIC_ASSERT(AI_MAX_FACE_INDICES <= 0xffff);\n          f.mNumIndices = Read_uint16_t(stream)\n          f.mIndices = []\n\n          for (let a = 0; a < f.mNumIndices; ++a) {\n            if (mesh.mNumVertices < 1 << 16) {\n              f.mIndices[a] = Read_uint16_t(stream)\n            } else {\n              f.mIndices[a] = Read_unsigned_int(stream)\n            }\n          }\n\n          if (f.mNumIndices === 3) {\n            mesh.mIndexArray.push(f.mIndices[0])\n            mesh.mIndexArray.push(f.mIndices[1])\n            mesh.mIndexArray.push(f.mIndices[2])\n          } else if (f.mNumIndices === 4) {\n            mesh.mIndexArray.push(f.mIndices[0])\n            mesh.mIndexArray.push(f.mIndices[1])\n            mesh.mIndexArray.push(f.mIndices[2])\n            mesh.mIndexArray.push(f.mIndices[2])\n            mesh.mIndexArray.push(f.mIndices[3])\n            mesh.mIndexArray.push(f.mIndices[0])\n          } else {\n            throw new Error(\"Sorry, can't currently triangulate polys. Use the triangulate preprocessor in Assimp.\")\n          }\n        }\n      }\n\n      // write bones\n      if (mesh.mNumBones) {\n        mesh.mBones = []\n\n        for (let a = 0; a < mesh.mNumBones; ++a) {\n          mesh.mBones[a] = new aiBone()\n          ReadBinaryBone(stream, mesh.mBones[a])\n        }\n      }\n    }\n\n    function ReadBinaryMaterialProperty(stream, prop) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AIMATERIALPROPERTY)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      prop.mKey = Read_aiString(stream)\n      prop.mSemantic = Read_unsigned_int(stream)\n      prop.mIndex = Read_unsigned_int(stream)\n      prop.mDataLength = Read_unsigned_int(stream)\n      prop.mType = Read_unsigned_int(stream)\n      prop.mData = []\n      stream.ReadBytes(prop.mData, 1, prop.mDataLength)\n    }\n\n    // -----------------------------------------------------------------------------------\n\n    function ReadBinaryMaterial(stream, mat) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AIMATERIAL)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      mat.mNumAllocated = mat.mNumProperties = Read_unsigned_int(stream)\n\n      if (mat.mNumProperties) {\n        if (mat.mProperties) {\n          delete mat.mProperties\n        }\n\n        mat.mProperties = []\n\n        for (let i = 0; i < mat.mNumProperties; ++i) {\n          mat.mProperties[i] = new aiMaterialProperty()\n          ReadBinaryMaterialProperty(stream, mat.mProperties[i])\n        }\n      }\n    }\n\n    function ReadBinaryNodeAnim(stream, nd) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AINODEANIM)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      nd.mNodeName = Read_aiString(stream)\n      nd.mNumPositionKeys = Read_unsigned_int(stream)\n      nd.mNumRotationKeys = Read_unsigned_int(stream)\n      nd.mNumScalingKeys = Read_unsigned_int(stream)\n      nd.mPreState = Read_unsigned_int(stream)\n      nd.mPostState = Read_unsigned_int(stream)\n\n      if (nd.mNumPositionKeys) {\n        if (shortened) {\n          ReadBounds(stream, nd.mPositionKeys, nd.mNumPositionKeys)\n        } else {\n          // else write as usual\n\n          nd.mPositionKeys = []\n          ReadArray_aiVectorKey(stream, nd.mPositionKeys, nd.mNumPositionKeys)\n        }\n      }\n\n      if (nd.mNumRotationKeys) {\n        if (shortened) {\n          ReadBounds(stream, nd.mRotationKeys, nd.mNumRotationKeys)\n        } else {\n          // else write as usual\n\n          nd.mRotationKeys = []\n          ReadArray_aiQuatKey(stream, nd.mRotationKeys, nd.mNumRotationKeys)\n        }\n      }\n\n      if (nd.mNumScalingKeys) {\n        if (shortened) {\n          ReadBounds(stream, nd.mScalingKeys, nd.mNumScalingKeys)\n        } else {\n          // else write as usual\n\n          nd.mScalingKeys = []\n          ReadArray_aiVectorKey(stream, nd.mScalingKeys, nd.mNumScalingKeys)\n        }\n      }\n    }\n\n    function ReadBinaryAnim(stream, anim) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AIANIMATION)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      anim.mName = Read_aiString(stream)\n      anim.mDuration = Read_double(stream)\n      anim.mTicksPerSecond = Read_double(stream)\n      anim.mNumChannels = Read_unsigned_int(stream)\n\n      if (anim.mNumChannels) {\n        anim.mChannels = []\n\n        for (let a = 0; a < anim.mNumChannels; ++a) {\n          anim.mChannels[a] = new aiNodeAnim()\n          ReadBinaryNodeAnim(stream, anim.mChannels[a])\n        }\n      }\n    }\n\n    function ReadBinaryTexture(stream, tex) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AITEXTURE)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      tex.mWidth = Read_unsigned_int(stream)\n      tex.mHeight = Read_unsigned_int(stream)\n      stream.ReadBytes(tex.achFormatHint, 1, 4)\n\n      if (!shortened) {\n        if (!tex.mHeight) {\n          tex.pcData = []\n          stream.ReadBytes(tex.pcData, 1, tex.mWidth)\n        } else {\n          tex.pcData = []\n          stream.ReadBytes(tex.pcData, 1, tex.mWidth * tex.mHeight * 4)\n        }\n      }\n    }\n\n    function ReadBinaryLight(stream, l) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AILIGHT)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      l.mName = Read_aiString(stream)\n      l.mType = Read_unsigned_int(stream)\n\n      if (l.mType != aiLightSource_DIRECTIONAL) {\n        l.mAttenuationConstant = readFloat(stream)\n        l.mAttenuationLinear = readFloat(stream)\n        l.mAttenuationQuadratic = readFloat(stream)\n      }\n\n      l.mColorDiffuse = Read_aiColor3D(stream)\n      l.mColorSpecular = Read_aiColor3D(stream)\n      l.mColorAmbient = Read_aiColor3D(stream)\n\n      if (l.mType == aiLightSource_SPOT) {\n        l.mAngleInnerCone = readFloat(stream)\n        l.mAngleOuterCone = readFloat(stream)\n      }\n    }\n\n    function ReadBinaryCamera(stream, cam) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AICAMERA)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      cam.mName = Read_aiString(stream)\n      cam.mPosition = Read_aiVector3D(stream)\n      cam.mLookAt = Read_aiVector3D(stream)\n      cam.mUp = Read_aiVector3D(stream)\n      cam.mHorizontalFOV = readFloat(stream)\n      cam.mClipPlaneNear = readFloat(stream)\n      cam.mClipPlaneFar = readFloat(stream)\n      cam.mAspect = readFloat(stream)\n    }\n\n    function ReadBinaryScene(stream, scene) {\n      var chunkID = Read_uint32_t(stream)\n      ai_assert(chunkID == ASSBIN_CHUNK_AISCENE)\n      /*uint32_t size =*/\n      Read_uint32_t(stream)\n      scene.mFlags = Read_unsigned_int(stream)\n      scene.mNumMeshes = Read_unsigned_int(stream)\n      scene.mNumMaterials = Read_unsigned_int(stream)\n      scene.mNumAnimations = Read_unsigned_int(stream)\n      scene.mNumTextures = Read_unsigned_int(stream)\n      scene.mNumLights = Read_unsigned_int(stream)\n      scene.mNumCameras = Read_unsigned_int(stream)\n      // Read node graph\n      scene.mRootNode = new aiNode()\n      scene.mRootNode = ReadBinaryNode(stream, null, 0)\n      // Read all meshes\n      if (scene.mNumMeshes) {\n        scene.mMeshes = []\n\n        for (let i = 0; i < scene.mNumMeshes; ++i) {\n          scene.mMeshes[i] = new aiMesh()\n          ReadBinaryMesh(stream, scene.mMeshes[i])\n        }\n      }\n\n      // Read materials\n      if (scene.mNumMaterials) {\n        scene.mMaterials = []\n\n        for (let i = 0; i < scene.mNumMaterials; ++i) {\n          scene.mMaterials[i] = new aiMaterial()\n          ReadBinaryMaterial(stream, scene.mMaterials[i])\n        }\n      }\n\n      // Read all animations\n      if (scene.mNumAnimations) {\n        scene.mAnimations = []\n\n        for (let i = 0; i < scene.mNumAnimations; ++i) {\n          scene.mAnimations[i] = new aiAnimation()\n          ReadBinaryAnim(stream, scene.mAnimations[i])\n        }\n      }\n\n      // Read all textures\n      if (scene.mNumTextures) {\n        scene.mTextures = []\n\n        for (let i = 0; i < scene.mNumTextures; ++i) {\n          scene.mTextures[i] = new aiTexture()\n          ReadBinaryTexture(stream, scene.mTextures[i])\n        }\n      }\n\n      // Read lights\n      if (scene.mNumLights) {\n        scene.mLights = []\n\n        for (let i = 0; i < scene.mNumLights; ++i) {\n          scene.mLights[i] = new aiLight()\n          ReadBinaryLight(stream, scene.mLights[i])\n        }\n      }\n\n      // Read cameras\n      if (scene.mNumCameras) {\n        scene.mCameras = []\n\n        for (let i = 0; i < scene.mNumCameras; ++i) {\n          scene.mCameras[i] = new aiCamera()\n          ReadBinaryCamera(stream, scene.mCameras[i])\n        }\n      }\n    }\n\n    var aiOrigin_CUR = 0\n    var aiOrigin_BEG = 1\n\n    function extendStream(stream) {\n      stream.readOffset = 0\n      stream.Seek = function (off, ori) {\n        if (ori == aiOrigin_CUR) {\n          stream.readOffset += off\n        }\n\n        if (ori == aiOrigin_BEG) {\n          stream.readOffset = off\n        }\n      }\n\n      stream.ReadBytes = function (buff, size, n) {\n        var bytes = size * n\n        for (let i = 0; i < bytes; i++) buff[i] = Read_uint8_t(this)\n      }\n\n      stream.subArray32 = function (start, end) {\n        var buff = this.buffer\n        var newbuff = buff.slice(start, end)\n        return new Float32Array(newbuff)\n      }\n\n      stream.subArrayUint16 = function (start, end) {\n        var buff = this.buffer\n        var newbuff = buff.slice(start, end)\n        return new Uint16Array(newbuff)\n      }\n\n      stream.subArrayUint8 = function (start, end) {\n        var buff = this.buffer\n        var newbuff = buff.slice(start, end)\n        return new Uint8Array(newbuff)\n      }\n\n      stream.subArrayUint32 = function (start, end) {\n        var buff = this.buffer\n        var newbuff = buff.slice(start, end)\n        return new Uint32Array(newbuff)\n      }\n    }\n\n    var shortened, compressed\n\n    function InternReadFile(pFiledata) {\n      var pScene = new aiScene()\n      var stream = new DataView(pFiledata)\n      extendStream(stream)\n      stream.Seek(44, aiOrigin_CUR) // signature\n      /*unsigned int versionMajor =*/\n      pScene.versionMajor = Read_unsigned_int(stream)\n      /*unsigned int versionMinor =*/\n      pScene.versionMinor = Read_unsigned_int(stream)\n      /*unsigned int versionRevision =*/\n      pScene.versionRevision = Read_unsigned_int(stream)\n      /*unsigned int compileFlags =*/\n      pScene.compileFlags = Read_unsigned_int(stream)\n      shortened = Read_uint16_t(stream) > 0\n      compressed = Read_uint16_t(stream) > 0\n      if (shortened) throw 'Shortened binaries are not supported!'\n      stream.Seek(256, aiOrigin_CUR) // original filename\n      stream.Seek(128, aiOrigin_CUR) // options\n      stream.Seek(64, aiOrigin_CUR) // padding\n      if (compressed) {\n        var uncompressedSize = Read_uint32_t(stream)\n        var compressedSize = stream.FileSize() - stream.Tell()\n        var compressedData = []\n        stream.Read(compressedData, 1, compressedSize)\n        var uncompressedData = []\n        uncompress(uncompressedData, uncompressedSize, compressedData, compressedSize)\n        var buff = new ArrayBuffer(uncompressedData)\n        ReadBinaryScene(buff, pScene)\n      } else {\n        ReadBinaryScene(stream, pScene)\n      }\n\n      return pScene.toTHREE()\n    }\n\n    return InternReadFile(buffer)\n  }\n}\n\nexport { AssimpLoader }\n"], "mappings": ";AAoBA,MAAMA,YAAA,SAAqBC,MAAA,CAAO;EAChCC,KAAKC,GAAA,EAAKC,MAAA,EAAQC,UAAA,EAAYC,OAAA,EAAS;IACrC,IAAIC,KAAA,GAAQ;IAEZ,IAAIC,IAAA,GAAOD,KAAA,CAAMC,IAAA,KAAS,KAAKC,WAAA,CAAYC,cAAA,CAAeP,GAAG,IAAII,KAAA,CAAMC,IAAA;IAEvE,IAAIG,MAAA,GAAS,IAAIC,UAAA,CAAWL,KAAA,CAAMM,OAAO;IACzCF,MAAA,CAAOG,OAAA,CAAQP,KAAA,CAAMC,IAAI;IACzBG,MAAA,CAAOI,eAAA,CAAgB,aAAa;IACpCJ,MAAA,CAAOK,gBAAA,CAAiBT,KAAA,CAAMU,aAAa;IAC3CN,MAAA,CAAOO,kBAAA,CAAmBX,KAAA,CAAMY,eAAe;IAE/CR,MAAA,CAAOT,IAAA,CACLC,GAAA,EACA,UAAUiB,MAAA,EAAQ;MAChB,IAAI;QACFhB,MAAA,CAAOG,KAAA,CAAMc,KAAA,CAAMD,MAAA,EAAQZ,IAAI,CAAC;MACjC,SAAQc,CAAA,EAAP;QACA,IAAIhB,OAAA,EAAS;UACXA,OAAA,CAAQgB,CAAC;QACrB,OAAiB;UACLC,OAAA,CAAQC,KAAA,CAAMF,CAAC;QAChB;QAEDf,KAAA,CAAMM,OAAA,CAAQY,SAAA,CAAUtB,GAAG;MAC5B;IACF,GACDE,UAAA,EACAC,OACD;EACF;EAEDe,MAAMD,MAAA,EAAQZ,IAAA,EAAM;IAClB,IAAIkB,aAAA,GAAgB,IAAIC,aAAA,CAAc,KAAKd,OAAO;IAClDa,aAAA,CAAcZ,OAAA,CAAQ,KAAKc,YAAA,IAAgBpB,IAAI,EAAEqB,cAAA,CAAe,KAAKC,WAAW;IAEhF,IAAIC,SAAA,GAAY,CAAE;IAElBA,SAAA,CAAUC,QAAA,GAAW,MAAM;MACzBC,YAAYC,IAAA,EAAMC,MAAA,EAAQ;QACxB,KAAKD,IAAA,GAAOA,IAAA;QACZ,KAAKC,MAAA,GAASA,MAAA,CAAOC,KAAA,CAAO;QAC5B,KAAKC,QAAA,GAAW,IAAIC,OAAA,CAAS;QAC7B,KAAKC,UAAA,GAAa,IAAIC,UAAA,CAAY;QAClC,KAAKC,KAAA,GAAQ,IAAIH,OAAA,CAAQ,GAAG,GAAG,CAAC;QAChC,KAAKH,MAAA,CAAOO,SAAA,CAAU,KAAKL,QAAA,EAAU,KAAKE,UAAA,EAAY,KAAKE,KAAK;QAChE,KAAKL,KAAA,GAAQ,YAAY;UACvB,IAAIO,CAAA,GAAI,IAAIZ,SAAA,CAAUC,QAAA,CAAS,KAAKE,IAAA,EAAM,KAAKC,MAAM;UACrD,OAAOQ,CAAA;QACR;QAED,KAAKC,IAAA,GAAO,UAAUC,OAAA,EAASC,KAAA,EAAM;UACnCA,KAAA,IAAQ,KAAKZ,IAAA;UACb,IAAIa,IAAA,GAAOF,OAAA,CAAQX,IAAA,GAAO,KAAKA,IAAA;UAC/B,IAAIc,CAAA,GAAIF,KAAA,GAAOC,IAAA;UACf,IAAIE,EAAA,GAAK,IAAID,CAAA;UACb,IAAIE,MAAA,GAAS,KAAKb,QAAA;UAClB,IAAIc,MAAA,GAAS,KAAKZ,UAAA;UAElB,IAAIa,OAAA,GAAUP,OAAA,CAAQR,QAAA;UACtB,IAAIgB,OAAA,GAAUR,OAAA,CAAQN,UAAA;UAEtBR,SAAA,CAAUC,QAAA,CAASsB,UAAA,CAAWC,CAAA,GAAIL,MAAA,CAAOK,CAAA,GAAIN,EAAA,GAAKG,OAAA,CAAQG,CAAA,GAAIP,CAAA;UAC9DjB,SAAA,CAAUC,QAAA,CAASsB,UAAA,CAAWE,CAAA,GAAIN,MAAA,CAAOM,CAAA,GAAIP,EAAA,GAAKG,OAAA,CAAQI,CAAA,GAAIR,CAAA;UAC9DjB,SAAA,CAAUC,QAAA,CAASsB,UAAA,CAAWG,CAAA,GAAIP,MAAA,CAAOO,CAAA,GAAIR,EAAA,GAAKG,OAAA,CAAQK,CAAA,GAAIT,CAAA;UAI9DjB,SAAA,CAAUC,QAAA,CAAS0B,WAAA,CAAYC,GAAA,CAAIR,MAAA,CAAOI,CAAA,EAAGJ,MAAA,CAAOK,CAAA,EAAGL,MAAA,CAAOM,CAAA,EAAGN,MAAA,CAAOS,CAAC;UACzE7B,SAAA,CAAUC,QAAA,CAAS0B,WAAA,CAAYG,KAAA,CAAMR,OAAA,EAASL,CAAC;UAC/C,OAAOjB,SAAA,CAAUC,QAAA,CAAS8B,aAAA,CAAcC,OAAA,CACtChC,SAAA,CAAUC,QAAA,CAASsB,UAAA,EACnBvB,SAAA,CAAUC,QAAA,CAAS0B,WAAA,EACnB3B,SAAA,CAAUC,QAAA,CAASgC,YACpB;QACF;MACF;IACF;IAEDjC,SAAA,CAAUC,QAAA,CAASsB,UAAA,GAAa,IAAIhB,OAAA,CAAS;IAC7CP,SAAA,CAAUC,QAAA,CAAS0B,WAAA,GAAc,IAAIlB,UAAA,CAAY;IACjDT,SAAA,CAAUC,QAAA,CAASgC,YAAA,GAAe,IAAI1B,OAAA,CAAQ,GAAG,GAAG,CAAC;IACrDP,SAAA,CAAUC,QAAA,CAAS8B,aAAA,GAAgB,IAAIG,OAAA,CAAS;IAChDlC,SAAA,CAAUmC,aAAA,GAAgB,YAAY;MACpC,KAAKC,IAAA,GAAO,EAAE;MACd,KAAKC,MAAA,GAAS;MACd,KAAKlC,IAAA,GAAO;MACZ,KAAKmC,MAAA,GAAS;MACd,KAAKC,WAAA,GAAc,CAAE;MACrB,KAAKC,GAAA,GAAM;MACX,KAAKC,MAAA,GAAS,UAAUC,GAAA,EAAK;QAC3B,KAAKN,IAAA,CAAKO,IAAA,CAAKD,GAAG;MACnB;MAED,KAAKE,IAAA,GAAO,YAAY;QACtB,KAAKC,QAAA,CAAU;QAEf,IAAI,KAAKT,IAAA,CAAKE,MAAA,GAAS,GAAG,KAAKA,MAAA,GAAS,KAAKF,IAAA,CAAK,KAAKA,IAAA,CAAKE,MAAA,GAAS,CAAC,EAAEnC,IAAA,MACnE,KAAKmC,MAAA,GAAS;QAEnB,IAAI,CAAC,KAAKE,GAAA,EAAK;QAEf,SAASM,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKR,MAAA,GAAS,KAAKE,GAAA,EAAKM,CAAA,IAAK;UAC/C,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKX,IAAA,CAAKE,MAAA,EAAQS,CAAA,IAAK;YACzC,IAAI,KAAKX,IAAA,CAAKW,CAAC,EAAE5C,IAAA,IAAQ2C,CAAA,EAAG;cAC1B,KAAKP,WAAA,CAAYO,CAAC,IAAIC,CAAA;cACtB;YACd,WAAuB,KAAKX,IAAA,CAAKW,CAAC,EAAE5C,IAAA,GAAO2C,CAAA,GAAI,KAAKN,GAAA,IAAO,KAAKJ,IAAA,CAAKW,CAAA,GAAI,CAAC,KAAK,KAAKX,IAAA,CAAKW,CAAA,GAAI,CAAC,EAAE5C,IAAA,IAAQ2C,CAAA,GAAI,KAAKN,GAAA,EAAK;cACxG,KAAKD,WAAA,CAAYO,CAAC,IAAIC,CAAA;cACtB;YACD;UACF;QACF;MACF;MAED,KAAKC,cAAA,GAAiB,UAAUC,IAAA,EAAM;QACpC,IAAIT,GAAA,GAAMS,IAAA,CAAKT,GAAA;QACf,KAAKH,MAAA,GAASY,IAAA,CAAKC,IAAA;QACnB,IAAIC,KAAA,GAAQF,IAAA,CAAKG,SAAA,CAAU,CAAC,EAAEhB,IAAA;QAC9B,SAASW,CAAA,GAAI,GAAGA,CAAA,GAAII,KAAA,CAAMb,MAAA,EAAQS,CAAA,IAAK;UACrC,KAAKN,MAAA,CAAO,IAAIzC,SAAA,CAAUC,QAAA,CAAS8C,CAAA,GAAIP,GAAA,IAAOW,KAAA,CAAMJ,CAAC,EAAE5C,IAAA,EAAMgD,KAAA,CAAMJ,CAAC,EAAEM,OAAA,CAAQ,CAAC,EAAEJ,IAAI,CAAC;QACvF;QAED,KAAKL,IAAA,CAAM;MACZ;MAED,KAAKU,gBAAA,GAAmB,UAAUL,IAAA,EAAM;QACtC,IAAIE,KAAA,GAAQF,IAAA,CAAKb,IAAA;QACjB,IAAII,GAAA,GAAM,KAAKA,GAAA;QAEf,SAASO,CAAA,GAAI,GAAGA,CAAA,GAAII,KAAA,CAAMb,MAAA,EAAQS,CAAA,IAAK;UACrC,KAAKN,MAAA,CAAO,IAAIzC,SAAA,CAAUC,QAAA,CAAS8C,CAAA,GAAIP,GAAA,IAAOW,KAAA,CAAMJ,CAAC,EAAE5C,IAAA,EAAMgD,KAAA,CAAMJ,CAAC,EAAE3C,MAAM,CAAC;QAC9E;QAED,KAAKwC,IAAA,CAAM;MACZ;MAED,KAAKC,QAAA,GAAW,YAAY;QAC1B,KAAKT,IAAA,CAAKmB,IAAA,CAAK,KAAKC,WAAW;MAChC;MAED,KAAKA,WAAA,GAAc,UAAUC,CAAA,EAAGC,CAAA,EAAG;QACjC,OAAOD,CAAA,CAAEtD,IAAA,GAAOuD,CAAA,CAAEvD,IAAA;MACnB;MAED,KAAKE,KAAA,GAAQ,YAAY;QACvB,IAAIsD,CAAA,GAAI,IAAI3D,SAAA,CAAUmC,aAAA,CAAe;QACrCwB,CAAA,CAAEtB,MAAA,GAAS,KAAKA,MAAA;QAChBsB,CAAA,CAAExD,IAAA,GAAO,KAAKA,IAAA;QACdwD,CAAA,CAAErB,MAAA,GAAS,KAAKA,MAAA;QAEhB,SAASS,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKX,IAAA,CAAKE,MAAA,EAAQS,CAAA,IAAK;UACzCY,CAAA,CAAElB,MAAA,CAAO,KAAKL,IAAA,CAAKW,CAAC,EAAE1C,KAAA,EAAO;QAC9B;QAEDsD,CAAA,CAAEf,IAAA,CAAM;QACR,OAAOe,CAAA;MACR;MAED,KAAKC,QAAA,GAAW,UAAUC,IAAA,EAAMC,WAAA,EAAa;QAC3C,IAAI,CAACA,WAAA,EAAaA,WAAA,GAAc9D,SAAA,CAAU+D,0BAAA;QAC1C,KAAK1B,MAAA,GAASyB,WAAA,CAAYD,IAAA,EAAM,KAAKxB,MAAM;MAC5C;MAED,KAAK2B,cAAA,GAAiB,UAAU7D,IAAA,EAAM;QACpCA,IAAA,IAAQ,KAAKqC,GAAA;QACbrC,IAAA,GAAO8D,IAAA,CAAKC,KAAA,CAAM/D,IAAI;QACtB,OAAO,KAAKoC,WAAA,CAAYpC,IAAI,KAAK;MAClC;MAED,KAAKgE,OAAA,GAAU,UAAUhE,IAAA,EAAM;QAC7BA,IAAA,GAAO8D,IAAA,CAAKG,GAAA,CAAIjE,IAAI;QACpB,IAAI,KAAKmC,MAAA,EAAQnC,IAAA,GAAQA,IAAA,GAAO,KAAKmC,MAAA,GAAU;QAC/C,IAAI+B,IAAA,GAAO;QACX,IAAIC,IAAA,GAAO;QAEX,SAASvB,CAAA,GAAI,KAAKiB,cAAA,CAAe7D,IAAI,GAAG4C,CAAA,GAAI,KAAKX,IAAA,CAAKE,MAAA,EAAQS,CAAA,IAAK;UACjE,IAAI,KAAKX,IAAA,CAAKW,CAAC,EAAE5C,IAAA,IAAQA,IAAA,EAAM;YAC7BkE,IAAA,GAAO,KAAKjC,IAAA,CAAKW,CAAC;YAClBuB,IAAA,GAAO,KAAKlC,IAAA,CAAKW,CAAC;YAClB;UACZ,WAAqB,KAAKX,IAAA,CAAKW,CAAC,EAAE5C,IAAA,GAAOA,IAAA,IAAQ,KAAKiC,IAAA,CAAKW,CAAA,GAAI,CAAC,KAAK,KAAKX,IAAA,CAAKW,CAAA,GAAI,CAAC,EAAE5C,IAAA,GAAOA,IAAA,EAAM;YACvFkE,IAAA,GAAO,KAAKjC,IAAA,CAAKW,CAAC;YAClBuB,IAAA,GAAO,KAAKlC,IAAA,CAAKW,CAAA,GAAI,CAAC;YACtB;UACD,WAAU,KAAKX,IAAA,CAAKW,CAAC,EAAE5C,IAAA,GAAOA,IAAA,IAAQ4C,CAAA,IAAK,KAAKX,IAAA,CAAKE,MAAA,GAAS,GAAG;YAChE+B,IAAA,GAAO,KAAKjC,IAAA,CAAKW,CAAC;YAClBuB,IAAA,GAAO,KAAKlC,IAAA,CAAK,CAAC,EAAE/B,KAAA,CAAO;YAC3BiE,IAAA,CAAKnE,IAAA,IAAQ,KAAKmC,MAAA,GAAS;YAC3B;UACD;QACF;QAED,IAAI+B,IAAA,IAAQC,IAAA,IAAQD,IAAA,KAASC,IAAA,EAAM;UACjC,KAAKjC,MAAA,CAAOkC,gBAAA,GAAmB;UAC/B,KAAKlC,MAAA,CAAOjC,MAAA,CAAOoE,IAAA,CAAKH,IAAA,CAAKxD,IAAA,CAAKyD,IAAA,EAAMnE,IAAI,CAAC;UAC7C,KAAKkC,MAAA,CAAOoC,sBAAA,GAAyB;UACrC;QACD;QAED,IAAIJ,IAAA,IAAQC,IAAA,IAAQD,IAAA,IAAQC,IAAA,EAAM;UAChC,KAAKjC,MAAA,CAAOkC,gBAAA,GAAmB;UAC/B,KAAKlC,MAAA,CAAOjC,MAAA,CAAOoE,IAAA,CAAKH,IAAA,CAAKjE,MAAM;UACnC,KAAKiC,MAAA,CAAOoC,sBAAA,GAAyB;UACrC;QACD;MACF;IACF;IAEDzE,SAAA,CAAU+D,0BAAA,GAA6B,UAAUF,IAAA,EAAMxB,MAAA,EAAQ;MAC7D,SAASqC,KAAKxB,IAAA,EAAMyB,IAAA,EAAM;QACxB,IAAIzB,IAAA,CAAKyB,IAAA,IAAQA,IAAA,EAAM,OAAOzB,IAAA;QAE9B,SAASH,CAAA,GAAI,GAAGA,CAAA,GAAIG,IAAA,CAAK0B,QAAA,CAAStC,MAAA,EAAQS,CAAA,IAAK;UAC7C,IAAI8B,CAAA,GAAIH,IAAA,CAAKxB,IAAA,CAAK0B,QAAA,CAAS7B,CAAC,GAAG4B,IAAI;UACnC,IAAIE,CAAA,EAAG,OAAOA,CAAA;QACf;QAED,OAAO;MACR;MAED,OAAOH,IAAA,CAAKb,IAAA,EAAMxB,MAAA,CAAOsC,IAAI;IAC9B;IAED3E,SAAA,CAAU8E,SAAA,GAAY,YAAY;MAChC,KAAKC,MAAA,GAAS,EAAE;MAChB,KAAKzC,MAAA,GAAS;MAEd,KAAK0C,QAAA,GAAW,UAAU7B,KAAA,EAAO;QAC/B,KAAK4B,MAAA,CAAOpC,IAAA,CAAKQ,KAAK;QACtB,KAAKb,MAAA,GAAS2B,IAAA,CAAKgB,GAAA,CAAI9B,KAAA,CAAMb,MAAA,EAAQ,KAAKA,MAAM;MACjD;MAED,KAAK6B,OAAA,GAAU,UAAUhE,IAAA,EAAM;QAC7B,KAAKA,IAAA,GAAOA,IAAA;QAEZ,SAAS4C,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKgC,MAAA,CAAOzC,MAAA,EAAQS,CAAA,IAAK,KAAKgC,MAAA,CAAOhC,CAAC,EAAEoB,OAAA,CAAQhE,IAAI;MACzE;MAED,KAAKE,KAAA,GAAQ,UAAUgC,MAAA,EAAQyB,WAAA,EAAa;QAC1C,IAAI,CAACA,WAAA,EAAaA,WAAA,GAAc9D,SAAA,CAAU+D,0BAAA;QAC1C,IAAInD,CAAA,GAAI,IAAIZ,SAAA,CAAU8E,SAAA,CAAW;QACjClE,CAAA,CAAEyB,MAAA,GAASA,MAAA;QACX,SAASU,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKgC,MAAA,CAAOzC,MAAA,EAAQS,CAAA,IAAK;UAC3C,IAAII,KAAA,GAAQ,KAAK4B,MAAA,CAAOhC,CAAC,EAAE1C,KAAA,CAAO;UAClC8C,KAAA,CAAMS,QAAA,CAASvB,MAAA,EAAQyB,WAAW;UAClClD,CAAA,CAAEoE,QAAA,CAAS7B,KAAK;QACjB;QAED,OAAOvC,CAAA;MACR;IACF;IAED,IAAIsE,qBAAA,GAAwB;IAC5B,IAAIC,oBAAA,GAAuB;IAC3B,IAAIC,sBAAA,GAAyB;IAC7B,IAAIC,mBAAA,GAAsB;IAC1B,IAAIC,uBAAA,GAA0B;IAC9B,IAAIC,oBAAA,GAAuB;IAC3B,IAAIC,mBAAA,GAAsB;IAC1B,IAAIC,wBAAA,GAA2B;IAC/B,IAAIC,mBAAA,GAAsB;IAC1B,IAAIC,uBAAA,GAA0B;IAC9B,IAAIC,+BAAA,GAAkC;IACtC,IAAIC,yBAAA,GAA4B;IAChC,IAAIC,uBAAA,GAA0B;IAC9B,IAAIC,uCAAA,GAA0C;IAC9C,IAAIC,6BAAA,GAAgC;IACpC,IAAIC,0BAAA,GAA6B;IACjC,IAAIC,2BAAA,GAA8B;IAClC,IAAIC,8BAAA,GAAiC;IAEzC;IACA;IACA;IACI,IAAIC,yBAAA,GAA4B;IACpC;IACA;IACA;IAEA;IACA;IACA;IACA;IACI,IAAIC,kBAAA,GAAqB;IAC7B;IACA;IACA;IACA;IACA;IA+CI,IAAIC,qBAAA,GAAwB;IAyB5B,IAAIC,qBAAA,GAAwB;IAc5B,IAAIC,qBAAA,GAAwB;IAc5B,IAAIC,sBAAA,GAAyB;IAc7B,IAAIC,YAAA,GAAe;IAEnB,SAASC,yBAAyB/F,CAAA,EAAG;MACnC,OAAOoF,6BAAA,IAAiCpF,CAAA;IACzC;IAED,SAASgG,sBAAsBhG,CAAA,EAAG;MAChC,OAAOqF,0BAAA,IAA8BrF,CAAA;IACtC;IAED,SAASiG,UAAUC,KAAA,EAAO;MACxB,SAAS/D,CAAA,IAAK+D,KAAA,CAAMC,OAAA,EAAS;QAC3B,IAAIC,IAAA,GAAOF,KAAA,CAAMC,OAAA,CAAQhE,CAAC;QAC1B,SAASkE,CAAA,IAAKD,IAAA,CAAKE,MAAA,EAAQ;UACzB,IAAIC,QAAA,GAAWL,KAAA,CAAMM,QAAA,CAASJ,IAAA,CAAKE,MAAA,CAAOD,CAAC,EAAEI,KAAK;UAClD,IAAIF,QAAA,EAAUA,QAAA,CAASG,MAAA,GAAS;QACjC;MACF;IACF;IAED,SAASC,iBAAiB1D,IAAA,EAAMiD,KAAA,EAAO;MACrC,IAAIU,QAAA,GAAW,IAAIC,IAAA,CAAM;MACzBD,QAAA,CAASpH,MAAA,CAAOoE,IAAA,CAAKX,IAAA,CAAKzD,MAAM;MAChCoH,QAAA,CAASE,WAAA,CAAYlD,IAAA,CAAKX,IAAA,CAAK6D,WAAW;MAC1CF,QAAA,CAASlH,QAAA,CAASkE,IAAA,CAAKX,IAAA,CAAKvD,QAAQ;MACpCkH,QAAA,CAAShH,UAAA,CAAWgE,IAAA,CAAKX,IAAA,CAAKrD,UAAU;MACxCgH,QAAA,CAAS9G,KAAA,CAAM8D,IAAA,CAAKX,IAAA,CAAKnD,KAAK;MAC9BoG,KAAA,CAAMa,SAAA;MACNH,QAAA,CAAS7C,IAAA,GAAO,UAAUd,IAAA,CAAKc,IAAA,GAAOmC,KAAA,CAAMa,SAAA,CAAUC,QAAA,CAAU;MAEhE,IAAI,CAACd,KAAA,CAAMe,aAAA,CAAchE,IAAA,CAAKc,IAAI,GAAGmC,KAAA,CAAMe,aAAA,CAAchE,IAAA,CAAKc,IAAI,IAAI,EAAE;MACxEmC,KAAA,CAAMe,aAAA,CAAchE,IAAA,CAAKc,IAAI,EAAEhC,IAAA,CAAK6E,QAAQ;MAC5C,SAASzE,CAAA,IAAKc,IAAA,CAAKe,QAAA,EAAU;QAC3B,IAAIkD,KAAA,GAAQP,gBAAA,CAAiB1D,IAAA,CAAKe,QAAA,CAAS7B,CAAC,GAAG+D,KAAK;QACpDU,QAAA,CAASO,GAAA,CAAID,KAAK;MACnB;MAED,OAAON,QAAA;IACR;IAED,SAASQ,YAAYC,OAAA,EAASC,OAAA,EAAS;MACrC,IAAIC,KAAA,GAAQ,EAAE;MAEd,SAASpF,CAAA,GAAI,GAAGA,CAAA,GAAIkF,OAAA,CAAQ3F,MAAA,EAAQS,CAAA,IAAK;QACvCoF,KAAA,CAAMxF,IAAA,CAAK;UACTI,CAAA,EAAGkF,OAAA,CAAQlF,CAAC;UACZlB,CAAA,EAAGqG,OAAA,CAAQnF,CAAC;QACtB,CAAS;MACF;MAEDoF,KAAA,CAAM5E,IAAA,CAAK,UAAUE,CAAA,EAAGC,CAAA,EAAG;QACzB,OAAOA,CAAA,CAAE7B,CAAA,GAAI4B,CAAA,CAAE5B,CAAA;MACvB,CAAO;MAED,OAAOsG,KAAA,CAAM7F,MAAA,GAAS,GAAG;QACvB6F,KAAA,CAAMxF,IAAA,CAAK;UACTI,CAAA,EAAG;UACHlB,CAAA,EAAG;QACb,CAAS;MACF;MAED,IAAIsG,KAAA,CAAM7F,MAAA,GAAS,GAAG6F,KAAA,CAAM7F,MAAA,GAAS;MACrC,IAAI8F,GAAA,GAAM;MAEV,SAASrF,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1BqF,GAAA,IAAOD,KAAA,CAAMpF,CAAC,EAAElB,CAAA,GAAIsG,KAAA,CAAMpF,CAAC,EAAElB,CAAA;MAC9B;MAEDuG,GAAA,GAAMnE,IAAA,CAAKoE,IAAA,CAAKD,GAAG;MAEnB,SAASrF,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;QAC1BoF,KAAA,CAAMpF,CAAC,EAAElB,CAAA,GAAIsG,KAAA,CAAMpF,CAAC,EAAElB,CAAA,GAAIuG,GAAA;QAC1BH,OAAA,CAAQlF,CAAC,IAAIoF,KAAA,CAAMpF,CAAC,EAAEA,CAAA;QACtBmF,OAAA,CAAQnF,CAAC,IAAIoF,KAAA,CAAMpF,CAAC,EAAElB,CAAA;MACvB;IACF;IAED,SAASyG,iBAAiBzE,IAAA,EAAMc,IAAA,EAAM;MACpC,IAAId,IAAA,CAAKc,IAAA,CAAK4D,OAAA,CAAQ,UAAU5D,IAAI,KAAK,GAAG,OAAOd,IAAA;MAEnD,SAASd,CAAA,IAAKc,IAAA,CAAKe,QAAA,EAAU;QAC3B,IAAI4D,GAAA,GAAMF,gBAAA,CAAiBzE,IAAA,CAAKe,QAAA,CAAS7B,CAAC,GAAG4B,IAAI;QAEjD,IAAI6D,GAAA,EAAK,OAAOA,GAAA;MACjB;MAED,OAAO;IACR;IAED,MAAMC,MAAA,CAAO;MACXvI,YAAA,EAAc;QACZ,KAAKwI,eAAA,GAAkB;QACvB,KAAKC,YAAA,GAAe;QACpB,KAAKC,SAAA,GAAY;QACjB,KAAKC,SAAA,GAAY;QACjB,KAAKC,cAAA,GAAiB;QACtB,KAAKC,SAAA,GAAY,EAAE;QACnB,KAAKC,QAAA,GAAW,EAAE;QAClB,KAAKC,SAAA,GAAY,EAAE;QACnB,KAAKC,WAAA,GAAc,EAAE;QACrB,KAAKC,OAAA,GAAU,CAAC,EAAE;QAClB,KAAKC,cAAA,GAAiB,CAAC,EAAE;QACzB,KAAKC,MAAA,GAAS,EAAE;QAChB,KAAKnC,MAAA,GAAS,EAAE;QAChB,KAAKoC,eAAA,GAAkB,UAAUxC,KAAA,EAAO;UACtC,IAAI,KAAKI,MAAA,CAAO5E,MAAA,IAAU,GAAG;UAE7B,IAAIiH,QAAA,GAAW,EAAE;UACjB,IAAIC,YAAA,GAAe,EAAE;UACrB,IAAIC,YAAA,GAAe3C,KAAA,CAAMM,QAAA,CAAS,KAAKF,MAAA,CAAO,CAAC,EAAEG,KAAK;UAEtD,OAAOoC,YAAA,CAAaC,OAAA,IAAWD,YAAA,CAAaC,OAAA,CAAQpC,MAAA,EAAQ;YAC1DmC,YAAA,GAAeA,YAAA,CAAaC,OAAA;UAC7B;UAED,IAAIC,iBAAA,GAAoBF,YAAA,CAAaG,OAAA,CAAQ9C,KAAK;UAClD,IAAI+C,qBAAA,GAAwBtC,gBAAA,CAAiBoC,iBAAA,EAAmB7C,KAAK;UACrE,KAAKgD,SAAA,CAAU/B,GAAA,CAAI8B,qBAAqB;UAExC,SAAS9G,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKmE,MAAA,CAAO5E,MAAA,EAAQS,CAAA,IAAK;YAC3C,IAAIgH,IAAA,GAAOzB,gBAAA,CAAiBuB,qBAAA,EAAuB,KAAK3C,MAAA,CAAOnE,CAAC,EAAEsE,KAAK;YAEvE,IAAI0C,IAAA,EAAM;cACR,IAAIC,KAAA,GAAQD,IAAA;cACZR,QAAA,CAAS5G,IAAA,CAAKqH,KAAK;cAEnBR,YAAA,CAAa7G,IAAA,CAAK,KAAKuE,MAAA,CAAOnE,CAAC,EAAEkH,aAAA,CAAcL,OAAA,EAAS;YACtE,OAAmB;cACL,IAAIH,YAAA,GAAe3C,KAAA,CAAMM,QAAA,CAAS,KAAKF,MAAA,CAAOnE,CAAC,EAAEsE,KAAK;cACtD,IAAI,CAACoC,YAAA,EAAc;cACnB,IAAIE,iBAAA,GAAoBF,YAAA,CAAaG,OAAA,CAAQ9C,KAAK;cAClD,IAAI+C,qBAAA,GAAwBtC,gBAAA,CAAiBoC,iBAAA,EAAmB7C,KAAK;cACrE,KAAKgD,SAAA,CAAU/B,GAAA,CAAI8B,qBAAqB;cACxC,IAAIE,IAAA,GAAOzB,gBAAA,CAAiBuB,qBAAA,EAAuB,KAAK3C,MAAA,CAAOnE,CAAC,EAAEsE,KAAK;cACvE,IAAI2C,KAAA,GAAQD,IAAA;cACZR,QAAA,CAAS5G,IAAA,CAAKqH,KAAK;cAEnBR,YAAA,CAAa7G,IAAA,CAAK,KAAKuE,MAAA,CAAOnE,CAAC,EAAEkH,aAAA,CAAcL,OAAA,EAAS;YACzD;UACF;UAED,IAAIM,QAAA,GAAW,IAAIC,QAAA,CAASZ,QAAA,EAAUC,YAAY;UAElD,KAAKM,SAAA,CAAUM,IAAA,CAAKF,QAAA,EAAU,IAAIhI,OAAA,CAAO,CAAE;UAC3C,KAAK4H,SAAA,CAAUO,QAAA,CAASC,QAAA,GAAW;QACpC;QAED,KAAKV,OAAA,GAAU,UAAU9C,KAAA,EAAO;UAC9B,IAAI,KAAKgD,SAAA,EAAW,OAAO,KAAKA,SAAA;UAChC,IAAIS,QAAA,GAAW,IAAIC,cAAA,CAAgB;UACnC,IAAIC,GAAA;UACJ,IAAI3D,KAAA,CAAM4D,UAAA,CAAW,KAAK5B,cAAc,GAAG2B,GAAA,GAAM3D,KAAA,CAAM4D,UAAA,CAAW,KAAK5B,cAAc,EAAEc,OAAA,CAAQ9C,KAAK,OAC/F2D,GAAA,GAAM,IAAIE,mBAAA,CAAqB;UACpCJ,QAAA,CAASK,QAAA,CAAS,IAAIC,eAAA,CAAgB,IAAIC,WAAA,CAAY,KAAKC,WAAW,GAAG,CAAC,CAAC;UAC3ER,QAAA,CAASS,YAAA,CAAa,YAAY,IAAIH,eAAA,CAAgB,KAAKI,aAAA,EAAe,CAAC,CAAC;UAC5E,IAAI,KAAKC,aAAA,IAAiB,KAAKA,aAAA,CAAc5I,MAAA,GAAS,GAAG;YACvDiI,QAAA,CAASS,YAAA,CAAa,UAAU,IAAIH,eAAA,CAAgB,KAAKK,aAAA,EAAe,CAAC,CAAC;UAC3E;UACD,IAAI,KAAKC,YAAA,IAAgB,KAAKA,YAAA,CAAa7I,MAAA,GAAS,GAAG;YACrDiI,QAAA,CAASS,YAAA,CAAa,SAAS,IAAIH,eAAA,CAAgB,KAAKM,YAAA,EAAc,CAAC,CAAC;UACzE;UACD,IAAI,KAAKC,iBAAA,CAAkB,CAAC,KAAK,KAAKA,iBAAA,CAAkB,CAAC,EAAE9I,MAAA,GAAS,GAAG;YACrEiI,QAAA,CAASS,YAAA,CAAa,MAAM,IAAIH,eAAA,CAAgB,IAAIQ,YAAA,CAAa,KAAKD,iBAAA,CAAkB,CAAC,CAAC,GAAG,CAAC,CAAC;UAChG;UACD,IAAI,KAAKA,iBAAA,CAAkB,CAAC,KAAK,KAAKA,iBAAA,CAAkB,CAAC,EAAE9I,MAAA,GAAS,GAAG;YACrEiI,QAAA,CAASS,YAAA,CAAa,OAAO,IAAIH,eAAA,CAAgB,IAAIQ,YAAA,CAAa,KAAKD,iBAAA,CAAkB,CAAC,CAAC,GAAG,CAAC,CAAC;UACjG;UACD,IAAI,KAAKE,cAAA,IAAkB,KAAKA,cAAA,CAAehJ,MAAA,GAAS,GAAG;YACzDiI,QAAA,CAASS,YAAA,CAAa,YAAY,IAAIH,eAAA,CAAgB,KAAKS,cAAA,EAAgB,CAAC,CAAC;UAC9E;UACD,IAAI,KAAKC,gBAAA,IAAoB,KAAKA,gBAAA,CAAiBjJ,MAAA,GAAS,GAAG;YAC7DiI,QAAA,CAASS,YAAA,CAAa,cAAc,IAAIH,eAAA,CAAgB,KAAKU,gBAAA,EAAkB,CAAC,CAAC;UAClF;UACD,IAAI,KAAKrE,MAAA,CAAO5E,MAAA,GAAS,GAAG;YAC1B,IAAI4F,OAAA,GAAU,EAAE;YAChB,IAAIsD,KAAA,GAAQ,EAAE;YAEd,SAASzI,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKmE,MAAA,CAAO5E,MAAA,EAAQS,CAAA,IAAK;cAC3C,SAASD,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKoE,MAAA,CAAOnE,CAAC,EAAE0I,QAAA,CAASnJ,MAAA,EAAQQ,CAAA,IAAK;gBACvD,IAAI4I,MAAA,GAAS,KAAKxE,MAAA,CAAOnE,CAAC,EAAE0I,QAAA,CAAS3I,CAAC;gBACtC,IAAI4I,MAAA,EAAQ;kBACV,IAAI,CAACxD,OAAA,CAAQwD,MAAA,CAAOC,SAAS,GAAGzD,OAAA,CAAQwD,MAAA,CAAOC,SAAS,IAAI,EAAE;kBAC9D,IAAI,CAACH,KAAA,CAAME,MAAA,CAAOC,SAAS,GAAGH,KAAA,CAAME,MAAA,CAAOC,SAAS,IAAI,EAAE;kBAC1DzD,OAAA,CAAQwD,MAAA,CAAOC,SAAS,EAAEhJ,IAAA,CAAK+I,MAAA,CAAOE,OAAO;kBAC7CJ,KAAA,CAAME,MAAA,CAAOC,SAAS,EAAEhJ,IAAA,CAAKkJ,QAAA,CAAS9I,CAAC,CAAC;gBACzC;cACF;YACF;YAED,SAASA,CAAA,IAAKyI,KAAA,EAAO;cACnBxD,WAAA,CAAYwD,KAAA,CAAMzI,CAAC,GAAGmF,OAAA,CAAQnF,CAAC,CAAC;YACjC;YAED,IAAI+I,QAAA,GAAW,EAAE;YACjB,IAAIC,MAAA,GAAS,EAAE;YAEf,SAAShJ,CAAA,GAAI,GAAGA,CAAA,GAAImF,OAAA,CAAQ5F,MAAA,EAAQS,CAAA,IAAK;cACvC,SAASD,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;gBAC1B,IAAIoF,OAAA,CAAQnF,CAAC,KAAKyI,KAAA,CAAMzI,CAAC,GAAG;kBAC1B+I,QAAA,CAASnJ,IAAA,CAAKuF,OAAA,CAAQnF,CAAC,EAAED,CAAC,CAAC;kBAC3BiJ,MAAA,CAAOpJ,IAAA,CAAK6I,KAAA,CAAMzI,CAAC,EAAED,CAAC,CAAC;gBACzC,OAAuB;kBACLgJ,QAAA,CAASnJ,IAAA,CAAK,CAAC;kBACfoJ,MAAA,CAAOpJ,IAAA,CAAK,CAAC;gBACd;cACF;YACF;YAED4H,QAAA,CAASS,YAAA,CAAa,cAAc,IAAIH,eAAA,CAAgB,IAAIQ,YAAA,CAAaS,QAAQ,GAAGpF,YAAY,CAAC;YACjG6D,QAAA,CAASS,YAAA,CAAa,aAAa,IAAIH,eAAA,CAAgB,IAAIQ,YAAA,CAAaU,MAAM,GAAGrF,YAAY,CAAC;UAC/F;UAED,IAAIM,IAAA;UAEJ,IAAI,KAAKE,MAAA,CAAO5E,MAAA,IAAU,GAAG0E,IAAA,GAAO,IAAIgF,IAAA,CAAKzB,QAAA,EAAUE,GAAG;UAE1D,IAAI,KAAKvD,MAAA,CAAO5E,MAAA,GAAS,GAAG;YAC1B0E,IAAA,GAAO,IAAIiF,WAAA,CAAY1B,QAAA,EAAUE,GAAG;YACpCzD,IAAA,CAAKkF,oBAAA,CAAsB;UAC5B;UAED,KAAKpC,SAAA,GAAY9C,IAAA;UAEjB,OAAOA,IAAA;QACR;MACF;IACF;IAED,MAAMmF,MAAA,CAAO;MACXjM,YAAA,EAAc;QACZ,KAAKkM,WAAA,GAAc;QACnB,KAAKC,QAAA,GAAW,EAAE;MACnB;IACF;IAED,MAAMC,UAAA,CAAW;MACfpM,YAAA,EAAc;QACZ,KAAKsB,CAAA,GAAI;QACT,KAAKC,CAAA,GAAI;QACT,KAAKC,CAAA,GAAI;QAET,KAAKkI,OAAA,GAAU,YAAY;UACzB,OAAO,IAAIrJ,OAAA,CAAQ,KAAKiB,CAAA,EAAG,KAAKC,CAAA,EAAG,KAAKC,CAAC;QAC1C;MACF;IACF;IAED,MAAM6K,SAAA,CAAU;MACdrM,YAAA,EAAc;QACZ,KAAK2E,CAAA,GAAI;QACT,KAAK2H,CAAA,GAAI;QACT,KAAK9I,CAAA,GAAI;QACT,KAAKD,CAAA,GAAI;QACT,KAAKmG,OAAA,GAAU,YAAY;UACzB,OAAO,IAAI6C,KAAA,CAAM,KAAK5H,CAAA,EAAG,KAAK2H,CAAA,EAAG,KAAK9I,CAAC;QACxC;MACF;IACF;IAED,MAAMgJ,YAAA,CAAa;MACjBxM,YAAA,EAAc;QACZ,KAAKsB,CAAA,GAAI;QACT,KAAKC,CAAA,GAAI;QACT,KAAKC,CAAA,GAAI;QACT,KAAKG,CAAA,GAAI;QACT,KAAK+H,OAAA,GAAU,YAAY;UACzB,OAAO,IAAInJ,UAAA,CAAW,KAAKe,CAAA,EAAG,KAAKC,CAAA,EAAG,KAAKC,CAAA,EAAG,KAAKG,CAAC;QACrD;MACF;IACF;IAED,MAAM8K,cAAA,CAAe;MACnBzM,YAAA,EAAc;QACZ,KAAKyL,SAAA,GAAY;QACjB,KAAKC,OAAA,GAAU;MAChB;IACF;IAED,MAAMgB,QAAA,CAAS;MACb1M,YAAA,EAAc;QACZ,KAAK+C,IAAA,GAAO,EAAE;QACd,KAAK2E,QAAA,GAAW,YAAY;UAC1B,IAAIiF,GAAA,GAAM;UACV,KAAK5J,IAAA,CAAK6J,OAAA,CAAQ,UAAU/J,CAAA,EAAG;YAC7B8J,GAAA,IAAOE,MAAA,CAAOC,YAAA,CAAajK,CAAC;UACxC,CAAW;UACD,OAAO8J,GAAA,CAAII,OAAA,CAAQ,kBAAkB,EAAE;QACxC;MACF;IACF;IAED,MAAMC,WAAA,CAAY;MAChBhN,YAAA,EAAc;QACZ,KAAKiN,KAAA,GAAQ;QACb,KAAKC,MAAA,GAAS;MACf;IACF;IAED,MAAMC,SAAA,CAAU;MACdnN,YAAA,EAAc;QACZ,KAAKiN,KAAA,GAAQ;QACb,KAAKC,MAAA,GAAS;MACf;IACF;IAED,MAAME,MAAA,CAAO;MACXpN,YAAA,EAAc;QACZ,KAAKmH,KAAA,GAAQ;QACb,KAAKkG,eAAA,GAAkB,EAAE;QACzB,KAAKC,YAAA,GAAe;QACpB,KAAKC,UAAA,GAAa;QAClB,KAAK1G,OAAA,GAAU,EAAE;QACjB,KAAK2G,SAAA,GAAY,EAAE;QACnB,KAAK9D,OAAA,GAAU,UAAU9C,KAAA,EAAO;UAC9B,IAAI,KAAKgD,SAAA,EAAW,OAAO,KAAKA,SAAA;UAChC,IAAI6D,CAAA,GAAI,IAAIC,QAAA,CAAU;UACtBD,CAAA,CAAEhJ,IAAA,GAAO,KAAK0C,KAAA;UACdsG,CAAA,CAAEvN,MAAA,GAAS,KAAKmN,eAAA,CAAgB3D,OAAA,CAAS;UAEzC,SAAS7G,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK2K,SAAA,CAAUpL,MAAA,EAAQS,CAAA,IAAK;YAC9C4K,CAAA,CAAE5F,GAAA,CAAI,KAAK2F,SAAA,CAAU3K,CAAC,EAAE6G,OAAA,CAAQ9C,KAAK,CAAC;UACvC;UAED,SAAS/D,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAKgE,OAAA,CAAQzE,MAAA,EAAQS,CAAA,IAAK;YAC5C4K,CAAA,CAAE5F,GAAA,CAAIjB,KAAA,CAAMC,OAAA,CAAQ,KAAKA,OAAA,CAAQhE,CAAC,CAAC,EAAE6G,OAAA,CAAQ9C,KAAK,CAAC;UACpD;UAED,KAAKgD,SAAA,GAAY6D,CAAA;UAEjBA,CAAA,CAAEvN,MAAA,CAAOO,SAAA,CAAUgN,CAAA,CAAErN,QAAA,EAAUqN,CAAA,CAAEnN,UAAA,EAAYmN,CAAA,CAAEjN,KAAK;UACpD,OAAOiN,CAAA;QACR;MACF;IACF;IAED,MAAME,MAAA,CAAO;MACX3N,YAAA,EAAc;QACZ,KAAKmH,KAAA,GAAQ;QACb,KAAKyG,WAAA,GAAc;QACnB,KAAK7D,aAAA,GAAgB;MACtB;IACF;IAED,MAAM8D,kBAAA,CAAmB;MACvB7N,YAAA,EAAc;QACZ,KAAK8N,IAAA,GAAO;QACZ,KAAKC,SAAA,GAAY;QACjB,KAAKC,MAAA,GAAS;QACd,KAAKC,KAAA,GAAQ,EAAE;QACf,KAAKC,WAAA,GAAc;QACnB,KAAKC,KAAA,GAAQ;QACb,KAAKC,WAAA,GAAc,YAAY;UAC7B,IAAIC,KAAA,GAAQ,IAAIC,UAAA,CAAW,KAAKL,KAAK,EAAE9O,MAAA;UACvC,IAAIoP,MAAA,GAAS,IAAIC,QAAA,CAASH,KAAK;UAC/B,IAAI1J,CAAA,GAAI4J,MAAA,CAAOE,UAAA,CAAW,GAAG,IAAI;UACjC,IAAInC,CAAA,GAAIiC,MAAA,CAAOE,UAAA,CAAW,GAAG,IAAI;UACjC,IAAIjL,CAAA,GAAI+K,MAAA,CAAOE,UAAA,CAAW,GAAG,IAAI;UAEjC,OAAO,IAAIlC,KAAA,CAAM5H,CAAA,EAAG2H,CAAA,EAAG9I,CAAC;QACzB;QAED,KAAKkL,WAAA,GAAc,YAAY;UAC7B,IAAIL,KAAA,GAAQ,IAAIC,UAAA,CAAW,KAAKL,KAAK,EAAE9O,MAAA;UACvC,IAAIoP,MAAA,GAAS,IAAIC,QAAA,CAASH,KAAK;UAC/B,IAAI1J,CAAA,GAAI4J,MAAA,CAAOE,UAAA,CAAW,GAAG,IAAI;UACjC,OAAO9J,CAAA;QACR;QAED,KAAKgK,UAAA,GAAa,YAAY;UAC5B,IAAIN,KAAA,GAAQ,IAAIC,UAAA,CAAW,KAAKL,KAAK,EAAE9O,MAAA;UACvC,IAAIoP,MAAA,GAAS,IAAIC,QAAA,CAASH,KAAK;UAC/B,IAAI1J,CAAA,GAAI4J,MAAA,CAAOE,UAAA,CAAW,GAAG,IAAI;UACjC,OAAO,CAAC,CAAC9J,CAAA;QACV;QAED,KAAKiK,YAAA,GAAe,YAAY;UAC9B,IAAIC,CAAA,GAAI,IAAInC,QAAA,CAAU;UACtBmC,CAAA,CAAE9L,IAAA,GAAO,KAAKkL,KAAA;UACd,OAAOY,CAAA,CAAEnH,QAAA,CAAU;QACpB;QAED,KAAKoH,SAAA,GAAY,YAAY;UAC3B,IAAID,CAAA,GAAI,IAAInC,QAAA,CAAU;UACtBmC,CAAA,CAAE9L,IAAA,GAAO,KAAKkL,KAAA;UACd,IAAIc,KAAA,GAAOF,CAAA,CAAEnH,QAAA,CAAU;UACvBqH,KAAA,GAAOA,KAAA,CAAKhC,OAAA,CAAQ,OAAO,GAAG;UAE9B,IAAIgC,KAAA,CAAK1G,OAAA,CAAQ,GAAG,KAAK,IAAI;YAC3B0G,KAAA,GAAOA,KAAA,CAAKC,MAAA,CAAOD,KAAA,CAAKE,WAAA,CAAY,GAAG,IAAI,CAAC;UAC7C;UAED,OAAOxP,aAAA,CAAcxB,IAAA,CAAK8Q,KAAI;QAC/B;MACF;IACF;IAED,IAAIG,eAAA,GAAkB;MACpB,aAAa;MACb,iBAAiB;MACjB,iBAAiB;MACjB,kBAAkB;MAClB,gBAAgB;MAChB,gBAAgB;MAChB,iBAAiB;MACjB,iBAAiB;MACjB,oBAAoB;MACpB,mBAAmB;MACnB,kBAAkB;MAClB,qBAAqB;MACrB,iBAAiB;MACjB,aAAa;IACd;IAED,IAAIC,eAAA,GAAkB;MACpB,aAAa;MACb,iBAAiB;MACjB,iBAAiB;MACjB,kBAAkB;MAClB,gBAAgB;MAChB,gBAAgB;MAChB,iBAAiB;MACjB,iBAAiB;MACjB,oBAAoB;MACpB,mBAAmB;MACnB,kBAAkB;MAClB,qBAAqB;MACrB,iBAAiB;MACjB,aAAa;IACd;IAED,MAAMC,UAAA,CAAW;MACfpP,YAAA,EAAc;QACZ,KAAKqP,aAAA,GAAgB;QACrB,KAAKC,cAAA,GAAiB;QACtB,KAAKC,WAAA,GAAc,EAAE;QACrB,KAAK7F,OAAA,GAAU,YAAY;UACzB,IAAIa,GAAA,GAAM,IAAIiF,iBAAA,CAAmB;UAEjC,SAAS3M,CAAA,GAAI,GAAGA,CAAA,GAAI,KAAK0M,WAAA,CAAYnN,MAAA,EAAQS,CAAA,IAAK;YAChD,IAAIsM,eAAA,CAAgB,KAAKI,WAAA,CAAY1M,CAAC,EAAEiL,IAAI,KAAK,SAAS;cACxDvD,GAAA,CAAI2E,eAAA,CAAgB,KAAKK,WAAA,CAAY1M,CAAC,EAAEiL,IAAI,CAAC,IAAI,KAAKyB,WAAA,CAAY1M,CAAC,EAAE6L,WAAA,CAAa;YACnF;YACD,IAAIS,eAAA,CAAgB,KAAKI,WAAA,CAAY1M,CAAC,EAAEiL,IAAI,KAAK,SAAS;cACxDvD,GAAA,CAAI2E,eAAA,CAAgB,KAAKK,WAAA,CAAY1M,CAAC,EAAEiL,IAAI,CAAC,IAAI,KAAKyB,WAAA,CAAY1M,CAAC,EAAEuL,WAAA,CAAa;YACnF;YACD,IAAIe,eAAA,CAAgB,KAAKI,WAAA,CAAY1M,CAAC,EAAEiL,IAAI,KAAK,QAAQ;cACvDvD,GAAA,CAAI2E,eAAA,CAAgB,KAAKK,WAAA,CAAY1M,CAAC,EAAEiL,IAAI,CAAC,IAAI,KAAKyB,WAAA,CAAY1M,CAAC,EAAE8L,UAAA,CAAY;YAClF;YACD,IAAIQ,eAAA,CAAgB,KAAKI,WAAA,CAAY1M,CAAC,EAAEiL,IAAI,KAAK,UAAU;cACzDvD,GAAA,CAAI2E,eAAA,CAAgB,KAAKK,WAAA,CAAY1M,CAAC,EAAEiL,IAAI,CAAC,IAAI,KAAKyB,WAAA,CAAY1M,CAAC,EAAE+L,YAAA,CAAc;YACpF;YACD,IAAIO,eAAA,CAAgB,KAAKI,WAAA,CAAY1M,CAAC,EAAEiL,IAAI,KAAK,OAAO;cACtD,IAAI2B,IAAA,GAAO,KAAKF,WAAA,CAAY1M,CAAC;cAC7B,IAAI4M,IAAA,CAAK1B,SAAA,IAAa3H,qBAAA,EAAuBmE,GAAA,CAAImF,GAAA,GAAM,KAAKH,WAAA,CAAY1M,CAAC,EAAEiM,SAAA,CAAW;cACtF,IAAIW,IAAA,CAAK1B,SAAA,IAAa1H,qBAAA,EAAuBkE,GAAA,CAAIoF,SAAA,GAAY,KAAKJ,WAAA,CAAY1M,CAAC,EAAEiM,SAAA,CAAW;cAC5F,IAAIW,IAAA,CAAK1B,SAAA,IAAaxH,sBAAA,EAAwBgE,GAAA,CAAIqF,QAAA,GAAW,KAAKL,WAAA,CAAY1M,CAAC,EAAEiM,SAAA,CAAW;cAC5F,IAAIW,IAAA,CAAK1B,SAAA,IAAazH,qBAAA,EAAuBiE,GAAA,CAAIsF,QAAA,GAAW,KAAKN,WAAA,CAAY1M,CAAC,EAAEiM,SAAA,CAAW;YAC5F;UACF;UAEDvE,GAAA,CAAIuF,OAAA,CAAQnL,CAAA,GAAI;UAChB4F,GAAA,CAAIuF,OAAA,CAAQxD,CAAA,GAAI;UAChB/B,GAAA,CAAIuF,OAAA,CAAQtM,CAAA,GAAI;UAChB+G,GAAA,CAAIwF,KAAA,CAAMpL,CAAA,GAAI;UACd4F,GAAA,CAAIwF,KAAA,CAAMzD,CAAA,GAAI;UACd/B,GAAA,CAAIwF,KAAA,CAAMvM,CAAA,GAAI;UACd,OAAO+G,GAAA;QACR;MACF;IACF;IAED,SAASyF,QAAQC,EAAA,EAAIC,EAAA,EAAInP,CAAA,EAAG;MAC1B,IAAIoP,CAAA,GAAI,IAAI9P,OAAA,CAAS;MACrB,IAAI+P,GAAA,GAAM,IAAIrP,CAAA;MACdoP,CAAA,CAAE7O,CAAA,GAAI2O,EAAA,CAAG3O,CAAA,GAAIP,CAAA,GAAImP,EAAA,CAAG5O,CAAA,GAAI8O,GAAA;MACxBD,CAAA,CAAE5O,CAAA,GAAI0O,EAAA,CAAG1O,CAAA,GAAIR,CAAA,GAAImP,EAAA,CAAG3O,CAAA,GAAI6O,GAAA;MACxBD,CAAA,CAAE3O,CAAA,GAAIyO,EAAA,CAAGzO,CAAA,GAAIT,CAAA,GAAImP,EAAA,CAAG1O,CAAA,GAAI4O,GAAA;MACxB,OAAOD,CAAA;IACR;IAED,SAASE,SAASC,EAAA,EAAIC,EAAA,EAAIxP,CAAA,EAAG;MAC3B,OAAOuP,EAAA,CAAGnQ,KAAA,CAAO,EAACyB,KAAA,CAAM2O,EAAA,EAAI,IAAIxP,CAAC;IAClC;IAED,SAASyP,YAAYtO,IAAA,EAAMjC,IAAA,EAAMwQ,GAAA,EAAK9P,IAAA,EAAM;MAC1C,IAAIuB,IAAA,CAAKE,MAAA,IAAU,GAAG,OAAOF,IAAA,CAAK,CAAC,EAAEgL,MAAA,CAAOxD,OAAA,CAAS;MAErD,IAAI5I,IAAA,GAAO4P,QAAA;MACX,IAAIlO,GAAA,GAAM;MACV,IAAI5B,OAAA,GAAU;MAEd,SAASiC,CAAA,GAAI,GAAGA,CAAA,GAAIX,IAAA,CAAKE,MAAA,EAAQS,CAAA,IAAK;QACpC,IAAI8N,QAAA,GAAW5M,IAAA,CAAKG,GAAA,CAAIhC,IAAA,CAAKW,CAAC,EAAEoK,KAAA,GAAQhN,IAAI;QAE5C,IAAI0Q,QAAA,GAAW7P,IAAA,IAAQoB,IAAA,CAAKW,CAAC,EAAEoK,KAAA,IAAShN,IAAA,EAAM;UAC5Ca,IAAA,GAAO6P,QAAA;UACPnO,GAAA,GAAMN,IAAA,CAAKW,CAAC;UACZjC,OAAA,GAAUsB,IAAA,CAAKW,CAAA,GAAI,CAAC;QACrB;MACF;MAED,IAAI,CAACL,GAAA,EAAK;QACR,OAAO;MACR,WAAU5B,OAAA,EAAS;QAClB,IAAIgQ,EAAA,GAAKhQ,OAAA,CAAQqM,KAAA,GAAQzK,GAAA,CAAIyK,KAAA;QAC7B,IAAI4D,CAAA,GAAIrO,GAAA,CAAIyK,KAAA,GAAQhN,IAAA;QACpB,IAAIc,CAAA,GAAI8P,CAAA,GAAID,EAAA;QAEZ,OAAOjQ,IAAA,CAAK6B,GAAA,CAAI0K,MAAA,CAAOxD,OAAA,CAAO,GAAI9I,OAAA,CAAQsM,MAAA,CAAOxD,OAAA,CAAS,GAAE3I,CAAC;MACrE,OAAa;QACLH,OAAA,GAAUsB,IAAA,CAAK,CAAC,EAAE/B,KAAA,CAAO;QACzBS,OAAA,CAAQqM,KAAA,IAASwD,GAAA;QAEjB,IAAIG,EAAA,GAAKhQ,OAAA,CAAQqM,KAAA,GAAQzK,GAAA,CAAIyK,KAAA;QAC7B,IAAI4D,CAAA,GAAIrO,GAAA,CAAIyK,KAAA,GAAQhN,IAAA;QACpB,IAAIc,CAAA,GAAI8P,CAAA,GAAID,EAAA;QAEZ,OAAOjQ,IAAA,CAAK6B,GAAA,CAAI0K,MAAA,CAAOxD,OAAA,CAAO,GAAI9I,OAAA,CAAQsM,MAAA,CAAOxD,OAAA,CAAS,GAAE3I,CAAC;MAC9D;IACF;IAED,MAAM+P,UAAA,CAAW;MACf9Q,YAAA,EAAc;QACZ,KAAK+Q,SAAA,GAAY;QACjB,KAAKC,gBAAA,GAAmB;QACxB,KAAKC,gBAAA,GAAmB;QACxB,KAAKC,eAAA,GAAkB;QACvB,KAAKC,aAAA,GAAgB,EAAE;QACvB,KAAKC,aAAA,GAAgB,EAAE;QACvB,KAAKC,YAAA,GAAe,EAAE;QACtB,KAAKC,SAAA,GAAY;QACjB,KAAKC,UAAA,GAAa;QAClB,KAAK7O,IAAA,GAAO,UAAU8O,GAAA,EAAK;UACzB,IAAI,CAACA,GAAA,EAAKA,GAAA,GAAM;UAEhB,SAAS/N,EAAEgO,EAAA,EAAG;YACZA,EAAA,CAAExE,KAAA,IAASuE,GAAA;UACZ;UAED,KAAKL,aAAA,CAAcvE,OAAA,CAAQnJ,CAAC;UAC5B,KAAK2N,aAAA,CAAcxE,OAAA,CAAQnJ,CAAC;UAC5B,KAAK4N,YAAA,CAAazE,OAAA,CAAQnJ,CAAC;QAC5B;QAED,KAAKd,QAAA,GAAW,YAAY;UAC1B,SAAS+O,KAAKnO,CAAA,EAAGC,CAAA,EAAG;YAClB,OAAOD,CAAA,CAAE0J,KAAA,GAAQzJ,CAAA,CAAEyJ,KAAA;UACpB;UAED,KAAKkE,aAAA,CAAc9N,IAAA,CAAKqO,IAAI;UAC5B,KAAKN,aAAA,CAAc/N,IAAA,CAAKqO,IAAI;UAC5B,KAAKL,YAAA,CAAahO,IAAA,CAAKqO,IAAI;QAC5B;QAED,KAAKC,SAAA,GAAY,YAAY;UAC3B,OAAO5N,IAAA,CAAKgB,GAAA,CACVhB,IAAA,CAAKgB,GAAA,CAAI6M,KAAA,CACP,MACA,KAAKT,aAAA,CAAczB,GAAA,CAAI,UAAUnM,CAAA,EAAG;YAClC,OAAOA,CAAA,CAAE0J,KAAA;UACzB,CAAe,CACF,GACDlJ,IAAA,CAAKgB,GAAA,CAAI6M,KAAA,CACP,MACA,KAAKR,aAAA,CAAc1B,GAAA,CAAI,UAAUnM,CAAA,EAAG;YAClC,OAAOA,CAAA,CAAE0J,KAAA;UACzB,CAAe,CACF,GACDlJ,IAAA,CAAKgB,GAAA,CAAI6M,KAAA,CACP,MACA,KAAKP,YAAA,CAAa3B,GAAA,CAAI,UAAUnM,CAAA,EAAG;YACjC,OAAOA,CAAA,CAAE0J,KAAA;UACzB,CAAe,CACF,CACF;QACF;QAED,KAAKvD,OAAA,GAAU,UAAU+D,CAAA,EAAG;UAC1B,KAAK9K,QAAA,CAAU;UACf,IAAIP,MAAA,GAAS,KAAKuP,SAAA,CAAW;UAC7B,IAAI1O,KAAA,GAAQ,IAAInD,SAAA,CAAUmC,aAAA,CAAe;UAEzC,SAASY,CAAA,GAAI,GAAGA,CAAA,GAAIT,MAAA,EAAQS,CAAA,IAAK,MAAM;YACrC,IAAI3C,MAAA,GAAS,IAAI8B,OAAA,CAAS;YAC1B,IAAI/B,IAAA,GAAO4C,CAAA;YACX,IAAIgP,GAAA,GAAMrB,WAAA,CAAY,KAAKW,aAAA,EAAelR,IAAA,EAAMmC,MAAA,EAAQ4N,OAAO;YAC/D,IAAIxP,KAAA,GAAQgQ,WAAA,CAAY,KAAKa,YAAA,EAAcpR,IAAA,EAAMmC,MAAA,EAAQ4N,OAAO;YAChE,IAAI8B,QAAA,GAAWtB,WAAA,CAAY,KAAKY,aAAA,EAAenR,IAAA,EAAMmC,MAAA,EAAQiO,QAAQ;YACrEnQ,MAAA,CAAO4B,OAAA,CAAQ+P,GAAA,EAAKC,QAAA,EAAUtR,KAAK;YAEnC,IAAIgC,GAAA,GAAM,IAAI1C,SAAA,CAAUC,QAAA,CAASE,IAAA,EAAMC,MAAM;YAC7C+C,KAAA,CAAMV,MAAA,CAAOC,GAAG;UACjB;UAEDS,KAAA,CAAMd,MAAA,GAASsL,CAAA,CAAEvG,QAAA,CAAS,KAAK6J,SAAS,EAAErH,OAAA,CAAS;UAEnD,IAAI7E,MAAA,GAAS,CAAC5B,KAAK;UAEnB,IAAIwK,CAAA,CAAE9F,aAAA,CAAc,KAAKoJ,SAAS,GAAG;YACnC,SAASlO,CAAA,GAAI,GAAGA,CAAA,GAAI4K,CAAA,CAAE9F,aAAA,CAAc,KAAKoJ,SAAS,EAAE3O,MAAA,EAAQS,CAAA,IAAK;cAC/D,IAAI4O,EAAA,GAAKxO,KAAA,CAAM9C,KAAA,CAAO;cACtBsR,EAAA,CAAGtP,MAAA,GAASsL,CAAA,CAAE9F,aAAA,CAAc,KAAKoJ,SAAS,EAAElO,CAAC;cAC7CgC,MAAA,CAAOpC,IAAA,CAAKgP,EAAE;YACf;UACF;UAED,OAAO5M,MAAA;QACR;MACF;IACF;IAED,MAAMkN,WAAA,CAAY;MAChB/R,YAAA,EAAc;QACZ,KAAKmH,KAAA,GAAQ;QACb,KAAK6K,SAAA,GAAY;QACjB,KAAKC,eAAA,GAAkB;QACvB,KAAKC,YAAA,GAAe;QACpB,KAAKC,SAAA,GAAY,EAAE;QACnB,KAAKzI,OAAA,GAAU,UAAU/F,IAAA,EAAM;UAC7B,IAAIyO,eAAA,GAAkB,IAAItS,SAAA,CAAU8E,SAAA,CAAW;UAE/C,SAAS/B,CAAA,IAAK,KAAKsP,SAAA,EAAW;YAC5B,KAAKA,SAAA,CAAUtP,CAAC,EAAEH,IAAA,CAAK,KAAKuP,eAAe;YAE3C,IAAIpN,MAAA,GAAS,KAAKsN,SAAA,CAAUtP,CAAC,EAAE6G,OAAA,CAAQ/F,IAAI;YAE3C,SAASf,CAAA,IAAKiC,MAAA,EAAQ;cACpBA,MAAA,CAAOjC,CAAC,EAAEF,IAAA,CAAM;cAChB0P,eAAA,CAAgBtN,QAAA,CAASD,MAAA,CAAOjC,CAAC,CAAC;YACnC;UACF;UAEDwP,eAAA,CAAgBhQ,MAAA,GAAS2B,IAAA,CAAKgB,GAAA,CAAI6M,KAAA,CAChC,MACAQ,eAAA,CAAgBvN,MAAA,CAAO6K,GAAA,CAAI,UAAUrQ,CAAA,EAAG;YACtC,OAAOA,CAAA,CAAE+C,MAAA;UACvB,CAAa,CACF;UACD,OAAOgQ,eAAA;QACR;MACF;IACF;IAED,MAAMC,SAAA,CAAU;MACdrS,YAAA,EAAc;QACZ,KAAKsS,MAAA,GAAS;QACd,KAAKC,OAAA,GAAU;QACf,KAAKC,gBAAA,GAAmB,EAAE;QAC1B,KAAKC,MAAA,GAAS,EAAE;MACjB;IACF;IAED,MAAMC,OAAA,CAAQ;MACZ1S,YAAA,EAAc;QACZ,KAAKmH,KAAA,GAAQ;QACb,KAAKgH,KAAA,GAAQ;QACb,KAAKwE,oBAAA,GAAuB;QAC5B,KAAKC,kBAAA,GAAqB;QAC1B,KAAKC,qBAAA,GAAwB;QAC7B,KAAKC,eAAA,GAAkB;QACvB,KAAKC,eAAA,GAAkB;QACvB,KAAKC,aAAA,GAAgB;QACrB,KAAKC,cAAA,GAAiB;QACtB,KAAKC,aAAA,GAAgB;MACtB;IACF;IAED,MAAMC,QAAA,CAAS;MACbnT,YAAA,EAAc;QACZ,KAAKmH,KAAA,GAAQ;QACb,KAAKiM,SAAA,GAAY;QACjB,KAAKC,OAAA,GAAU;QACf,KAAKC,GAAA,GAAM;QACX,KAAKC,cAAA,GAAiB;QACtB,KAAKC,cAAA,GAAiB;QACtB,KAAKC,aAAA,GAAgB;QACrB,KAAKC,OAAA,GAAU;MAChB;IACF;IAED,MAAMC,OAAA,CAAQ;MACZ3T,YAAA,EAAc;QACZ,KAAK4T,YAAA,GAAe;QACpB,KAAKC,YAAA,GAAe;QACpB,KAAKC,eAAA,GAAkB;QACvB,KAAKC,YAAA,GAAe;QACpB,KAAKC,MAAA,GAAS;QACd,KAAKzG,UAAA,GAAa;QAClB,KAAK0G,aAAA,GAAgB;QACrB,KAAKC,cAAA,GAAiB;QACtB,KAAKC,YAAA,GAAe;QACpB,KAAKC,UAAA,GAAa;QAClB,KAAKC,WAAA,GAAc;QACnB,KAAKC,SAAA,GAAY;QACjB,KAAKzN,OAAA,GAAU,EAAE;QACjB,KAAK2D,UAAA,GAAa,EAAE;QACpB,KAAK+J,WAAA,GAAc,EAAE;QACrB,KAAKC,OAAA,GAAU,EAAE;QACjB,KAAKC,QAAA,GAAW,EAAE;QAClB,KAAK9M,aAAA,GAAgB,CAAE;QACvB,KAAKT,QAAA,GAAW,UAAUzC,IAAA,EAAMd,IAAA,EAAM;UACpC,IAAI,CAACA,IAAA,EAAM;YACTA,IAAA,GAAO,KAAK2Q,SAAA;UACb;UAED,IAAI3Q,IAAA,CAAKwD,KAAA,IAAS1C,IAAA,EAAM;YACtB,OAAOd,IAAA;UACR;UAED,SAASd,CAAA,GAAI,GAAGA,CAAA,GAAIc,IAAA,CAAK6J,SAAA,CAAUpL,MAAA,EAAQS,CAAA,IAAK;YAC9C,IAAIyF,GAAA,GAAM,KAAKpB,QAAA,CAASzC,IAAA,EAAMd,IAAA,CAAK6J,SAAA,CAAU3K,CAAC,CAAC;YAC/C,IAAIyF,GAAA,EAAK,OAAOA,GAAA;UACjB;UAED,OAAO;QACR;QAED,KAAKoB,OAAA,GAAU,YAAY;UACzB,KAAKjC,SAAA,GAAY;UAEjBd,SAAA,CAAU,IAAI;UAEd,IAAI8G,CAAA,GAAI,KAAK6G,SAAA,CAAU5K,OAAA,CAAQ,IAAI;UAEnC,SAAS7G,CAAA,IAAK,KAAKgE,OAAA,EAAS,KAAKA,OAAA,CAAQhE,CAAC,EAAEuG,eAAA,CAAgB,IAAI;UAEhE,IAAI,KAAKmL,WAAA,CAAYnS,MAAA,GAAS,GAAG;YAC/B,IAAImB,CAAA,GAAI,KAAKgR,WAAA,CAAY,CAAC,EAAE7K,OAAA,CAAQ,IAAI;UACzC;UAED,OAAO;YAAEgL,MAAA,EAAQjH,CAAA;YAAGkH,SAAA,EAAWpR;UAAG;QACnC;MACF;IACF;IAED,MAAMqR,SAAA,CAAU;MACd5U,YAAA,EAAc;QACZ,KAAK6U,QAAA,GAAW,CAAC,IAAI,IAAI,IAAI,EAAE;QAC/B,KAAKnL,OAAA,GAAU,YAAY;UACzB,IAAIoL,CAAA,GAAI,IAAI9S,OAAA,CAAS;UAErB,SAASa,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAG,EAAEA,CAAA,EAAG;YAC1B,SAASkS,EAAA,GAAK,GAAGA,EAAA,GAAK,GAAG,EAAEA,EAAA,EAAI;cAC7BD,CAAA,CAAED,QAAA,CAAShS,CAAA,GAAI,IAAIkS,EAAE,IAAI,KAAKF,QAAA,CAASE,EAAE,EAAElS,CAAC;YAC7C;UACF;UAED,OAAOiS,CAAA;QACR;MACF;IACF;IAED,IAAIE,YAAA,GAAe;IAEnB,SAASC,UAAUC,QAAA,EAAU;MAC3B,IAAIC,GAAA,GAAMD,QAAA,CAASzG,UAAA,CAAWyG,QAAA,CAASE,UAAA,EAAYJ,YAAY;MAC/DE,QAAA,CAASE,UAAA,IAAc;MACvB,OAAOD,GAAA;IACR;IAED,SAASE,YAAYH,QAAA,EAAU;MAC7B,IAAIC,GAAA,GAAMD,QAAA,CAASI,UAAA,CAAWJ,QAAA,CAASE,UAAA,EAAYJ,YAAY;MAC/DE,QAAA,CAASE,UAAA,IAAc;MACvB,OAAOD,GAAA;IACR;IAED,SAASI,aAAaL,QAAA,EAAU;MAC9B,IAAIC,GAAA,GAAMD,QAAA,CAASM,QAAA,CAASN,QAAA,CAASE,UAAU;MAC/CF,QAAA,CAASE,UAAA,IAAc;MACvB,OAAOD,GAAA;IACR;IAED,SAASM,cAAcP,QAAA,EAAU;MAC/B,IAAIC,GAAA,GAAMD,QAAA,CAASQ,SAAA,CAAUR,QAAA,CAASE,UAAA,EAAYJ,YAAY;MAC9DE,QAAA,CAASE,UAAA,IAAc;MACvB,OAAOD,GAAA;IACR;IAED,SAASQ,kBAAkBT,QAAA,EAAU;MACnC,IAAIC,GAAA,GAAMD,QAAA,CAASU,SAAA,CAAUV,QAAA,CAASE,UAAA,EAAYJ,YAAY;MAC9DE,QAAA,CAASE,UAAA,IAAc;MACvB,OAAOD,GAAA;IACR;IAED,SAASU,cAAcX,QAAA,EAAU;MAC/B,IAAIC,GAAA,GAAMD,QAAA,CAASU,SAAA,CAAUV,QAAA,CAASE,UAAA,EAAYJ,YAAY;MAC9DE,QAAA,CAASE,UAAA,IAAc;MACvB,OAAOD,GAAA;IACR;IAED,SAASW,gBAAgBC,MAAA,EAAQ;MAC/B,IAAI5F,CAAA,GAAI,IAAI/D,UAAA,CAAY;MACxB+D,CAAA,CAAE7O,CAAA,GAAI2T,SAAA,CAAUc,MAAM;MACtB5F,CAAA,CAAE5O,CAAA,GAAI0T,SAAA,CAAUc,MAAM;MACtB5F,CAAA,CAAE3O,CAAA,GAAIyT,SAAA,CAAUc,MAAM;MACtB,OAAO5F,CAAA;IACR;IAED,SAAS6F,eAAeD,MAAA,EAAQ;MAC9B,IAAIE,CAAA,GAAI,IAAI5J,SAAA,CAAW;MACvB4J,CAAA,CAAEtR,CAAA,GAAIsQ,SAAA,CAAUc,MAAM;MACtBE,CAAA,CAAE3J,CAAA,GAAI2I,SAAA,CAAUc,MAAM;MACtBE,CAAA,CAAEzS,CAAA,GAAIyR,SAAA,CAAUc,MAAM;MACtB,OAAOE,CAAA;IACR;IAED,SAASC,kBAAkBH,MAAA,EAAQ;MACjC,IAAI5F,CAAA,GAAI,IAAI3D,YAAA,CAAc;MAC1B2D,CAAA,CAAExO,CAAA,GAAIsT,SAAA,CAAUc,MAAM;MACtB5F,CAAA,CAAE7O,CAAA,GAAI2T,SAAA,CAAUc,MAAM;MACtB5F,CAAA,CAAE5O,CAAA,GAAI0T,SAAA,CAAUc,MAAM;MACtB5F,CAAA,CAAE3O,CAAA,GAAIyT,SAAA,CAAUc,MAAM;MACtB,OAAO5F,CAAA;IACR;IAED,SAASgG,cAAcJ,MAAA,EAAQ;MAC7B,IAAIlH,CAAA,GAAI,IAAInC,QAAA,CAAU;MACtB,IAAI0J,iBAAA,GAAoBT,iBAAA,CAAkBI,MAAM;MAChDA,MAAA,CAAOM,SAAA,CAAUxH,CAAA,CAAE9L,IAAA,EAAM,GAAGqT,iBAAiB;MAC7C,OAAOvH,CAAA,CAAEnH,QAAA,CAAU;IACpB;IAED,SAAS4O,oBAAoBP,MAAA,EAAQ;MACnC,IAAIpU,CAAA,GAAI,IAAI8K,cAAA,CAAgB;MAC5B9K,CAAA,CAAE8J,SAAA,GAAYkK,iBAAA,CAAkBI,MAAM;MACtCpU,CAAA,CAAE+J,OAAA,GAAUuJ,SAAA,CAAUc,MAAM;MAC5B,OAAOpU,CAAA;IACR;IAED,SAAS4U,iBAAiBR,MAAA,EAAQ;MAChC,IAAIjB,CAAA,GAAI,IAAIF,SAAA,CAAW;MAEvB,SAAS/R,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAG,EAAEA,CAAA,EAAG;QAC1B,SAASkS,EAAA,GAAK,GAAGA,EAAA,GAAK,GAAG,EAAEA,EAAA,EAAI;UAC7BD,CAAA,CAAED,QAAA,CAAShS,CAAC,EAAEkS,EAAE,IAAIE,SAAA,CAAUc,MAAM;QACrC;MACF;MAED,OAAOjB,CAAA;IACR;IAED,SAAS0B,iBAAiBT,MAAA,EAAQ;MAChC,IAAI5F,CAAA,GAAI,IAAInD,WAAA,CAAa;MACzBmD,CAAA,CAAElD,KAAA,GAAQoI,WAAA,CAAYU,MAAM;MAC5B5F,CAAA,CAAEjD,MAAA,GAAS4I,eAAA,CAAgBC,MAAM;MACjC,OAAO5F,CAAA;IACR;IAED,SAASsG,eAAeV,MAAA,EAAQ;MAC9B,IAAI5F,CAAA,GAAI,IAAIhD,SAAA,CAAW;MACvBgD,CAAA,CAAElD,KAAA,GAAQoI,WAAA,CAAYU,MAAM;MAC5B5F,CAAA,CAAEjD,MAAA,GAASgJ,iBAAA,CAAkBH,MAAM;MACnC,OAAO5F,CAAA;IACR;IAED,SAASuG,yBAAyBX,MAAA,EAAQhT,IAAA,EAAM4T,IAAA,EAAM;MACpD,SAAS9T,CAAA,GAAI,GAAGA,CAAA,GAAI8T,IAAA,EAAM9T,CAAA,IAAKE,IAAA,CAAKF,CAAC,IAAIyT,mBAAA,CAAoBP,MAAM;IACpE;IAED,SAASa,sBAAsBb,MAAA,EAAQhT,IAAA,EAAM4T,IAAA,EAAM;MACjD,SAAS9T,CAAA,GAAI,GAAGA,CAAA,GAAI8T,IAAA,EAAM9T,CAAA,IAAKE,IAAA,CAAKF,CAAC,IAAI2T,gBAAA,CAAiBT,MAAM;IACjE;IAED,SAASc,oBAAoBd,MAAA,EAAQhT,IAAA,EAAM4T,IAAA,EAAM;MAC/C,SAAS9T,CAAA,GAAI,GAAGA,CAAA,GAAI8T,IAAA,EAAM9T,CAAA,IAAKE,IAAA,CAAKF,CAAC,IAAI4T,cAAA,CAAeV,MAAM;IAC/D;IAED,SAASe,WAAWf,MAAA,EAAQlF,CAAA,EAASnQ,CAAA,EAAG;MAEtC,OAAOqV,MAAA,CAAOgB,IAAA,CAAKC,MAAA,CAAOnG,CAAC,IAAInQ,CAAA,EAAGuW,YAAY;IAC/C;IAED,SAASC,UAAUC,IAAA,EAAM;MACvB,IAAI,CAACA,IAAA,EAAM,MAAM;IAClB;IAED,SAASC,eAAerB,MAAA,EAAQsB,MAAA,EAAQC,KAAA,EAAO;MAC7C,IAAIC,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAW/R,mBAAmB;MAExCqQ,aAAA,CAAcE,MAAM;MACpB,IAAI/S,IAAA,GAAO,IAAIoK,MAAA,CAAQ;MACvBpK,IAAA,CAAKwG,OAAA,GAAU6N,MAAA;MACfrU,IAAA,CAAKwU,MAAA,GAASF,KAAA;MACdtU,IAAA,CAAKmE,KAAA,GAAQgP,aAAA,CAAcJ,MAAM;MACjC/S,IAAA,CAAKqK,eAAA,GAAkBkJ,gBAAA,CAAiBR,MAAM;MAC9C/S,IAAA,CAAKsK,YAAA,GAAeqI,iBAAA,CAAkBI,MAAM;MAC5C/S,IAAA,CAAKuK,UAAA,GAAaoI,iBAAA,CAAkBI,MAAM;MAE1C,IAAI/S,IAAA,CAAKuK,UAAA,EAAY;QACnBvK,IAAA,CAAK6D,OAAA,GAAU,EAAE;QAEjB,SAAShE,CAAA,GAAI,GAAGA,CAAA,GAAIG,IAAA,CAAKuK,UAAA,EAAY,EAAE1K,CAAA,EAAG;UACxCG,IAAA,CAAK6D,OAAA,CAAQhE,CAAC,IAAI8S,iBAAA,CAAkBI,MAAM;QAC3C;MACF;MAED,IAAI/S,IAAA,CAAKsK,YAAA,EAAc;QACrBtK,IAAA,CAAKwK,SAAA,GAAY,EAAE;QAEnB,SAAS3K,CAAA,GAAI,GAAGA,CAAA,GAAIG,IAAA,CAAKsK,YAAA,EAAc,EAAEzK,CAAA,EAAG;UAC1C,IAAI4U,KAAA,GAAQL,cAAA,CAAerB,MAAA,EAAQ/S,IAAA,EAAMsU,KAAA,EAAO;UAChDtU,IAAA,CAAKwK,SAAA,CAAU3K,CAAC,IAAI4U,KAAA;QACrB;MACF;MAED,OAAOzU,IAAA;IACR;IAID,SAAS0U,eAAe3B,MAAA,EAAQvS,CAAA,EAAG;MACjC,IAAI+T,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAWjS,mBAAmB;MAExCuQ,aAAA,CAAcE,MAAM;MACpBvS,CAAA,CAAE2D,KAAA,GAAQgP,aAAA,CAAcJ,MAAM;MAC9BvS,CAAA,CAAEoK,WAAA,GAAc+H,iBAAA,CAAkBI,MAAM;MACxCvS,CAAA,CAAEuG,aAAA,GAAgBwM,gBAAA,CAAiBR,MAAM;MAGzC,IAAI4B,SAAA,EAAW;QACbb,UAAA,CAAWf,MAAA,EAAQvS,CAAA,CAAE+H,QAAA,EAAU/H,CAAA,CAAEoK,WAAW;MACpD,OAAa;QAGLpK,CAAA,CAAE+H,QAAA,GAAW,EAAE;QACfmL,wBAAA,CAAyBX,MAAA,EAAQvS,CAAA,CAAE+H,QAAA,EAAU/H,CAAA,CAAEoK,WAAW;MAC3D;MAED,OAAOpK,CAAA;IACR;IAED,SAASoU,eAAe7B,MAAA,EAAQjP,IAAA,EAAM;MACpC,IAAIyQ,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAWpS,mBAAmB;MAExC0Q,aAAA,CAAcE,MAAM;MACpBjP,IAAA,CAAK0B,eAAA,GAAkBmN,iBAAA,CAAkBI,MAAM;MAC/CjP,IAAA,CAAK2B,YAAA,GAAekN,iBAAA,CAAkBI,MAAM;MAC5CjP,IAAA,CAAK4B,SAAA,GAAYiN,iBAAA,CAAkBI,MAAM;MACzCjP,IAAA,CAAK6B,SAAA,GAAYgN,iBAAA,CAAkBI,MAAM;MACzCjP,IAAA,CAAK8B,cAAA,GAAiB+M,iBAAA,CAAkBI,MAAM;MAC9CjP,IAAA,CAAK+Q,gBAAA,GAAmB,EAAE;MAE1B,IAAI5B,CAAA,GAAIN,iBAAA,CAAkBI,MAAM;MAEhC,IAAIE,CAAA,GAAItQ,yBAAA,EAA2B;QACjC,IAAIgS,SAAA,EAAW;UACbb,UAAA,CAAWf,MAAA,EAAQjP,IAAA,CAAK+B,SAAA,EAAW/B,IAAA,CAAK2B,YAAY;QAC9D,OAAe;UAGL3B,IAAA,CAAK+B,SAAA,GAAY,EAAE;UACnB/B,IAAA,CAAKiE,aAAA,GAAgBgL,MAAA,CAAO+B,UAAA,CAAW/B,MAAA,CAAOX,UAAA,EAAYW,MAAA,CAAOX,UAAA,GAAatO,IAAA,CAAK2B,YAAA,GAAe,IAAI,CAAC;UACvGsN,MAAA,CAAOgB,IAAA,CAAKjQ,IAAA,CAAK2B,YAAA,GAAe,IAAI,GAAGwO,YAAY;QACpD;MACF;MAED,IAAIhB,CAAA,GAAIrQ,uBAAA,EAAyB;QAC/B,IAAI+R,SAAA,EAAW;UACbb,UAAA,CAAWf,MAAA,EAAQjP,IAAA,CAAKgC,QAAA,EAAUhC,IAAA,CAAK2B,YAAY;QAC7D,OAAe;UAGL3B,IAAA,CAAKgC,QAAA,GAAW,EAAE;UAClBhC,IAAA,CAAKkE,aAAA,GAAgB+K,MAAA,CAAO+B,UAAA,CAAW/B,MAAA,CAAOX,UAAA,EAAYW,MAAA,CAAOX,UAAA,GAAatO,IAAA,CAAK2B,YAAA,GAAe,IAAI,CAAC;UACvGsN,MAAA,CAAOgB,IAAA,CAAKjQ,IAAA,CAAK2B,YAAA,GAAe,IAAI,GAAGwO,YAAY;QACpD;MACF;MAED,IAAIhB,CAAA,GAAIpQ,uCAAA,EAAyC;QAC/C,IAAI8R,SAAA,EAAW;UACbb,UAAA,CAAWf,MAAA,EAAQjP,IAAA,CAAKiC,SAAA,EAAWjC,IAAA,CAAK2B,YAAY;UACpDqO,UAAA,CAAWf,MAAA,EAAQjP,IAAA,CAAKkC,WAAA,EAAalC,IAAA,CAAK2B,YAAY;QAChE,OAAe;UAGL3B,IAAA,CAAKiC,SAAA,GAAY,EAAE;UACnBjC,IAAA,CAAKsE,cAAA,GAAiB2K,MAAA,CAAO+B,UAAA,CAAW/B,MAAA,CAAOX,UAAA,EAAYW,MAAA,CAAOX,UAAA,GAAatO,IAAA,CAAK2B,YAAA,GAAe,IAAI,CAAC;UACxGsN,MAAA,CAAOgB,IAAA,CAAKjQ,IAAA,CAAK2B,YAAA,GAAe,IAAI,GAAGwO,YAAY;UACnDnQ,IAAA,CAAKkC,WAAA,GAAc,EAAE;UACrBlC,IAAA,CAAKuE,gBAAA,GAAmB0K,MAAA,CAAO+B,UAAA,CAAW/B,MAAA,CAAOX,UAAA,EAAYW,MAAA,CAAOX,UAAA,GAAatO,IAAA,CAAK2B,YAAA,GAAe,IAAI,CAAC;UAC1GsN,MAAA,CAAOgB,IAAA,CAAKjQ,IAAA,CAAK2B,YAAA,GAAe,IAAI,GAAGwO,YAAY;QACpD;MACF;MAED,SAASvW,CAAA,GAAI,GAAGA,CAAA,GAAIsF,2BAAA,EAA6B,EAAEtF,CAAA,EAAG;QACpD,IAAI,EAAEuV,CAAA,GAAIvP,qBAAA,CAAsBhG,CAAC,IAAI;QAErC,IAAIiX,SAAA,EAAW;UACbb,UAAA,CAAWf,MAAA,EAAQjP,IAAA,CAAKmC,OAAA,CAAQvI,CAAC,GAAGoG,IAAA,CAAK2B,YAAY;QAC/D,OAAe;UAGL3B,IAAA,CAAKmC,OAAA,CAAQvI,CAAC,IAAI,EAAE;UACpBoG,IAAA,CAAKmE,YAAA,GAAe8K,MAAA,CAAO+B,UAAA,CAAW/B,MAAA,CAAOX,UAAA,EAAYW,MAAA,CAAOX,UAAA,GAAatO,IAAA,CAAK2B,YAAA,GAAe,IAAI,CAAC;UACtGsN,MAAA,CAAOgB,IAAA,CAAKjQ,IAAA,CAAK2B,YAAA,GAAe,IAAI,GAAGwO,YAAY;QACpD;MACF;MAEDnQ,IAAA,CAAKoE,iBAAA,GAAoB,EAAE;MAE3B,SAASxK,CAAA,GAAI,GAAGA,CAAA,GAAIuF,8BAAA,EAAgC,EAAEvF,CAAA,EAAG;QACvD,IAAI,EAAEuV,CAAA,GAAIxP,wBAAA,CAAyB/F,CAAC,IAAI;QAGxCoG,IAAA,CAAK+Q,gBAAA,CAAiBnX,CAAC,IAAIiV,iBAAA,CAAkBI,MAAM;QAEnD,IAAI4B,SAAA,EAAW;UACbb,UAAA,CAAWf,MAAA,EAAQjP,IAAA,CAAKoC,cAAA,CAAexI,CAAC,GAAGoG,IAAA,CAAK2B,YAAY;QACtE,OAAe;UAGL3B,IAAA,CAAKoC,cAAA,CAAexI,CAAC,IAAI,EAAE;UAE3BoG,IAAA,CAAKoE,iBAAA,CAAkBxK,CAAC,IAAI,EAAE;UAE9B,SAASqX,EAAA,GAAK,GAAGA,EAAA,GAAKjR,IAAA,CAAK2B,YAAA,EAAcsP,EAAA,IAAM;YAC7CjR,IAAA,CAAKoE,iBAAA,CAAkBxK,CAAC,EAAE+B,IAAA,CAAKwS,SAAA,CAAUc,MAAM,CAAC;YAChDjP,IAAA,CAAKoE,iBAAA,CAAkBxK,CAAC,EAAE+B,IAAA,CAAKwS,SAAA,CAAUc,MAAM,CAAC;YAChDd,SAAA,CAAUc,MAAM;UACjB;QACF;MACF;MAMD,IAAI4B,SAAA,EAAW;QACbhC,iBAAA,CAAkBI,MAAM;MAChC,OAAa;QAILjP,IAAA,CAAKqC,MAAA,GAAS,EAAE;QAChBrC,IAAA,CAAK+D,WAAA,GAAc,EAAE;QAErB,SAAShI,CAAA,GAAI,GAAGA,CAAA,GAAIiE,IAAA,CAAK4B,SAAA,EAAW,EAAE7F,CAAA,EAAG;UACvC,IAAImV,CAAA,GAAKlR,IAAA,CAAKqC,MAAA,CAAOtG,CAAC,IAAI,IAAIoJ,MAAA;UAE9B+L,CAAA,CAAE9L,WAAA,GAAcuJ,aAAA,CAAcM,MAAM;UACpCiC,CAAA,CAAE7L,QAAA,GAAW,EAAE;UAEf,SAAS5I,CAAA,GAAI,GAAGA,CAAA,GAAIyU,CAAA,CAAE9L,WAAA,EAAa,EAAE3I,CAAA,EAAG;YACtC,IAAIuD,IAAA,CAAK2B,YAAA,GAAe,KAAK,IAAI;cAC/BuP,CAAA,CAAE7L,QAAA,CAAS5I,CAAC,IAAIkS,aAAA,CAAcM,MAAM;YAClD,OAAmB;cACLiC,CAAA,CAAE7L,QAAA,CAAS5I,CAAC,IAAIoS,iBAAA,CAAkBI,MAAM;YACzC;UACF;UAED,IAAIiC,CAAA,CAAE9L,WAAA,KAAgB,GAAG;YACvBpF,IAAA,CAAK+D,WAAA,CAAYpI,IAAA,CAAKuV,CAAA,CAAE7L,QAAA,CAAS,CAAC,CAAC;YACnCrF,IAAA,CAAK+D,WAAA,CAAYpI,IAAA,CAAKuV,CAAA,CAAE7L,QAAA,CAAS,CAAC,CAAC;YACnCrF,IAAA,CAAK+D,WAAA,CAAYpI,IAAA,CAAKuV,CAAA,CAAE7L,QAAA,CAAS,CAAC,CAAC;UAC/C,WAAqB6L,CAAA,CAAE9L,WAAA,KAAgB,GAAG;YAC9BpF,IAAA,CAAK+D,WAAA,CAAYpI,IAAA,CAAKuV,CAAA,CAAE7L,QAAA,CAAS,CAAC,CAAC;YACnCrF,IAAA,CAAK+D,WAAA,CAAYpI,IAAA,CAAKuV,CAAA,CAAE7L,QAAA,CAAS,CAAC,CAAC;YACnCrF,IAAA,CAAK+D,WAAA,CAAYpI,IAAA,CAAKuV,CAAA,CAAE7L,QAAA,CAAS,CAAC,CAAC;YACnCrF,IAAA,CAAK+D,WAAA,CAAYpI,IAAA,CAAKuV,CAAA,CAAE7L,QAAA,CAAS,CAAC,CAAC;YACnCrF,IAAA,CAAK+D,WAAA,CAAYpI,IAAA,CAAKuV,CAAA,CAAE7L,QAAA,CAAS,CAAC,CAAC;YACnCrF,IAAA,CAAK+D,WAAA,CAAYpI,IAAA,CAAKuV,CAAA,CAAE7L,QAAA,CAAS,CAAC,CAAC;UAC/C,OAAiB;YACL,MAAM,IAAI8L,KAAA,CAAM,uFAAuF;UACxG;QACF;MACF;MAGD,IAAInR,IAAA,CAAK6B,SAAA,EAAW;QAClB7B,IAAA,CAAKE,MAAA,GAAS,EAAE;QAEhB,SAASzD,CAAA,GAAI,GAAGA,CAAA,GAAIuD,IAAA,CAAK6B,SAAA,EAAW,EAAEpF,CAAA,EAAG;UACvCuD,IAAA,CAAKE,MAAA,CAAOzD,CAAC,IAAI,IAAIoK,MAAA,CAAQ;UAC7B+J,cAAA,CAAe3B,MAAA,EAAQjP,IAAA,CAAKE,MAAA,CAAOzD,CAAC,CAAC;QACtC;MACF;IACF;IAED,SAAS2U,2BAA2BnC,MAAA,EAAQtG,IAAA,EAAM;MAChD,IAAI8H,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAW7R,+BAA+B;MAEpDmQ,aAAA,CAAcE,MAAM;MACpBtG,IAAA,CAAK3B,IAAA,GAAOqI,aAAA,CAAcJ,MAAM;MAChCtG,IAAA,CAAK1B,SAAA,GAAY4H,iBAAA,CAAkBI,MAAM;MACzCtG,IAAA,CAAKzB,MAAA,GAAS2H,iBAAA,CAAkBI,MAAM;MACtCtG,IAAA,CAAKvB,WAAA,GAAcyH,iBAAA,CAAkBI,MAAM;MAC3CtG,IAAA,CAAKtB,KAAA,GAAQwH,iBAAA,CAAkBI,MAAM;MACrCtG,IAAA,CAAKxB,KAAA,GAAQ,EAAE;MACf8H,MAAA,CAAOM,SAAA,CAAU5G,IAAA,CAAKxB,KAAA,EAAO,GAAGwB,IAAA,CAAKvB,WAAW;IACjD;IAID,SAASiK,mBAAmBpC,MAAA,EAAQxL,GAAA,EAAK;MACvC,IAAIgN,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAW9R,uBAAuB;MAE5CoQ,aAAA,CAAcE,MAAM;MACpBxL,GAAA,CAAI8E,aAAA,GAAgB9E,GAAA,CAAI+E,cAAA,GAAiBqG,iBAAA,CAAkBI,MAAM;MAEjE,IAAIxL,GAAA,CAAI+E,cAAA,EAAgB;QACtB,IAAI/E,GAAA,CAAIgF,WAAA,EAAa;UACnB,OAAOhF,GAAA,CAAIgF,WAAA;QACZ;QAEDhF,GAAA,CAAIgF,WAAA,GAAc,EAAE;QAEpB,SAAS1M,CAAA,GAAI,GAAGA,CAAA,GAAI0H,GAAA,CAAI+E,cAAA,EAAgB,EAAEzM,CAAA,EAAG;UAC3C0H,GAAA,CAAIgF,WAAA,CAAY1M,CAAC,IAAI,IAAIgL,kBAAA,CAAoB;UAC7CqK,0BAAA,CAA2BnC,MAAA,EAAQxL,GAAA,CAAIgF,WAAA,CAAY1M,CAAC,CAAC;QACtD;MACF;IACF;IAED,SAASuV,mBAAmBrC,MAAA,EAAQsC,EAAA,EAAI;MACtC,IAAId,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAWnS,uBAAuB;MAE5CyQ,aAAA,CAAcE,MAAM;MACpBsC,EAAA,CAAGtH,SAAA,GAAYoF,aAAA,CAAcJ,MAAM;MACnCsC,EAAA,CAAGrH,gBAAA,GAAmB2E,iBAAA,CAAkBI,MAAM;MAC9CsC,EAAA,CAAGpH,gBAAA,GAAmB0E,iBAAA,CAAkBI,MAAM;MAC9CsC,EAAA,CAAGnH,eAAA,GAAkByE,iBAAA,CAAkBI,MAAM;MAC7CsC,EAAA,CAAG/G,SAAA,GAAYqE,iBAAA,CAAkBI,MAAM;MACvCsC,EAAA,CAAG9G,UAAA,GAAaoE,iBAAA,CAAkBI,MAAM;MAExC,IAAIsC,EAAA,CAAGrH,gBAAA,EAAkB;QACvB,IAAI2G,SAAA,EAAW;UACbb,UAAA,CAAWf,MAAA,EAAQsC,EAAA,CAAGlH,aAAA,EAAekH,EAAA,CAAGrH,gBAAgB;QAClE,OAAe;UAGLqH,EAAA,CAAGlH,aAAA,GAAgB,EAAE;UACrByF,qBAAA,CAAsBb,MAAA,EAAQsC,EAAA,CAAGlH,aAAA,EAAekH,EAAA,CAAGrH,gBAAgB;QACpE;MACF;MAED,IAAIqH,EAAA,CAAGpH,gBAAA,EAAkB;QACvB,IAAI0G,SAAA,EAAW;UACbb,UAAA,CAAWf,MAAA,EAAQsC,EAAA,CAAGjH,aAAA,EAAeiH,EAAA,CAAGpH,gBAAgB;QAClE,OAAe;UAGLoH,EAAA,CAAGjH,aAAA,GAAgB,EAAE;UACrByF,mBAAA,CAAoBd,MAAA,EAAQsC,EAAA,CAAGjH,aAAA,EAAeiH,EAAA,CAAGpH,gBAAgB;QAClE;MACF;MAED,IAAIoH,EAAA,CAAGnH,eAAA,EAAiB;QACtB,IAAIyG,SAAA,EAAW;UACbb,UAAA,CAAWf,MAAA,EAAQsC,EAAA,CAAGhH,YAAA,EAAcgH,EAAA,CAAGnH,eAAe;QAChE,OAAe;UAGLmH,EAAA,CAAGhH,YAAA,GAAe,EAAE;UACpBuF,qBAAA,CAAsBb,MAAA,EAAQsC,EAAA,CAAGhH,YAAA,EAAcgH,EAAA,CAAGnH,eAAe;QAClE;MACF;IACF;IAED,SAASoH,eAAevC,MAAA,EAAQwC,IAAA,EAAM;MACpC,IAAIhB,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAWhS,wBAAwB;MAE7CsQ,aAAA,CAAcE,MAAM;MACpBwC,IAAA,CAAKpR,KAAA,GAAQgP,aAAA,CAAcJ,MAAM;MACjCwC,IAAA,CAAKvG,SAAA,GAAYqD,WAAA,CAAYU,MAAM;MACnCwC,IAAA,CAAKtG,eAAA,GAAkBoD,WAAA,CAAYU,MAAM;MACzCwC,IAAA,CAAKrG,YAAA,GAAeyD,iBAAA,CAAkBI,MAAM;MAE5C,IAAIwC,IAAA,CAAKrG,YAAA,EAAc;QACrBqG,IAAA,CAAKpG,SAAA,GAAY,EAAE;QAEnB,SAAS5O,CAAA,GAAI,GAAGA,CAAA,GAAIgV,IAAA,CAAKrG,YAAA,EAAc,EAAE3O,CAAA,EAAG;UAC1CgV,IAAA,CAAKpG,SAAA,CAAU5O,CAAC,IAAI,IAAIuN,UAAA,CAAY;UACpCsH,kBAAA,CAAmBrC,MAAA,EAAQwC,IAAA,CAAKpG,SAAA,CAAU5O,CAAC,CAAC;QAC7C;MACF;IACF;IAED,SAASiV,kBAAkBzC,MAAA,EAAQ0C,GAAA,EAAK;MACtC,IAAIlB,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAWrS,sBAAsB;MAE3C2Q,aAAA,CAAcE,MAAM;MACpB0C,GAAA,CAAInG,MAAA,GAASqD,iBAAA,CAAkBI,MAAM;MACrC0C,GAAA,CAAIlG,OAAA,GAAUoD,iBAAA,CAAkBI,MAAM;MACtCA,MAAA,CAAOM,SAAA,CAAUoC,GAAA,CAAIC,aAAA,EAAe,GAAG,CAAC;MAExC,IAAI,CAACf,SAAA,EAAW;QACd,IAAI,CAACc,GAAA,CAAIlG,OAAA,EAAS;UAChBkG,GAAA,CAAIhG,MAAA,GAAS,EAAE;UACfsD,MAAA,CAAOM,SAAA,CAAUoC,GAAA,CAAIhG,MAAA,EAAQ,GAAGgG,GAAA,CAAInG,MAAM;QACpD,OAAe;UACLmG,GAAA,CAAIhG,MAAA,GAAS,EAAE;UACfsD,MAAA,CAAOM,SAAA,CAAUoC,GAAA,CAAIhG,MAAA,EAAQ,GAAGgG,GAAA,CAAInG,MAAA,GAASmG,GAAA,CAAIlG,OAAA,GAAU,CAAC;QAC7D;MACF;IACF;IAED,SAASoG,gBAAgB5C,MAAA,EAAQhV,CAAA,EAAG;MAClC,IAAIwW,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAWtS,oBAAoB;MAEzC4Q,aAAA,CAAcE,MAAM;MACpBhV,CAAA,CAAEoG,KAAA,GAAQgP,aAAA,CAAcJ,MAAM;MAC9BhV,CAAA,CAAEoN,KAAA,GAAQwH,iBAAA,CAAkBI,MAAM;MAElC,IAAIhV,CAAA,CAAEoN,KAAA,IAASjI,yBAAA,EAA2B;QACxCnF,CAAA,CAAE4R,oBAAA,GAAuBsC,SAAA,CAAUc,MAAM;QACzChV,CAAA,CAAE6R,kBAAA,GAAqBqC,SAAA,CAAUc,MAAM;QACvChV,CAAA,CAAE8R,qBAAA,GAAwBoC,SAAA,CAAUc,MAAM;MAC3C;MAEDhV,CAAA,CAAEiS,aAAA,GAAgBgD,cAAA,CAAeD,MAAM;MACvChV,CAAA,CAAEkS,cAAA,GAAiB+C,cAAA,CAAeD,MAAM;MACxChV,CAAA,CAAEmS,aAAA,GAAgB8C,cAAA,CAAeD,MAAM;MAEvC,IAAIhV,CAAA,CAAEoN,KAAA,IAAShI,kBAAA,EAAoB;QACjCpF,CAAA,CAAE+R,eAAA,GAAkBmC,SAAA,CAAUc,MAAM;QACpChV,CAAA,CAAEgS,eAAA,GAAkBkC,SAAA,CAAUc,MAAM;MACrC;IACF;IAED,SAAS6C,iBAAiB7C,MAAA,EAAQ8C,GAAA,EAAK;MACrC,IAAItB,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAWvS,qBAAqB;MAE1C6Q,aAAA,CAAcE,MAAM;MACpB8C,GAAA,CAAI1R,KAAA,GAAQgP,aAAA,CAAcJ,MAAM;MAChC8C,GAAA,CAAIzF,SAAA,GAAY0C,eAAA,CAAgBC,MAAM;MACtC8C,GAAA,CAAIxF,OAAA,GAAUyC,eAAA,CAAgBC,MAAM;MACpC8C,GAAA,CAAIvF,GAAA,GAAMwC,eAAA,CAAgBC,MAAM;MAChC8C,GAAA,CAAItF,cAAA,GAAiB0B,SAAA,CAAUc,MAAM;MACrC8C,GAAA,CAAIrF,cAAA,GAAiByB,SAAA,CAAUc,MAAM;MACrC8C,GAAA,CAAIpF,aAAA,GAAgBwB,SAAA,CAAUc,MAAM;MACpC8C,GAAA,CAAInF,OAAA,GAAUuB,SAAA,CAAUc,MAAM;IAC/B;IAED,SAAS+C,gBAAgB/C,MAAA,EAAQnP,KAAA,EAAO;MACtC,IAAI2Q,OAAA,GAAU1B,aAAA,CAAcE,MAAM;MAClCmB,SAAA,CAAUK,OAAA,IAAWlS,oBAAoB;MAEzCwQ,aAAA,CAAcE,MAAM;MACpBnP,KAAA,CAAMoN,MAAA,GAAS2B,iBAAA,CAAkBI,MAAM;MACvCnP,KAAA,CAAM2G,UAAA,GAAaoI,iBAAA,CAAkBI,MAAM;MAC3CnP,KAAA,CAAMqN,aAAA,GAAgB0B,iBAAA,CAAkBI,MAAM;MAC9CnP,KAAA,CAAMsN,cAAA,GAAiByB,iBAAA,CAAkBI,MAAM;MAC/CnP,KAAA,CAAMuN,YAAA,GAAewB,iBAAA,CAAkBI,MAAM;MAC7CnP,KAAA,CAAMwN,UAAA,GAAauB,iBAAA,CAAkBI,MAAM;MAC3CnP,KAAA,CAAMyN,WAAA,GAAcsB,iBAAA,CAAkBI,MAAM;MAE5CnP,KAAA,CAAM0N,SAAA,GAAY,IAAIlH,MAAA,CAAQ;MAC9BxG,KAAA,CAAM0N,SAAA,GAAY8C,cAAA,CAAerB,MAAA,EAAQ,MAAM,CAAC;MAEhD,IAAInP,KAAA,CAAM2G,UAAA,EAAY;QACpB3G,KAAA,CAAMC,OAAA,GAAU,EAAE;QAElB,SAAShE,CAAA,GAAI,GAAGA,CAAA,GAAI+D,KAAA,CAAM2G,UAAA,EAAY,EAAE1K,CAAA,EAAG;UACzC+D,KAAA,CAAMC,OAAA,CAAQhE,CAAC,IAAI,IAAI0F,MAAA,CAAQ;UAC/BqP,cAAA,CAAe7B,MAAA,EAAQnP,KAAA,CAAMC,OAAA,CAAQhE,CAAC,CAAC;QACxC;MACF;MAGD,IAAI+D,KAAA,CAAMqN,aAAA,EAAe;QACvBrN,KAAA,CAAM4D,UAAA,GAAa,EAAE;QAErB,SAAS3H,CAAA,GAAI,GAAGA,CAAA,GAAI+D,KAAA,CAAMqN,aAAA,EAAe,EAAEpR,CAAA,EAAG;UAC5C+D,KAAA,CAAM4D,UAAA,CAAW3H,CAAC,IAAI,IAAIuM,UAAA,CAAY;UACtC+I,kBAAA,CAAmBpC,MAAA,EAAQnP,KAAA,CAAM4D,UAAA,CAAW3H,CAAC,CAAC;QAC/C;MACF;MAGD,IAAI+D,KAAA,CAAMsN,cAAA,EAAgB;QACxBtN,KAAA,CAAM2N,WAAA,GAAc,EAAE;QAEtB,SAAS1R,CAAA,GAAI,GAAGA,CAAA,GAAI+D,KAAA,CAAMsN,cAAA,EAAgB,EAAErR,CAAA,EAAG;UAC7C+D,KAAA,CAAM2N,WAAA,CAAY1R,CAAC,IAAI,IAAIkP,WAAA,CAAa;UACxCuG,cAAA,CAAevC,MAAA,EAAQnP,KAAA,CAAM2N,WAAA,CAAY1R,CAAC,CAAC;QAC5C;MACF;MAGD,IAAI+D,KAAA,CAAMuN,YAAA,EAAc;QACtBvN,KAAA,CAAMmS,SAAA,GAAY,EAAE;QAEpB,SAASlW,CAAA,GAAI,GAAGA,CAAA,GAAI+D,KAAA,CAAMuN,YAAA,EAAc,EAAEtR,CAAA,EAAG;UAC3C+D,KAAA,CAAMmS,SAAA,CAAUlW,CAAC,IAAI,IAAIwP,SAAA,CAAW;UACpCmG,iBAAA,CAAkBzC,MAAA,EAAQnP,KAAA,CAAMmS,SAAA,CAAUlW,CAAC,CAAC;QAC7C;MACF;MAGD,IAAI+D,KAAA,CAAMwN,UAAA,EAAY;QACpBxN,KAAA,CAAM4N,OAAA,GAAU,EAAE;QAElB,SAAS3R,CAAA,GAAI,GAAGA,CAAA,GAAI+D,KAAA,CAAMwN,UAAA,EAAY,EAAEvR,CAAA,EAAG;UACzC+D,KAAA,CAAM4N,OAAA,CAAQ3R,CAAC,IAAI,IAAI6P,OAAA,CAAS;UAChCiG,eAAA,CAAgB5C,MAAA,EAAQnP,KAAA,CAAM4N,OAAA,CAAQ3R,CAAC,CAAC;QACzC;MACF;MAGD,IAAI+D,KAAA,CAAMyN,WAAA,EAAa;QACrBzN,KAAA,CAAM6N,QAAA,GAAW,EAAE;QAEnB,SAAS5R,CAAA,GAAI,GAAGA,CAAA,GAAI+D,KAAA,CAAMyN,WAAA,EAAa,EAAExR,CAAA,EAAG;UAC1C+D,KAAA,CAAM6N,QAAA,CAAS5R,CAAC,IAAI,IAAIsQ,QAAA,CAAU;UAClCyF,gBAAA,CAAiB7C,MAAA,EAAQnP,KAAA,CAAM6N,QAAA,CAAS5R,CAAC,CAAC;QAC3C;MACF;IACF;IAED,IAAIoU,YAAA,GAAe;IACnB,IAAI+B,YAAA,GAAe;IAEnB,SAASC,aAAalD,MAAA,EAAQ;MAC5BA,MAAA,CAAOX,UAAA,GAAa;MACpBW,MAAA,CAAOgB,IAAA,GAAO,UAAUmC,GAAA,EAAKC,GAAA,EAAK;QAChC,IAAIA,GAAA,IAAOlC,YAAA,EAAc;UACvBlB,MAAA,CAAOX,UAAA,IAAc8D,GAAA;QACtB;QAED,IAAIC,GAAA,IAAOH,YAAA,EAAc;UACvBjD,MAAA,CAAOX,UAAA,GAAa8D,GAAA;QACrB;MACF;MAEDnD,MAAA,CAAOM,SAAA,GAAY,UAAU+C,IAAA,EAAMzC,IAAA,EAAMjW,CAAA,EAAG;QAC1C,IAAI2Y,KAAA,GAAQ1C,IAAA,GAAOjW,CAAA;QACnB,SAASmC,CAAA,GAAI,GAAGA,CAAA,GAAIwW,KAAA,EAAOxW,CAAA,IAAKuW,IAAA,CAAKvW,CAAC,IAAI0S,YAAA,CAAa,IAAI;MAC5D;MAEDQ,MAAA,CAAO+B,UAAA,GAAa,UAAUwB,KAAA,EAAOC,GAAA,EAAK;QACxC,IAAIH,IAAA,GAAO,KAAKja,MAAA;QAChB,IAAIqa,OAAA,GAAUJ,IAAA,CAAKK,KAAA,CAAMH,KAAA,EAAOC,GAAG;QACnC,OAAO,IAAIpO,YAAA,CAAaqO,OAAO;MAChC;MAEDzD,MAAA,CAAO2D,cAAA,GAAiB,UAAUJ,KAAA,EAAOC,GAAA,EAAK;QAC5C,IAAIH,IAAA,GAAO,KAAKja,MAAA;QAChB,IAAIqa,OAAA,GAAUJ,IAAA,CAAKK,KAAA,CAAMH,KAAA,EAAOC,GAAG;QACnC,OAAO,IAAII,WAAA,CAAYH,OAAO;MAC/B;MAEDzD,MAAA,CAAO6D,aAAA,GAAgB,UAAUN,KAAA,EAAOC,GAAA,EAAK;QAC3C,IAAIH,IAAA,GAAO,KAAKja,MAAA;QAChB,IAAIqa,OAAA,GAAUJ,IAAA,CAAKK,KAAA,CAAMH,KAAA,EAAOC,GAAG;QACnC,OAAO,IAAIjL,UAAA,CAAWkL,OAAO;MAC9B;MAEDzD,MAAA,CAAO8D,cAAA,GAAiB,UAAUP,KAAA,EAAOC,GAAA,EAAK;QAC5C,IAAIH,IAAA,GAAO,KAAKja,MAAA;QAChB,IAAIqa,OAAA,GAAUJ,IAAA,CAAKK,KAAA,CAAMH,KAAA,EAAOC,GAAG;QACnC,OAAO,IAAI3O,WAAA,CAAY4O,OAAO;MAC/B;IACF;IAED,IAAI7B,SAAA,EAAWmC,UAAA;IAEf,SAASC,eAAeC,SAAA,EAAW;MACjC,IAAIC,MAAA,GAAS,IAAItG,OAAA,CAAS;MAC1B,IAAIoC,MAAA,GAAS,IAAIvH,QAAA,CAASwL,SAAS;MACnCf,YAAA,CAAalD,MAAM;MACnBA,MAAA,CAAOgB,IAAA,CAAK,IAAIE,YAAY;MAE5BgD,MAAA,CAAOrG,YAAA,GAAe+B,iBAAA,CAAkBI,MAAM;MAE9CkE,MAAA,CAAOpG,YAAA,GAAe8B,iBAAA,CAAkBI,MAAM;MAE9CkE,MAAA,CAAOnG,eAAA,GAAkB6B,iBAAA,CAAkBI,MAAM;MAEjDkE,MAAA,CAAOlG,YAAA,GAAe4B,iBAAA,CAAkBI,MAAM;MAC9C4B,SAAA,GAAYlC,aAAA,CAAcM,MAAM,IAAI;MACpC+D,UAAA,GAAarE,aAAA,CAAcM,MAAM,IAAI;MACrC,IAAI4B,SAAA,EAAW,MAAM;MACrB5B,MAAA,CAAOgB,IAAA,CAAK,KAAKE,YAAY;MAC7BlB,MAAA,CAAOgB,IAAA,CAAK,KAAKE,YAAY;MAC7BlB,MAAA,CAAOgB,IAAA,CAAK,IAAIE,YAAY;MAC5B,IAAI6C,UAAA,EAAY;QACd,IAAII,gBAAA,GAAmBrE,aAAA,CAAcE,MAAM;QAC3C,IAAIoE,cAAA,GAAiBpE,MAAA,CAAOqE,QAAA,CAAQ,IAAKrE,MAAA,CAAOsE,IAAA,CAAM;QACtD,IAAIC,cAAA,GAAiB,EAAE;QACvBvE,MAAA,CAAOwE,IAAA,CAAKD,cAAA,EAAgB,GAAGH,cAAc;QAC7C,IAAIK,gBAAA,GAAmB,EAAE;QACzBC,UAAA,CAAWD,gBAAA,EAAkBN,gBAAA,EAAkBI,cAAA,EAAgBH,cAAc;QAC7E,IAAIf,IAAA,GAAO,IAAIsB,WAAA,CAAYF,gBAAgB;QAC3C1B,eAAA,CAAgBM,IAAA,EAAMa,MAAM;MACpC,OAAa;QACLnB,eAAA,CAAgB/C,MAAA,EAAQkE,MAAM;MAC/B;MAED,OAAOA,MAAA,CAAOvQ,OAAA,CAAS;IACxB;IAED,OAAOqQ,cAAA,CAAe5a,MAAM;EAC7B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}