{"ast": null, "code": "// converts the given BVH raycast intersection to align with the three.js raycast\n// structure (include object, world space distance and point).\nexport function convertRaycastIntersect(hit, object, raycaster) {\n  if (hit === null) {\n    return null;\n  }\n  hit.point.applyMatrix4(object.matrixWorld);\n  hit.distance = hit.point.distanceTo(raycaster.ray.origin);\n  hit.object = object;\n  return hit;\n}", "map": {"version": 3, "names": ["convertRaycastIntersect", "hit", "object", "raycaster", "point", "applyMatrix4", "matrixWorld", "distance", "distanceTo", "ray", "origin"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/node_modules/three-mesh-bvh/src/utils/GeometryRayIntersectUtilities.js"], "sourcesContent": ["// converts the given BVH raycast intersection to align with the three.js raycast\n// structure (include object, world space distance and point).\nexport function convertRaycastIntersect( hit, object, raycaster ) {\n\n\tif ( hit === null ) {\n\n\t\treturn null;\n\n\t}\n\n\thit.point.applyMatrix4( object.matrixWorld );\n\thit.distance = hit.point.distanceTo( raycaster.ray.origin );\n\thit.object = object;\n\n\treturn hit;\n\n}\n"], "mappings": "AAAA;AACA;AACA,OAAO,SAASA,uBAAuBA,CAAEC,GAAG,EAAEC,MAAM,EAAEC,SAAS,EAAG;EAEjE,IAAKF,GAAG,KAAK,IAAI,EAAG;IAEnB,OAAO,IAAI;EAEZ;EAEAA,GAAG,CAACG,KAAK,CAACC,YAAY,CAAEH,MAAM,CAACI,WAAY,CAAC;EAC5CL,GAAG,CAACM,QAAQ,GAAGN,GAAG,CAACG,KAAK,CAACI,UAAU,CAAEL,SAAS,CAACM,GAAG,CAACC,MAAO,CAAC;EAC3DT,GAAG,CAACC,MAAM,GAAGA,MAAM;EAEnB,OAAOD,GAAG;AAEX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}