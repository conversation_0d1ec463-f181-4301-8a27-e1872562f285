{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\nicola-portfolio\\\\nicola-portfolio\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport './App.css';\nimport Navbar from \"./components/Navbar/Navbar\";\nimport Hero from \"./components/Hero/Hero\";\nimport AboutMe from \"./components/AboutMe/AboutMe\";\nimport Skills from \"./components/Skills/Skills\";\nimport ContactMe from \"./components/ContactMe/ContactMe\";\nimport Projects from \"./components/Projects/Projects\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  _s();\n  const [isDarkMode, setIsDarkMode] = useState(true);\n  const toggleTheme = () => {\n    setIsDarkMode(!isDarkMode);\n  };\n  useEffect(() => {\n    document.body.className = isDarkMode ? \"dark-theme\" : \"light-theme\";\n  }, [isDarkMode]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {\n      toggleTheme: toggleTheme,\n      isDarkMode: isDarkMode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AboutMe, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Skills, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Projects, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ContactMe, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(App, \"wcg0iE8CdipV533flP6RCfuqOPI=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "Hero", "AboutMe", "Skills", "ContactMe", "Projects", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "isDarkMode", "setIsDarkMode", "toggleTheme", "document", "body", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/App.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport './App.css';\r\nimport Navbar from \"./components/Navbar/Navbar\";\r\nimport Hero from \"./components/Hero/Hero\";\r\nimport AboutMe from \"./components/AboutMe/AboutMe\";\r\nimport Skills from \"./components/Skills/Skills\";\r\nimport ContactMe from \"./components/ContactMe/ContactMe\";\r\nimport Projects from \"./components/Projects/Projects\";\r\n\r\nconst App = () => {\r\n  const [isDarkMode, setIsDarkMode] = useState(true); \r\n\r\n  const toggleTheme = () => {\r\n    setIsDarkMode(!isDarkMode);\r\n  };\r\n\r\n  useEffect(() => {\r\n    document.body.className = isDarkMode ? \"dark-theme\" : \"light-theme\";\r\n  }, [isDarkMode]);\r\n\r\n  return (\r\n    <>\r\n      <Navbar toggleTheme={toggleTheme} isDarkMode={isDarkMode} />\r\n      <div className=\"container\">\r\n        <Hero />\r\n        <AboutMe />\r\n        <Skills />\r\n        <Projects />\r\n        <ContactMe />\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAClB,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,QAAQ,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAElD,MAAMgB,WAAW,GAAGA,CAAA,KAAM;IACxBD,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAEDb,SAAS,CAAC,MAAM;IACdgB,QAAQ,CAACC,IAAI,CAACC,SAAS,GAAGL,UAAU,GAAG,YAAY,GAAG,aAAa;EACrE,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,oBACEL,OAAA,CAAAE,SAAA;IAAAS,QAAA,gBACEX,OAAA,CAACP,MAAM;MAACc,WAAW,EAAEA,WAAY;MAACF,UAAU,EAAEA;IAAW;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5Df,OAAA;MAAKU,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBX,OAAA,CAACN,IAAI;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACRf,OAAA,CAACL,OAAO;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXf,OAAA,CAACJ,MAAM;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVf,OAAA,CAACF,QAAQ;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACZf,OAAA,CAACH,SAAS;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACX,EAAA,CAvBID,GAAG;AAAAa,EAAA,GAAHb,GAAG;AAyBT,eAAeA,GAAG;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}