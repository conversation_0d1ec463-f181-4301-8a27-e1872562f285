{"ast": null, "code": "export const SKILLS = [{\n  title: \"Front-End\",\n  icon: \"./images/frontend.png\",\n  skills: [{\n    skill: \"HTML5\",\n    percentage: \"80%\"\n  }, {\n    skill: \"CSS3\",\n    percentage: \"80%\"\n  }, {\n    skill: \"JavaScript\",\n    percentage: \"70%\"\n  }, {\n    skill: \"React.js\",\n    percentage: \"70%\"\n  }, {\n    skill: \"Flutter\",\n    percentage: \"50%\"\n  }]\n}, {\n  title: \"Back-End\",\n  icon: \"./images/backend.png\",\n  skills: [{\n    skill: \"Laravel\",\n    percentage: \"55%\"\n  }, {\n    skill: \"ASP.NET\",\n    percentage: \"60%\"\n  }]\n}, {\n  title: \"Soft Skills\",\n  icon: \"./images/soft skills.png\",\n  skills: [{\n    skill: \"Communication\",\n    percentage: \"85%\"\n  }, {\n    skill: \"Presentation\",\n    percentage: \"85%\"\n  }, {\n    skill: \"Problem-Solving\",\n    percentage: \"75%\"\n  }, {\n    skill: \"Time Management\",\n    percentage: \"80%\"\n  }]\n}, {\n  title: \"Tools\",\n  icon: \"./images/tools.png\",\n  skills: [{\n    skill: \"Git & Github\",\n    percentage: \"70%\"\n  }, {\n    skill: \"Postman\",\n    percentage: \"65%\"\n  }, {\n    skill: \"SQL Server\",\n    percentage: \"55%\"\n  }, {\n    skill: \"Canva\",\n    percentage: \"80%\"\n  }, {\n    skill: \"Jira\",\n    percentage: \"80%\"\n  }]\n}];", "map": {"version": 3, "names": ["SKILLS", "title", "icon", "skills", "skill", "percentage"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/utils/data.js"], "sourcesContent": ["export const SKILLS =[\r\n    {\r\n        title:\"Front-End\",\r\n        icon:\"./images/frontend.png\",\r\n        skills : [\r\n            {skill : \"HTML5\", percentage:\"80%\"},\r\n            {skill : \"CSS3\", percentage:\"80%\"},\r\n            {skill :\"JavaScript\", percentage:\"70%\"},\r\n            {skill :\"React.js\", percentage:\"70%\"},\r\n            {skill :\"Flutter\",percentage:\"50%\"}\r\n\r\n        ],\r\n    },\r\n\r\n    {\r\n        title:\"Back-End\",\r\n        icon:\"./images/backend.png\",\r\n        skills : [\r\n            {skill : \"Laravel\", percentage:\"55%\"},\r\n            {skill : \"ASP.NET\", percentage:\"60%\"},\r\n            \r\n        \r\n        ],\r\n    },\r\n\r\n    {\r\n        title:\"Soft Skills\",\r\n        icon:\"./images/soft skills.png\",\r\n        skills : [\r\n            {skill : \"Communication\", percentage:\"85%\"},\r\n            {skill : \"Presentation\", percentage:\"85%\"},\r\n            {skill : \"Problem-Solving\", percentage:\"75%\"},\r\n            {skill : \"Time Management\", percentage:\"80%\"},\r\n\r\n        ],\r\n    },\r\n\r\n    {\r\n        title:\"Tools\",\r\n        icon:\"./images/tools.png\",\r\n        skills : [\r\n            {skill : \"Git & Github\", percentage:\"70%\"},\r\n            {skill : \"Postman\", percentage:\"65%\"},\r\n            {skill : \"SQL Server\", percentage:\"55%\"},\r\n            {skill : \"Canva\",percentage:\"80%\"},\r\n            {skill : \"Jira\",percentage:\"80%\"},\r\n\r\n        ],\r\n    },\r\n]\r\n\r\n\r\n"], "mappings": "AAAA,OAAO,MAAMA,MAAM,GAAE,CACjB;EACIC,KAAK,EAAC,WAAW;EACjBC,IAAI,EAAC,uBAAuB;EAC5BC,MAAM,EAAG,CACL;IAACC,KAAK,EAAG,OAAO;IAAEC,UAAU,EAAC;EAAK,CAAC,EACnC;IAACD,KAAK,EAAG,MAAM;IAAEC,UAAU,EAAC;EAAK,CAAC,EAClC;IAACD,KAAK,EAAE,YAAY;IAAEC,UAAU,EAAC;EAAK,CAAC,EACvC;IAACD,KAAK,EAAE,UAAU;IAAEC,UAAU,EAAC;EAAK,CAAC,EACrC;IAACD,KAAK,EAAE,SAAS;IAACC,UAAU,EAAC;EAAK,CAAC;AAG3C,CAAC,EAED;EACIJ,KAAK,EAAC,UAAU;EAChBC,IAAI,EAAC,sBAAsB;EAC3BC,MAAM,EAAG,CACL;IAACC,KAAK,EAAG,SAAS;IAAEC,UAAU,EAAC;EAAK,CAAC,EACrC;IAACD,KAAK,EAAG,SAAS;IAAEC,UAAU,EAAC;EAAK,CAAC;AAI7C,CAAC,EAED;EACIJ,KAAK,EAAC,aAAa;EACnBC,IAAI,EAAC,0BAA0B;EAC/BC,MAAM,EAAG,CACL;IAACC,KAAK,EAAG,eAAe;IAAEC,UAAU,EAAC;EAAK,CAAC,EAC3C;IAACD,KAAK,EAAG,cAAc;IAAEC,UAAU,EAAC;EAAK,CAAC,EAC1C;IAACD,KAAK,EAAG,iBAAiB;IAAEC,UAAU,EAAC;EAAK,CAAC,EAC7C;IAACD,KAAK,EAAG,iBAAiB;IAAEC,UAAU,EAAC;EAAK,CAAC;AAGrD,CAAC,EAED;EACIJ,KAAK,EAAC,OAAO;EACbC,IAAI,EAAC,oBAAoB;EACzBC,MAAM,EAAG,CACL;IAACC,KAAK,EAAG,cAAc;IAAEC,UAAU,EAAC;EAAK,CAAC,EAC1C;IAACD,KAAK,EAAG,SAAS;IAAEC,UAAU,EAAC;EAAK,CAAC,EACrC;IAACD,KAAK,EAAG,YAAY;IAAEC,UAAU,EAAC;EAAK,CAAC,EACxC;IAACD,KAAK,EAAG,OAAO;IAACC,UAAU,EAAC;EAAK,CAAC,EAClC;IAACD,KAAK,EAAG,MAAM;IAACC,UAAU,EAAC;EAAK,CAAC;AAGzC,CAAC,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}