{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\nicola-portfolio\\\\nicola-portfolio\\\\src\\\\components\\\\AboutMe\\\\AboutCard\\\\AboutCard.jsx\";\nimport React from 'react';\nimport './AboutCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AboutCard = ({\n  icon,\n  title,\n  items\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"about-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"about-card-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"about-card-icon\",\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"about-card-title\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"about-card-content\",\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"about-card-list\",\n        children: items.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"about-card-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bullet\",\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 15\n          }, this), item]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = AboutCard;\nexport default AboutCard;\nvar _c;\n$RefreshReg$(_c, \"AboutCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AboutCard", "icon", "title", "items", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/components/AboutMe/AboutCard/AboutCard.jsx"], "sourcesContent": ["import React from 'react';\nimport './AboutCard.css';\n\nconst AboutCard = ({ icon, title, items }) => {\n  return (\n    <div className=\"about-card\">\n      <div className=\"about-card-header\">\n        <span className=\"about-card-icon\">{icon}</span>\n        <h4 className=\"about-card-title\">{title}</h4>\n      </div>\n      <div className=\"about-card-content\">\n        <ul className=\"about-card-list\">\n          {items.map((item, index) => (\n            <li key={index} className=\"about-card-item\">\n              <span className=\"bullet\">•</span>\n              {item}\n            </li>\n          ))}\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default AboutCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC;AAAM,CAAC,KAAK;EAC5C,oBACEJ,OAAA;IAAKK,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACzBN,OAAA;MAAKK,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCN,OAAA;QAAMK,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEJ;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/CV,OAAA;QAAIK,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAEH;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eACNV,OAAA;MAAKK,SAAS,EAAC,oBAAoB;MAAAC,QAAA,eACjCN,OAAA;QAAIK,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC5BF,KAAK,CAACO,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBb,OAAA;UAAgBK,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBACzCN,OAAA;YAAMK,SAAS,EAAC,QAAQ;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAChCE,IAAI;QAAA,GAFEC,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGV,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACI,EAAA,GAnBIb,SAAS;AAqBf,eAAeA,SAAS;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}