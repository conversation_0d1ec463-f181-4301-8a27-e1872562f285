.skills-card {
    border-radius: 0.65rem;
    border: 1px solid var(--primary-color);
    padding: 2.5rem;
    text-align: center;
    background: rgba(22, 17, 47, 0.398);
    backdrop-filter: blur(1rem);
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .skills-card:hover,
  .skills-card.active {
    background: var(--gradient);
  }
  
  .skills-card span {
    font-weight: 500;
    font-size: 1.3rem;
    color: var(--text-color); /* Use text color variable */
  }
  
  .skill-icon {
    width: 6rem;
    height: 6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem; /* Center the icon */
  }

  .skill-icon img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
  }
