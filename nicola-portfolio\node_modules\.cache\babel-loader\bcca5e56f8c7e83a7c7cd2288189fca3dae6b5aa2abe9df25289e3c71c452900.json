{"ast": null, "code": "class _BufferStack {\n  constructor() {\n    this.float32Array = null;\n    this.uint16Array = null;\n    this.uint32Array = null;\n    const stack = [];\n    let prevBuffer = null;\n    this.setBuffer = buffer => {\n      if (prevBuffer) {\n        stack.push(prevBuffer);\n      }\n      prevBuffer = buffer;\n      this.float32Array = new Float32Array(buffer);\n      this.uint16Array = new Uint16Array(buffer);\n      this.uint32Array = new Uint32Array(buffer);\n    };\n    this.clearBuffer = () => {\n      prevBuffer = null;\n      this.float32Array = null;\n      this.uint16Array = null;\n      this.uint32Array = null;\n      if (stack.length !== 0) {\n        this.setBuffer(stack.pop());\n      }\n    };\n  }\n}\nexport const BufferStack = new _BufferStack();", "map": {"version": 3, "names": ["_BufferStack", "constructor", "float32Array", "uint16Array", "uint32Array", "stack", "prevBuffer", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "push", "Float32Array", "Uint16Array", "Uint32Array", "<PERSON><PERSON><PERSON><PERSON>", "length", "pop", "<PERSON><PERSON><PERSON><PERSON>ta<PERSON>"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/node_modules/three-mesh-bvh/src/core/utils/BufferStack.js"], "sourcesContent": ["class _BufferStack {\n\n\tconstructor() {\n\n\t\tthis.float32Array = null;\n\t\tthis.uint16Array = null;\n\t\tthis.uint32Array = null;\n\n\t\tconst stack = [];\n\t\tlet prevBuffer = null;\n\t\tthis.setBuffer = buffer => {\n\n\t\t\tif ( prevBuffer ) {\n\n\t\t\t\tstack.push( prevBuffer );\n\n\t\t\t}\n\n\t\t\tprevBuffer = buffer;\n\t\t\tthis.float32Array = new Float32Array( buffer );\n\t\t\tthis.uint16Array = new Uint16Array( buffer );\n\t\t\tthis.uint32Array = new Uint32Array( buffer );\n\n\t\t};\n\n\t\tthis.clearBuffer = () => {\n\n\t\t\tprevBuffer = null;\n\t\t\tthis.float32Array = null;\n\t\t\tthis.uint16Array = null;\n\t\t\tthis.uint32Array = null;\n\n\t\t\tif ( stack.length !== 0 ) {\n\n\t\t\t\tthis.setBuffer( stack.pop() );\n\n\t\t\t}\n\n\t\t};\n\n\t}\n\n}\n\nexport const BufferStack = new _BufferStack();\n"], "mappings": "AAAA,MAAMA,YAAY,CAAC;EAElBC,WAAWA,CAAA,EAAG;IAEb,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,WAAW,GAAG,IAAI;IAEvB,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAIC,UAAU,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAGC,MAAM,IAAI;MAE1B,IAAKF,UAAU,EAAG;QAEjBD,KAAK,CAACI,IAAI,CAAEH,UAAW,CAAC;MAEzB;MAEAA,UAAU,GAAGE,MAAM;MACnB,IAAI,CAACN,YAAY,GAAG,IAAIQ,YAAY,CAAEF,MAAO,CAAC;MAC9C,IAAI,CAACL,WAAW,GAAG,IAAIQ,WAAW,CAAEH,MAAO,CAAC;MAC5C,IAAI,CAACJ,WAAW,GAAG,IAAIQ,WAAW,CAAEJ,MAAO,CAAC;IAE7C,CAAC;IAED,IAAI,CAACK,WAAW,GAAG,MAAM;MAExBP,UAAU,GAAG,IAAI;MACjB,IAAI,CAACJ,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,WAAW,GAAG,IAAI;MACvB,IAAI,CAACC,WAAW,GAAG,IAAI;MAEvB,IAAKC,KAAK,CAACS,MAAM,KAAK,CAAC,EAAG;QAEzB,IAAI,CAACP,SAAS,CAAEF,KAAK,CAACU,GAAG,CAAC,CAAE,CAAC;MAE9B;IAED,CAAC;EAEF;AAED;AAEA,OAAO,MAAMC,WAAW,GAAG,IAAIhB,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}