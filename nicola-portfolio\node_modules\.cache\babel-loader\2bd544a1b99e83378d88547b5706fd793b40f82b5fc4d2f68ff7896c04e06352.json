{"ast": null, "code": "import React,{useState}from\"react\";import\"./Navbar.css\";import MobileNavbar from\"./MobileNavbar/MobileNavbar\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Navbar=_ref=>{let{toggleTheme,isDarkMode}=_ref;const[openMenu,setOpenMenu]=useState(false);const[activeLink,setActiveLink]=useState(\"home\");const toggleMenu=()=>{setOpenMenu(!openMenu);};const downloadButton=()=>{try{const link=document.createElement(\"a\");link.href=\"/images/Nicola-Fadoul-Resume.pdf\";link.download=\"Nicola-Fadoul-Resume.pdf\";document.body.appendChild(link);link.click();document.body.removeChild(link);}catch(error){console.error(\"Error downloading the file:\",error);window.open(\"/images/Nicola-Fadoul-Resume.pdf\",\"_blank\");}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(MobileNavbar,{isOpen:openMenu,toggleMenu:toggleMenu,activeLink:activeLink,setActiveLink:setActiveLink}),/*#__PURE__*/_jsx(\"nav\",{className:\"nav-wrapper\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"nav-content\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:toggleTheme,className:\"theme-toggle\",children:isDarkMode?\"🌞 Light Mode\":\"🌙 Dark Mode\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"#home\",className:`menu-item ${activeLink===\"home\"?\"active\":\"\"}`,onClick:()=>setActiveLink(\"home\"),children:\"Home\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"#skills\",className:`menu-item ${activeLink===\"skills\"?\"active\":\"\"}`,onClick:()=>setActiveLink(\"skills\"),children:\"Skills\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"#projects\",className:`menu-item ${activeLink===\"projects\"?\"active\":\"\"}`,onClick:()=>setActiveLink(\"projects\"),children:\"Projects\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"a\",{href:\"#contact-me\",className:`menu-item ${activeLink===\"contact-me\"?\"active\":\"\"}`,onClick:()=>setActiveLink(\"contact-me\"),children:\"Contact Me\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"resume-dropdown\",children:/*#__PURE__*/_jsx(\"button\",{className:\"contact-btn\",onClick:downloadButton,children:\"Resume\"})})]}),/*#__PURE__*/_jsx(\"button\",{className:\"menu-btn\",onClick:toggleMenu,\"aria-label\":\"Toggle Menu\",children:/*#__PURE__*/_jsx(\"span\",{className:\"material-symbols-outlined\",style:{fontSize:\"1.8rem\"},children:\"menu\"})})]})})]});};export default Navbar;", "map": {"version": 3, "names": ["React", "useState", "MobileNavbar", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_ref", "toggleTheme", "isDarkMode", "openMenu", "setOpenMenu", "activeLink", "setActiveLink", "toggleMenu", "downloadButton", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "error", "console", "window", "open", "children", "isOpen", "className", "onClick", "style", "fontSize"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/components/Navbar/Navbar.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"./Navbar.css\";\r\nimport MobileNavbar from \"./MobileNavbar/MobileNavbar\";\r\n\r\nconst Navbar = ({toggleTheme, isDarkMode}) => {\r\n  const [openMenu, setOpenMenu] = useState(false);\r\n  const [activeLink, setActiveLink] = useState(\"home\");\r\n\r\n  const toggleMenu = () => {\r\n    setOpenMenu(!openMenu);\r\n  };\r\n\r\n  const downloadButton = () => {\r\n    try {\r\n\r\n      const link = document.createElement(\"a\");\r\n      link.href = \"/images/Nicola-Fadoul-Resume.pdf\";\r\n      link.download = \"Nicola-Fadoul-Resume.pdf\";\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n    } catch (error) {\r\n      console.error(\"Error downloading the file:\", error);\r\n      window.open(\"/images/Nicola-Fadoul-Resume.pdf\", \"_blank\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <MobileNavbar\r\n        isOpen={openMenu}\r\n        toggleMenu={toggleMenu}\r\n        activeLink={activeLink}\r\n        setActiveLink={setActiveLink}\r\n      />\r\n      <nav className=\"nav-wrapper\">\r\n        <div className=\"nav-content\">\r\n        <button onClick={toggleTheme} className=\"theme-toggle\">\r\n          {isDarkMode ? \"🌞 Light Mode\" : \"🌙 Dark Mode\"}\r\n        </button>\r\n          <ul>\r\n            <li>\r\n              <a\r\n                href=\"#home\"\r\n                className={`menu-item ${activeLink === \"home\" ? \"active\" : \"\"}`}\r\n                onClick={() => setActiveLink(\"home\")}\r\n              >\r\n                Home\r\n              </a>\r\n            </li>\r\n            <li>\r\n              <a\r\n                href=\"#skills\"\r\n                className={`menu-item ${activeLink === \"skills\" ? \"active\" : \"\"}`}\r\n                onClick={() => setActiveLink(\"skills\")}\r\n              >\r\n                Skills\r\n              </a>\r\n            </li>\r\n            <li>\r\n              <a\r\n                href=\"#projects\"\r\n                className={`menu-item ${activeLink === \"projects\" ? \"active\" : \"\"}`}\r\n                onClick={() => setActiveLink(\"projects\")}\r\n              >\r\n                Projects\r\n              </a>\r\n            </li>\r\n            <li>\r\n              <a\r\n                href=\"#contact-me\"\r\n                className={`menu-item ${activeLink === \"contact-me\" ? \"active\" : \"\"}`}\r\n                onClick={() => setActiveLink(\"contact-me\")}\r\n              >\r\n                Contact Me\r\n              </a>\r\n            </li>\r\n            <div className=\"resume-dropdown\">\r\n              <button className=\"contact-btn\" onClick={downloadButton}>\r\n              Resume\r\n              </button>\r\n            </div>\r\n          </ul>\r\n          <button className=\"menu-btn\" onClick={toggleMenu} aria-label=\"Toggle Menu\">\r\n            <span className=\"material-symbols-outlined\" style={{ fontSize: \"1.8rem\" }}>\r\n              menu\r\n            </span>\r\n          </button>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Navbar;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,cAAc,CACrB,MAAO,CAAAC,YAAY,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvD,KAAM,CAAAC,MAAM,CAAGC,IAAA,EAA+B,IAA9B,CAACC,WAAW,CAAEC,UAAU,CAAC,CAAAF,IAAA,CACvC,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACc,UAAU,CAAEC,aAAa,CAAC,CAAGf,QAAQ,CAAC,MAAM,CAAC,CAEpD,KAAM,CAAAgB,UAAU,CAAGA,CAAA,GAAM,CACvBH,WAAW,CAAC,CAACD,QAAQ,CAAC,CACxB,CAAC,CAED,KAAM,CAAAK,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI,CAEF,KAAM,CAAAC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAG,kCAAkC,CAC9CH,IAAI,CAACI,QAAQ,CAAG,0BAA0B,CAC1CH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC,CACjC,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDE,MAAM,CAACC,IAAI,CAAC,kCAAkC,CAAE,QAAQ,CAAC,CAC3D,CACF,CAAC,CAED,mBACEzB,KAAA,CAAAE,SAAA,EAAAwB,QAAA,eACE5B,IAAA,CAACF,YAAY,EACX+B,MAAM,CAAEpB,QAAS,CACjBI,UAAU,CAAEA,UAAW,CACvBF,UAAU,CAAEA,UAAW,CACvBC,aAAa,CAAEA,aAAc,CAC9B,CAAC,cACFZ,IAAA,QAAK8B,SAAS,CAAC,aAAa,CAAAF,QAAA,cAC1B1B,KAAA,QAAK4B,SAAS,CAAC,aAAa,CAAAF,QAAA,eAC5B5B,IAAA,WAAQ+B,OAAO,CAAExB,WAAY,CAACuB,SAAS,CAAC,cAAc,CAAAF,QAAA,CACnDpB,UAAU,CAAG,eAAe,CAAG,cAAc,CACxC,CAAC,cACPN,KAAA,OAAA0B,QAAA,eACE5B,IAAA,OAAA4B,QAAA,cACE5B,IAAA,MACEkB,IAAI,CAAC,OAAO,CACZY,SAAS,CAAE,aAAanB,UAAU,GAAK,MAAM,CAAG,QAAQ,CAAG,EAAE,EAAG,CAChEoB,OAAO,CAAEA,CAAA,GAAMnB,aAAa,CAAC,MAAM,CAAE,CAAAgB,QAAA,CACtC,MAED,CAAG,CAAC,CACF,CAAC,cACL5B,IAAA,OAAA4B,QAAA,cACE5B,IAAA,MACEkB,IAAI,CAAC,SAAS,CACdY,SAAS,CAAE,aAAanB,UAAU,GAAK,QAAQ,CAAG,QAAQ,CAAG,EAAE,EAAG,CAClEoB,OAAO,CAAEA,CAAA,GAAMnB,aAAa,CAAC,QAAQ,CAAE,CAAAgB,QAAA,CACxC,QAED,CAAG,CAAC,CACF,CAAC,cACL5B,IAAA,OAAA4B,QAAA,cACE5B,IAAA,MACEkB,IAAI,CAAC,WAAW,CAChBY,SAAS,CAAE,aAAanB,UAAU,GAAK,UAAU,CAAG,QAAQ,CAAG,EAAE,EAAG,CACpEoB,OAAO,CAAEA,CAAA,GAAMnB,aAAa,CAAC,UAAU,CAAE,CAAAgB,QAAA,CAC1C,UAED,CAAG,CAAC,CACF,CAAC,cACL5B,IAAA,OAAA4B,QAAA,cACE5B,IAAA,MACEkB,IAAI,CAAC,aAAa,CAClBY,SAAS,CAAE,aAAanB,UAAU,GAAK,YAAY,CAAG,QAAQ,CAAG,EAAE,EAAG,CACtEoB,OAAO,CAAEA,CAAA,GAAMnB,aAAa,CAAC,YAAY,CAAE,CAAAgB,QAAA,CAC5C,YAED,CAAG,CAAC,CACF,CAAC,cACL5B,IAAA,QAAK8B,SAAS,CAAC,iBAAiB,CAAAF,QAAA,cAC9B5B,IAAA,WAAQ8B,SAAS,CAAC,aAAa,CAACC,OAAO,CAAEjB,cAAe,CAAAc,QAAA,CAAC,QAEzD,CAAQ,CAAC,CACN,CAAC,EACJ,CAAC,cACL5B,IAAA,WAAQ8B,SAAS,CAAC,UAAU,CAACC,OAAO,CAAElB,UAAW,CAAC,aAAW,aAAa,CAAAe,QAAA,cACxE5B,IAAA,SAAM8B,SAAS,CAAC,2BAA2B,CAACE,KAAK,CAAE,CAAEC,QAAQ,CAAE,QAAS,CAAE,CAAAL,QAAA,CAAC,MAE3E,CAAM,CAAC,CACD,CAAC,EACN,CAAC,CACH,CAAC,EACN,CAAC,CAEP,CAAC,CAED,cAAe,CAAAvB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}