{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\nicola-portfolio\\\\nicola-portfolio\\\\src\\\\components\\\\Projects\\\\Projects.jsx\";\nimport React from \"react\";\nimport \"./Projects.css\";\nimport ProjectCard from \"./ProjectCard/ProjectCard\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Projects = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"projects\",\n    className: \"projects-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n      children: \"My Projects\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"projects-content\",\n      children: [/*#__PURE__*/_jsxDEV(ProjectCard, {\n        imageUrl: `${process.env.PUBLIC_URL}/images/three.png`,\n        title: \"3D Boat Simulation\",\n        description: \"Created a 3D boat simulation using Three.js, handling physics and modeling independently. This project demonstrates advanced 3D graphics and interactive simulation skills.\",\n        reverse: false,\n        ProjectUrl: \"https://github.com/Nicholass206/Boat-3js\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProjectCard, {\n        imageUrl: `${process.env.PUBLIC_URL}/images/church-image.jpg`,\n        title: \"Alessandra Parisi Portfolio\",\n        description: \"Developed a responsive portfolio website using React.js to showcase Alessandra Parisi\\u2019s collection of holy icons and religious artwork.\",\n        reverse: true,\n        ProjectUrl: \"https://alessandraparisi.it\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProjectCard, {\n        imageUrl: `${process.env.PUBLIC_URL}/images/pierre4ad-logo.jpg`,\n        title: \"Pierre4Ad\",\n        description: \"Contributed to a freelance project by building responsive web pages with HTML, CSS, and JavaScript, focusing on user-friendly design and functionality.\",\n        reverse: false,\n        ProjectUrl: \"https://pierre4ad.com/\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProjectCard, {\n        imageUrl: `${process.env.PUBLIC_URL}/images/passport-image`,\n        title: \"Passports-IDs Scanner\",\n        description: \"Integrated a Flutter SDK to scan documents like (Passports, IDs, Driver's License etc..) using MRZ/, check validity of these documents \",\n        reverse: true,\n        ProjectUrl: \"https://gitlab.com/fatora1/Scanner-Flutter-App\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProjectCard, {\n        imageUrl: `${process.env.PUBLIC_URL}/images/athletics-dxb.png`,\n        title: \"AthleticsDXB\",\n        description: \"Designed and developed a full-featured athlete management system from the ground up using Flutter for the front end and ASP.NET Core for the back end.\",\n        reverse: false,\n        ProjectUrl: \"https://gitlab.com/fatora1/*********************\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = Projects;\nexport default Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["React", "ProjectCard", "jsxDEV", "_jsxDEV", "Projects", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "imageUrl", "process", "env", "PUBLIC_URL", "title", "description", "reverse", "ProjectUrl", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/components/Projects/Projects.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport \"./Projects.css\";\r\nimport ProjectCard from \"./ProjectCard/ProjectCard\";\r\n\r\nconst Projects = () => {\r\n  return (\r\n    <section id=\"projects\" className=\"projects-container\">\r\n      <h5>My Projects</h5>\r\n      <div className=\"projects-content\">\r\n        {/* <ProjectCard\r\n                imageUrl={`${process.env.PUBLIC_URL}/images/Medicure with name.png`}\r\n                title=\" Medicure \"\r\n                description=\"Built a Laravel-based system for managing pharmacy inventory and orders, streamlining warehouse operations with efficient backend functionality and database management.\"\r\n                reverse={false}  \r\n                ProjectUrl=\"https://github.com/Nicholass206/Pharmacy-Warehouse/tree/first\"\r\n            /> */}\r\n        <ProjectCard\r\n          imageUrl={`${process.env.PUBLIC_URL}/images/three.png`}\r\n          title=\"3D Boat Simulation\"\r\n          description=\"Created a 3D boat simulation using Three.js, handling physics and modeling independently. This project demonstrates advanced 3D graphics and interactive simulation skills.\"\r\n          reverse={false}\r\n          ProjectUrl=\"https://github.com/Nicholass206/Boat-3js\"\r\n        />\r\n        <ProjectCard\r\n          imageUrl={`${process.env.PUBLIC_URL}/images/church-image.jpg`}\r\n          title=\"Alessandra Parisi Portfolio\"\r\n          description=\"Developed a responsive portfolio website using React.js to showcase Alessandra Parisi’s collection of holy icons and religious artwork.\"\r\n          reverse={true}\r\n          ProjectUrl=\"https://alessandraparisi.it\"\r\n        />\r\n        <ProjectCard\r\n          imageUrl={`${process.env.PUBLIC_URL}/images/pierre4ad-logo.jpg`}\r\n          title=\"Pierre4Ad\"\r\n          description=\"Contributed to a freelance project by building responsive web pages with HTML, CSS, and JavaScript, focusing on user-friendly design and functionality.\"\r\n          reverse={false}\r\n          ProjectUrl=\"https://pierre4ad.com/\"\r\n        />\r\n        <ProjectCard\r\n          imageUrl={`${process.env.PUBLIC_URL}/images/passport-image`}\r\n          title=\"Passports-IDs Scanner\"\r\n          description=\"Integrated a Flutter SDK to scan documents like (Passports, IDs, Driver's License etc..) using MRZ/, check validity of these documents \"\r\n          reverse={true}\r\n          ProjectUrl=\"https://gitlab.com/fatora1/Scanner-Flutter-App\"\r\n        />\r\n        <ProjectCard\r\n          imageUrl={`${process.env.PUBLIC_URL}/images/athletics-dxb.png`}\r\n          title=\"AthleticsDXB\"\r\n          description=\"Designed and developed a full-featured athlete management system from the ground up using Flutter for the front end and ASP.NET Core for the back end.\"\r\n          reverse={false}\r\n          ProjectUrl=\"https://gitlab.com/fatora1/*********************\"\r\n        />\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Projects;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,gBAAgB;AACvB,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACED,OAAA;IAASE,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACnDJ,OAAA;MAAAI,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpBR,OAAA;MAAKG,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAQ/BJ,OAAA,CAACF,WAAW;QACVW,QAAQ,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,mBAAoB;QACvDC,KAAK,EAAC,oBAAoB;QAC1BC,WAAW,EAAC,6KAA6K;QACzLC,OAAO,EAAE,KAAM;QACfC,UAAU,EAAC;MAA0C;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eACFR,OAAA,CAACF,WAAW;QACVW,QAAQ,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,0BAA2B;QAC9DC,KAAK,EAAC,6BAA6B;QACnCC,WAAW,EAAC,8IAAyI;QACrJC,OAAO,EAAE,IAAK;QACdC,UAAU,EAAC;MAA6B;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eACFR,OAAA,CAACF,WAAW;QACVW,QAAQ,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,4BAA6B;QAChEC,KAAK,EAAC,WAAW;QACjBC,WAAW,EAAC,yJAAyJ;QACrKC,OAAO,EAAE,KAAM;QACfC,UAAU,EAAC;MAAwB;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACFR,OAAA,CAACF,WAAW;QACVW,QAAQ,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,wBAAyB;QAC5DC,KAAK,EAAC,uBAAuB;QAC7BC,WAAW,EAAC,yIAAyI;QACrJC,OAAO,EAAE,IAAK;QACdC,UAAU,EAAC;MAAgD;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACFR,OAAA,CAACF,WAAW;QACVW,QAAQ,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,2BAA4B;QAC/DC,KAAK,EAAC,cAAc;QACpBC,WAAW,EAAC,wJAAwJ;QACpKC,OAAO,EAAE,KAAM;QACfC,UAAU,EAAC;MAAkD;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACS,EAAA,GAlDIhB,QAAQ;AAoDd,eAAeA,QAAQ;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}