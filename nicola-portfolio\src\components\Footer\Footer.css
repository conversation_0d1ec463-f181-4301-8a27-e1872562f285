.footer-container {
  margin-top: 6rem;
  position: relative;
  background: rgba(22, 17, 47, 0.2);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-container::after,
.footer-container::before {
  content: " ";
  width: 20rem;
  height: 28rem;
  border-radius: 28.125rem;
  background: var(--primary-color);
  position: absolute;
  z-index: -1;
  filter: blur(200px);
}

.footer-container::after {
  top: -10rem;
  right: -5rem;
}

.footer-container::before {
  background: var(--secondary-color);
  bottom: -10rem;
  left: -5rem;
}

.footer-content {
  width: 100%;
  padding: 2.5rem 0 1.5rem;
}

.footer-main {
  display: flex;
  gap: 3rem;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 0 2rem;
}

.footer-info {
  flex: 1;
  max-width: 50%;
}

.footer-info h3 {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 1rem;
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-info > p {
  font-size: 0.9rem;
  line-height: 1.6;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.footer-quote {
  padding: 1rem;
  background: rgba(22, 17, 47, 0.4);
  backdrop-filter: blur(10px);
  border-radius: 0.65rem;
  border: 1px solid var(--primary-color);
  position: relative;
}

.footer-quote p {
  font-style: italic;
  font-size: 0.95rem;
  color: var(--text-color);
  margin: 0;
  text-align: center;
}

.footer-social {
  flex: 1;
}

.footer-social h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem;
  background: rgba(22, 17, 47, 0.3);
  backdrop-filter: blur(10px);
  border-radius: 0.65rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.social-link:hover {
  transform: translateY(-3px);
  border-color: var(--primary-color);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.social-link:hover .social-icon {
  background: var(--gradient);
  transform: scale(1.1);
}

.social-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  font-size: 1rem;
}

.social-info {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.social-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
}

.social-desc {
  font-size: 0.85rem;
  color: var(--text-color);
  opacity: 0.7;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem 2rem 0;
}

.footer-divider {
  width: 100%;
  height: 1px;
  background: var(--gradient);
  margin-bottom: 2rem;
  opacity: 0.3;
}

.footer-copyright {
  text-align: center;
}

.footer-copyright p {
  font-size: 0.9rem;
  color: var(--text-color);
  margin: 0.5rem 0;
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 1025px) {
  .footer-content {
    padding: 2rem 0 1rem;
  }

  .footer-main {
    gap: 2.5rem;
    padding: 0 1.5rem;
  }

  .footer-info h3 {
    font-size: 1.5rem;
  }

  .footer-bottom {
    padding: 1.2rem 1.5rem 0;
  }
}

@media (max-width: 768px) {
  .footer-main {
    flex-direction: column;
    gap: 2rem;
    padding: 0 1rem;
  }

  .footer-info {
    max-width: 100%;
  }

  .footer-info h3 {
    font-size: 1.4rem;
    text-align: center;
  }

  .footer-social h4 {
    font-size: 1.1rem;
    text-align: center;
  }

  .footer-bottom {
    padding: 1rem 1rem 0;
  }

  .footer-container::after,
  .footer-container::before {
    width: 18rem;
    height: 18rem;
  }
}

@media (max-width: 480px) {
  .footer-content {
    padding: 1.5rem 0 0.8rem;
  }

  .footer-main {
    gap: 1.5rem;
    padding: 0 0.8rem;
  }

  .footer-info h3 {
    font-size: 1.3rem;
  }

  .footer-info > p {
    font-size: 0.85rem;
  }

  .footer-quote {
    padding: 0.8rem;
  }

  .footer-quote p {
    font-size: 0.9rem;
  }

  .footer-bottom {
    padding: 1rem 0.8rem 0;
  }

  .social-link {
    padding: 0.7rem;
    gap: 0.7rem;
  }

  .social-icon {
    width: 2.2rem;
    height: 2.2rem;
    font-size: 0.9rem;
  }

  .social-name {
    font-size: 0.85rem;
  }

  .social-desc {
    font-size: 0.75rem;
  }
}
