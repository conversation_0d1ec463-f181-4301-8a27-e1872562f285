{"ast": null, "code": "import { Vector3, Color, SphericalHarmonics3, LightProbe } from \"three\";\nconst LightProbeGenerator = {\n  // https://www.ppsloan.org/publications/StupidSH36.pdf\n  fromCubeTexture(cubeTexture) {\n    let totalWeight = 0;\n    const coord = new Vector3();\n    const dir = new Vector3();\n    const color = new Color();\n    const shBasis = [0, 0, 0, 0, 0, 0, 0, 0, 0];\n    const sh = new SphericalHarmonics3();\n    const shCoefficients = sh.coefficients;\n    for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\n      const image = cubeTexture.image[faceIndex];\n      const width = image.width;\n      const height = image.height;\n      const canvas = document.createElement(\"canvas\");\n      canvas.width = width;\n      canvas.height = height;\n      const context = canvas.getContext(\"2d\");\n      context.drawImage(image, 0, 0, width, height);\n      const imageData = context.getImageData(0, 0, width, height);\n      const data = imageData.data;\n      const imageWidth = imageData.width;\n      const pixelSize = 2 / imageWidth;\n      for (let i = 0, il = data.length; i < il; i += 4) {\n        color.setRGB(data[i] / 255, data[i + 1] / 255, data[i + 2] / 255);\n        if (\"colorSpace\" in cubeTexture) {\n          if (cubeTexture.colorSpace === \"srgb\") {\n            color.convertSRGBToLinear();\n          }\n        } else if (cubeTexture.encoding === 3001) {\n          color.convertSRGBToLinear();\n        }\n        const pixelIndex = i / 4;\n        const col = -1 + (pixelIndex % imageWidth + 0.5) * pixelSize;\n        const row = 1 - (Math.floor(pixelIndex / imageWidth) + 0.5) * pixelSize;\n        switch (faceIndex) {\n          case 0:\n            coord.set(-1, row, -col);\n            break;\n          case 1:\n            coord.set(1, row, col);\n            break;\n          case 2:\n            coord.set(-col, 1, -row);\n            break;\n          case 3:\n            coord.set(-col, -1, row);\n            break;\n          case 4:\n            coord.set(-col, row, 1);\n            break;\n          case 5:\n            coord.set(col, row, -1);\n            break;\n        }\n        const lengthSq = coord.lengthSq();\n        const weight = 4 / (Math.sqrt(lengthSq) * lengthSq);\n        totalWeight += weight;\n        dir.copy(coord).normalize();\n        SphericalHarmonics3.getBasisAt(dir, shBasis);\n        for (let j = 0; j < 9; j++) {\n          shCoefficients[j].x += shBasis[j] * color.r * weight;\n          shCoefficients[j].y += shBasis[j] * color.g * weight;\n          shCoefficients[j].z += shBasis[j] * color.b * weight;\n        }\n      }\n    }\n    const norm = 4 * Math.PI / totalWeight;\n    for (let j = 0; j < 9; j++) {\n      shCoefficients[j].x *= norm;\n      shCoefficients[j].y *= norm;\n      shCoefficients[j].z *= norm;\n    }\n    return new LightProbe(sh);\n  },\n  fromCubeRenderTarget(renderer, cubeRenderTarget) {\n    let totalWeight = 0;\n    const coord = new Vector3();\n    const dir = new Vector3();\n    const color = new Color();\n    const shBasis = [0, 0, 0, 0, 0, 0, 0, 0, 0];\n    const sh = new SphericalHarmonics3();\n    const shCoefficients = sh.coefficients;\n    for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\n      const imageWidth = cubeRenderTarget.width;\n      const data = new Uint8Array(imageWidth * imageWidth * 4);\n      renderer.readRenderTargetPixels(cubeRenderTarget, 0, 0, imageWidth, imageWidth, data, faceIndex);\n      const pixelSize = 2 / imageWidth;\n      for (let i = 0, il = data.length; i < il; i += 4) {\n        color.setRGB(data[i] / 255, data[i + 1] / 255, data[i + 2] / 255);\n        if (\"colorSpace\" in cubeRenderTarget.texture) {\n          if (cubeRenderTarget.texture.colorSpace === \"srgb\") {\n            color.convertSRGBToLinear();\n          }\n        } else if (cubeRenderTarget.texture.encoding === 3001) {\n          color.convertSRGBToLinear();\n        }\n        const pixelIndex = i / 4;\n        const col = -1 + (pixelIndex % imageWidth + 0.5) * pixelSize;\n        const row = 1 - (Math.floor(pixelIndex / imageWidth) + 0.5) * pixelSize;\n        switch (faceIndex) {\n          case 0:\n            coord.set(1, row, -col);\n            break;\n          case 1:\n            coord.set(-1, row, col);\n            break;\n          case 2:\n            coord.set(col, 1, -row);\n            break;\n          case 3:\n            coord.set(col, -1, row);\n            break;\n          case 4:\n            coord.set(col, row, 1);\n            break;\n          case 5:\n            coord.set(-col, row, -1);\n            break;\n        }\n        const lengthSq = coord.lengthSq();\n        const weight = 4 / (Math.sqrt(lengthSq) * lengthSq);\n        totalWeight += weight;\n        dir.copy(coord).normalize();\n        SphericalHarmonics3.getBasisAt(dir, shBasis);\n        for (let j = 0; j < 9; j++) {\n          shCoefficients[j].x += shBasis[j] * color.r * weight;\n          shCoefficients[j].y += shBasis[j] * color.g * weight;\n          shCoefficients[j].z += shBasis[j] * color.b * weight;\n        }\n      }\n    }\n    const norm = 4 * Math.PI / totalWeight;\n    for (let j = 0; j < 9; j++) {\n      shCoefficients[j].x *= norm;\n      shCoefficients[j].y *= norm;\n      shCoefficients[j].z *= norm;\n    }\n    return new LightProbe(sh);\n  }\n};\nexport { LightProbeGenerator };", "map": {"version": 3, "names": ["LightProbeGenerator", "fromCubeTexture", "cubeTexture", "totalWeight", "coord", "Vector3", "dir", "color", "Color", "shBasis", "sh", "SphericalHarmonics3", "shCoefficients", "coefficients", "faceIndex", "image", "width", "height", "canvas", "document", "createElement", "context", "getContext", "drawImage", "imageData", "getImageData", "data", "imageWidth", "pixelSize", "i", "il", "length", "setRGB", "colorSpace", "convertSRGBToLinear", "encoding", "pixelIndex", "col", "row", "Math", "floor", "set", "lengthSq", "weight", "sqrt", "copy", "normalize", "getBasisAt", "j", "x", "r", "y", "g", "z", "b", "norm", "PI", "LightProbe", "fromCubeRenderTarget", "renderer", "cubeRenderTarget", "Uint8Array", "readRenderTargetPixels", "texture"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\lights\\LightProbeGenerator.js"], "sourcesContent": ["import { Color, LightProbe, SphericalHarmonics3, Vector3 } from 'three'\n\nconst LightProbeGenerator = {\n  // https://www.ppsloan.org/publications/StupidSH36.pdf\n  fromCubeTexture(cubeTexture) {\n    let totalWeight = 0\n\n    const coord = new Vector3()\n\n    const dir = new Vector3()\n\n    const color = new Color()\n\n    const shBasis = [0, 0, 0, 0, 0, 0, 0, 0, 0]\n\n    const sh = new SphericalHarmonics3()\n    const shCoefficients = sh.coefficients\n\n    for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\n      const image = cubeTexture.image[faceIndex]\n\n      const width = image.width\n      const height = image.height\n\n      const canvas = document.createElement('canvas')\n\n      canvas.width = width\n      canvas.height = height\n\n      const context = canvas.getContext('2d')\n\n      context.drawImage(image, 0, 0, width, height)\n\n      const imageData = context.getImageData(0, 0, width, height)\n\n      const data = imageData.data\n\n      const imageWidth = imageData.width // assumed to be square\n\n      const pixelSize = 2 / imageWidth\n\n      for (let i = 0, il = data.length; i < il; i += 4) {\n        // RGBA assumed\n\n        // pixel color\n        color.setRGB(data[i] / 255, data[i + 1] / 255, data[i + 2] / 255)\n\n        // convert to linear color space\n        if ('colorSpace' in cubeTexture) {\n          if (cubeTexture.colorSpace === 'srgb') {\n            color.convertSRGBToLinear()\n          }\n        } else if (cubeTexture.encoding === 3001) {\n          // sRGBEncoding\n          color.convertSRGBToLinear()\n        }\n\n        // pixel coordinate on unit cube\n\n        const pixelIndex = i / 4\n\n        const col = -1 + ((pixelIndex % imageWidth) + 0.5) * pixelSize\n\n        const row = 1 - (Math.floor(pixelIndex / imageWidth) + 0.5) * pixelSize\n\n        switch (faceIndex) {\n          case 0:\n            coord.set(-1, row, -col)\n            break\n\n          case 1:\n            coord.set(1, row, col)\n            break\n\n          case 2:\n            coord.set(-col, 1, -row)\n            break\n\n          case 3:\n            coord.set(-col, -1, row)\n            break\n\n          case 4:\n            coord.set(-col, row, 1)\n            break\n\n          case 5:\n            coord.set(col, row, -1)\n            break\n        }\n\n        // weight assigned to this pixel\n\n        const lengthSq = coord.lengthSq()\n\n        const weight = 4 / (Math.sqrt(lengthSq) * lengthSq)\n\n        totalWeight += weight\n\n        // direction vector to this pixel\n        dir.copy(coord).normalize()\n\n        // evaluate SH basis functions in direction dir\n        SphericalHarmonics3.getBasisAt(dir, shBasis)\n\n        // accummuulate\n        for (let j = 0; j < 9; j++) {\n          shCoefficients[j].x += shBasis[j] * color.r * weight\n          shCoefficients[j].y += shBasis[j] * color.g * weight\n          shCoefficients[j].z += shBasis[j] * color.b * weight\n        }\n      }\n    }\n\n    // normalize\n    const norm = (4 * Math.PI) / totalWeight\n\n    for (let j = 0; j < 9; j++) {\n      shCoefficients[j].x *= norm\n      shCoefficients[j].y *= norm\n      shCoefficients[j].z *= norm\n    }\n\n    return new LightProbe(sh)\n  },\n\n  fromCubeRenderTarget(renderer, cubeRenderTarget) {\n    // The renderTarget must be set to RGBA in order to make readRenderTargetPixels works\n    let totalWeight = 0\n\n    const coord = new Vector3()\n\n    const dir = new Vector3()\n\n    const color = new Color()\n\n    const shBasis = [0, 0, 0, 0, 0, 0, 0, 0, 0]\n\n    const sh = new SphericalHarmonics3()\n    const shCoefficients = sh.coefficients\n\n    for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\n      const imageWidth = cubeRenderTarget.width // assumed to be square\n      const data = new Uint8Array(imageWidth * imageWidth * 4)\n      renderer.readRenderTargetPixels(cubeRenderTarget, 0, 0, imageWidth, imageWidth, data, faceIndex)\n\n      const pixelSize = 2 / imageWidth\n\n      for (let i = 0, il = data.length; i < il; i += 4) {\n        // RGBA assumed\n\n        // pixel color\n        color.setRGB(data[i] / 255, data[i + 1] / 255, data[i + 2] / 255)\n\n        // convert to linear color space\n        if ('colorSpace' in cubeRenderTarget.texture) {\n          if (cubeRenderTarget.texture.colorSpace === 'srgb') {\n            color.convertSRGBToLinear()\n          }\n        } else if (cubeRenderTarget.texture.encoding === 3001) {\n          // sRGBEncoding\n          color.convertSRGBToLinear()\n        }\n\n        // pixel coordinate on unit cube\n\n        const pixelIndex = i / 4\n\n        const col = -1 + ((pixelIndex % imageWidth) + 0.5) * pixelSize\n\n        const row = 1 - (Math.floor(pixelIndex / imageWidth) + 0.5) * pixelSize\n\n        switch (faceIndex) {\n          case 0:\n            coord.set(1, row, -col)\n            break\n\n          case 1:\n            coord.set(-1, row, col)\n            break\n\n          case 2:\n            coord.set(col, 1, -row)\n            break\n\n          case 3:\n            coord.set(col, -1, row)\n            break\n\n          case 4:\n            coord.set(col, row, 1)\n            break\n\n          case 5:\n            coord.set(-col, row, -1)\n            break\n        }\n\n        // weight assigned to this pixel\n\n        const lengthSq = coord.lengthSq()\n\n        const weight = 4 / (Math.sqrt(lengthSq) * lengthSq)\n\n        totalWeight += weight\n\n        // direction vector to this pixel\n        dir.copy(coord).normalize()\n\n        // evaluate SH basis functions in direction dir\n        SphericalHarmonics3.getBasisAt(dir, shBasis)\n\n        // accummuulate\n        for (let j = 0; j < 9; j++) {\n          shCoefficients[j].x += shBasis[j] * color.r * weight\n          shCoefficients[j].y += shBasis[j] * color.g * weight\n          shCoefficients[j].z += shBasis[j] * color.b * weight\n        }\n      }\n    }\n\n    // normalize\n    const norm = (4 * Math.PI) / totalWeight\n\n    for (let j = 0; j < 9; j++) {\n      shCoefficients[j].x *= norm\n      shCoefficients[j].y *= norm\n      shCoefficients[j].z *= norm\n    }\n\n    return new LightProbe(sh)\n  },\n}\n\nexport { LightProbeGenerator }\n"], "mappings": ";AAEK,MAACA,mBAAA,GAAsB;EAAA;EAE1BC,gBAAgBC,WAAA,EAAa;IAC3B,IAAIC,WAAA,GAAc;IAElB,MAAMC,KAAA,GAAQ,IAAIC,OAAA,CAAS;IAE3B,MAAMC,GAAA,GAAM,IAAID,OAAA,CAAS;IAEzB,MAAME,KAAA,GAAQ,IAAIC,KAAA,CAAO;IAEzB,MAAMC,OAAA,GAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAE1C,MAAMC,EAAA,GAAK,IAAIC,mBAAA,CAAqB;IACpC,MAAMC,cAAA,GAAiBF,EAAA,CAAGG,YAAA;IAE1B,SAASC,SAAA,GAAY,GAAGA,SAAA,GAAY,GAAGA,SAAA,IAAa;MAClD,MAAMC,KAAA,GAAQb,WAAA,CAAYa,KAAA,CAAMD,SAAS;MAEzC,MAAME,KAAA,GAAQD,KAAA,CAAMC,KAAA;MACpB,MAAMC,MAAA,GAASF,KAAA,CAAME,MAAA;MAErB,MAAMC,MAAA,GAASC,QAAA,CAASC,aAAA,CAAc,QAAQ;MAE9CF,MAAA,CAAOF,KAAA,GAAQA,KAAA;MACfE,MAAA,CAAOD,MAAA,GAASA,MAAA;MAEhB,MAAMI,OAAA,GAAUH,MAAA,CAAOI,UAAA,CAAW,IAAI;MAEtCD,OAAA,CAAQE,SAAA,CAAUR,KAAA,EAAO,GAAG,GAAGC,KAAA,EAAOC,MAAM;MAE5C,MAAMO,SAAA,GAAYH,OAAA,CAAQI,YAAA,CAAa,GAAG,GAAGT,KAAA,EAAOC,MAAM;MAE1D,MAAMS,IAAA,GAAOF,SAAA,CAAUE,IAAA;MAEvB,MAAMC,UAAA,GAAaH,SAAA,CAAUR,KAAA;MAE7B,MAAMY,SAAA,GAAY,IAAID,UAAA;MAEtB,SAASE,CAAA,GAAI,GAAGC,EAAA,GAAKJ,IAAA,CAAKK,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;QAIhDtB,KAAA,CAAMyB,MAAA,CAAON,IAAA,CAAKG,CAAC,IAAI,KAAKH,IAAA,CAAKG,CAAA,GAAI,CAAC,IAAI,KAAKH,IAAA,CAAKG,CAAA,GAAI,CAAC,IAAI,GAAG;QAGhE,IAAI,gBAAgB3B,WAAA,EAAa;UAC/B,IAAIA,WAAA,CAAY+B,UAAA,KAAe,QAAQ;YACrC1B,KAAA,CAAM2B,mBAAA,CAAqB;UAC5B;QACX,WAAmBhC,WAAA,CAAYiC,QAAA,KAAa,MAAM;UAExC5B,KAAA,CAAM2B,mBAAA,CAAqB;QAC5B;QAID,MAAME,UAAA,GAAaP,CAAA,GAAI;QAEvB,MAAMQ,GAAA,GAAM,MAAOD,UAAA,GAAaT,UAAA,GAAc,OAAOC,SAAA;QAErD,MAAMU,GAAA,GAAM,KAAKC,IAAA,CAAKC,KAAA,CAAMJ,UAAA,GAAaT,UAAU,IAAI,OAAOC,SAAA;QAE9D,QAAQd,SAAA;UACN,KAAK;YACHV,KAAA,CAAMqC,GAAA,CAAI,IAAIH,GAAA,EAAK,CAACD,GAAG;YACvB;UAEF,KAAK;YACHjC,KAAA,CAAMqC,GAAA,CAAI,GAAGH,GAAA,EAAKD,GAAG;YACrB;UAEF,KAAK;YACHjC,KAAA,CAAMqC,GAAA,CAAI,CAACJ,GAAA,EAAK,GAAG,CAACC,GAAG;YACvB;UAEF,KAAK;YACHlC,KAAA,CAAMqC,GAAA,CAAI,CAACJ,GAAA,EAAK,IAAIC,GAAG;YACvB;UAEF,KAAK;YACHlC,KAAA,CAAMqC,GAAA,CAAI,CAACJ,GAAA,EAAKC,GAAA,EAAK,CAAC;YACtB;UAEF,KAAK;YACHlC,KAAA,CAAMqC,GAAA,CAAIJ,GAAA,EAAKC,GAAA,EAAK,EAAE;YACtB;QACH;QAID,MAAMI,QAAA,GAAWtC,KAAA,CAAMsC,QAAA,CAAU;QAEjC,MAAMC,MAAA,GAAS,KAAKJ,IAAA,CAAKK,IAAA,CAAKF,QAAQ,IAAIA,QAAA;QAE1CvC,WAAA,IAAewC,MAAA;QAGfrC,GAAA,CAAIuC,IAAA,CAAKzC,KAAK,EAAE0C,SAAA,CAAW;QAG3BnC,mBAAA,CAAoBoC,UAAA,CAAWzC,GAAA,EAAKG,OAAO;QAG3C,SAASuC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1BpC,cAAA,CAAeoC,CAAC,EAAEC,CAAA,IAAKxC,OAAA,CAAQuC,CAAC,IAAIzC,KAAA,CAAM2C,CAAA,GAAIP,MAAA;UAC9C/B,cAAA,CAAeoC,CAAC,EAAEG,CAAA,IAAK1C,OAAA,CAAQuC,CAAC,IAAIzC,KAAA,CAAM6C,CAAA,GAAIT,MAAA;UAC9C/B,cAAA,CAAeoC,CAAC,EAAEK,CAAA,IAAK5C,OAAA,CAAQuC,CAAC,IAAIzC,KAAA,CAAM+C,CAAA,GAAIX,MAAA;QAC/C;MACF;IACF;IAGD,MAAMY,IAAA,GAAQ,IAAIhB,IAAA,CAAKiB,EAAA,GAAMrD,WAAA;IAE7B,SAAS6C,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1BpC,cAAA,CAAeoC,CAAC,EAAEC,CAAA,IAAKM,IAAA;MACvB3C,cAAA,CAAeoC,CAAC,EAAEG,CAAA,IAAKI,IAAA;MACvB3C,cAAA,CAAeoC,CAAC,EAAEK,CAAA,IAAKE,IAAA;IACxB;IAED,OAAO,IAAIE,UAAA,CAAW/C,EAAE;EACzB;EAEDgD,qBAAqBC,QAAA,EAAUC,gBAAA,EAAkB;IAE/C,IAAIzD,WAAA,GAAc;IAElB,MAAMC,KAAA,GAAQ,IAAIC,OAAA,CAAS;IAE3B,MAAMC,GAAA,GAAM,IAAID,OAAA,CAAS;IAEzB,MAAME,KAAA,GAAQ,IAAIC,KAAA,CAAO;IAEzB,MAAMC,OAAA,GAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;IAE1C,MAAMC,EAAA,GAAK,IAAIC,mBAAA,CAAqB;IACpC,MAAMC,cAAA,GAAiBF,EAAA,CAAGG,YAAA;IAE1B,SAASC,SAAA,GAAY,GAAGA,SAAA,GAAY,GAAGA,SAAA,IAAa;MAClD,MAAMa,UAAA,GAAaiC,gBAAA,CAAiB5C,KAAA;MACpC,MAAMU,IAAA,GAAO,IAAImC,UAAA,CAAWlC,UAAA,GAAaA,UAAA,GAAa,CAAC;MACvDgC,QAAA,CAASG,sBAAA,CAAuBF,gBAAA,EAAkB,GAAG,GAAGjC,UAAA,EAAYA,UAAA,EAAYD,IAAA,EAAMZ,SAAS;MAE/F,MAAMc,SAAA,GAAY,IAAID,UAAA;MAEtB,SAASE,CAAA,GAAI,GAAGC,EAAA,GAAKJ,IAAA,CAAKK,MAAA,EAAQF,CAAA,GAAIC,EAAA,EAAID,CAAA,IAAK,GAAG;QAIhDtB,KAAA,CAAMyB,MAAA,CAAON,IAAA,CAAKG,CAAC,IAAI,KAAKH,IAAA,CAAKG,CAAA,GAAI,CAAC,IAAI,KAAKH,IAAA,CAAKG,CAAA,GAAI,CAAC,IAAI,GAAG;QAGhE,IAAI,gBAAgB+B,gBAAA,CAAiBG,OAAA,EAAS;UAC5C,IAAIH,gBAAA,CAAiBG,OAAA,CAAQ9B,UAAA,KAAe,QAAQ;YAClD1B,KAAA,CAAM2B,mBAAA,CAAqB;UAC5B;QACF,WAAU0B,gBAAA,CAAiBG,OAAA,CAAQ5B,QAAA,KAAa,MAAM;UAErD5B,KAAA,CAAM2B,mBAAA,CAAqB;QAC5B;QAID,MAAME,UAAA,GAAaP,CAAA,GAAI;QAEvB,MAAMQ,GAAA,GAAM,MAAOD,UAAA,GAAaT,UAAA,GAAc,OAAOC,SAAA;QAErD,MAAMU,GAAA,GAAM,KAAKC,IAAA,CAAKC,KAAA,CAAMJ,UAAA,GAAaT,UAAU,IAAI,OAAOC,SAAA;QAE9D,QAAQd,SAAA;UACN,KAAK;YACHV,KAAA,CAAMqC,GAAA,CAAI,GAAGH,GAAA,EAAK,CAACD,GAAG;YACtB;UAEF,KAAK;YACHjC,KAAA,CAAMqC,GAAA,CAAI,IAAIH,GAAA,EAAKD,GAAG;YACtB;UAEF,KAAK;YACHjC,KAAA,CAAMqC,GAAA,CAAIJ,GAAA,EAAK,GAAG,CAACC,GAAG;YACtB;UAEF,KAAK;YACHlC,KAAA,CAAMqC,GAAA,CAAIJ,GAAA,EAAK,IAAIC,GAAG;YACtB;UAEF,KAAK;YACHlC,KAAA,CAAMqC,GAAA,CAAIJ,GAAA,EAAKC,GAAA,EAAK,CAAC;YACrB;UAEF,KAAK;YACHlC,KAAA,CAAMqC,GAAA,CAAI,CAACJ,GAAA,EAAKC,GAAA,EAAK,EAAE;YACvB;QACH;QAID,MAAMI,QAAA,GAAWtC,KAAA,CAAMsC,QAAA,CAAU;QAEjC,MAAMC,MAAA,GAAS,KAAKJ,IAAA,CAAKK,IAAA,CAAKF,QAAQ,IAAIA,QAAA;QAE1CvC,WAAA,IAAewC,MAAA;QAGfrC,GAAA,CAAIuC,IAAA,CAAKzC,KAAK,EAAE0C,SAAA,CAAW;QAG3BnC,mBAAA,CAAoBoC,UAAA,CAAWzC,GAAA,EAAKG,OAAO;QAG3C,SAASuC,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;UAC1BpC,cAAA,CAAeoC,CAAC,EAAEC,CAAA,IAAKxC,OAAA,CAAQuC,CAAC,IAAIzC,KAAA,CAAM2C,CAAA,GAAIP,MAAA;UAC9C/B,cAAA,CAAeoC,CAAC,EAAEG,CAAA,IAAK1C,OAAA,CAAQuC,CAAC,IAAIzC,KAAA,CAAM6C,CAAA,GAAIT,MAAA;UAC9C/B,cAAA,CAAeoC,CAAC,EAAEK,CAAA,IAAK5C,OAAA,CAAQuC,CAAC,IAAIzC,KAAA,CAAM+C,CAAA,GAAIX,MAAA;QAC/C;MACF;IACF;IAGD,MAAMY,IAAA,GAAQ,IAAIhB,IAAA,CAAKiB,EAAA,GAAMrD,WAAA;IAE7B,SAAS6C,CAAA,GAAI,GAAGA,CAAA,GAAI,GAAGA,CAAA,IAAK;MAC1BpC,cAAA,CAAeoC,CAAC,EAAEC,CAAA,IAAKM,IAAA;MACvB3C,cAAA,CAAeoC,CAAC,EAAEG,CAAA,IAAKI,IAAA;MACvB3C,cAAA,CAAeoC,CAAC,EAAEK,CAAA,IAAKE,IAAA;IACxB;IAED,OAAO,IAAIE,UAAA,CAAW/C,EAAE;EACzB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}