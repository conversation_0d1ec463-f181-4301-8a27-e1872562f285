import React from 'react';
import './Footer.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const socialLinks = [
    {
      name: 'GitHub',
      url: 'https://github.com/nicolafadoul',
      icon: './images/github-sign.png',
      description: 'Check out my code'
    },
    {
      name: 'Git<PERSON>ab',
      url: 'https://gitlab.com/nicolafadoul',
      icon: '🦊',
      description: 'View my projects'
    },
    {
      name: 'Instagram',
      url: 'https://instagram.com/nicolafadoul',
      icon: '📸',
      description: 'Follow my journey'
    },
    {
      name: 'Facebook',
      url: 'https://facebook.com/nicolafadoul',
      icon: '📘',
      description: 'Connect with me'
    }
  ];

  return (
    <footer className="footer-container">
      <div className="footer-content">
        <div className="footer-main">
          <div className="footer-info">
            <h3><PERSON></h3>
            <p><PERSON> passionate about creating innovative solutions and building meaningful digital experiences.</p>
            <div className="footer-quote">
              <p>"Code is poetry written in logic"</p>
            </div>
          </div>
          
          <div className="footer-social">
            <h4>Let's Connect</h4>
            <div className="social-links">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link"
                  title={social.description}
                >
                  <div className="social-icon">
                    <span>{social.icon}</span>
                  </div>
                  <div className="social-info">
                    <span className="social-name">{social.name}</span>
                    <span className="social-desc">{social.description}</span>
                  </div>
                </a>
              ))}
            </div>
          </div>
        </div>
        
        <div className="footer-bottom">
          <div className="footer-divider"></div>
          <div className="footer-copyright">
            <p>&copy; {currentYear} Nicola Fadoul. All rights reserved.</p>
            <p>Built with React & passion ❤️</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
