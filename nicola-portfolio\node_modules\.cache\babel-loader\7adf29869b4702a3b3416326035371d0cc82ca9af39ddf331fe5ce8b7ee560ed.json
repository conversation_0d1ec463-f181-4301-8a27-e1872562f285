{"ast": null, "code": "import { Vector2 } from \"three\";\nconst TriangleBlurShader = {\n  uniforms: {\n    texture: {\n      value: null\n    },\n    delta: {\n      value: /* @__PURE__ */new Vector2(1, 1)\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    #include <common>\n\n    #define ITERATIONS 10.0\n\n    uniform sampler2D texture;\n    uniform vec2 delta;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 color = vec4( 0.0 );\n\n    \tfloat total = 0.0;\n\n    // randomize the lookup values to hide the fixed number of samples\n\n    \tfloat offset = rand( vUv );\n\n    \tfor ( float t = -ITERATIONS; t <= ITERATIONS; t ++ ) {\n\n    \t\tfloat percent = ( t + offset - 0.5 ) / ITERATIONS;\n    \t\tfloat weight = 1.0 - abs( percent );\n\n    \t\tcolor += texture2D( texture, vUv + delta * percent ) * weight;\n    \t\ttotal += weight;\n\n    \t}\n\n    \tgl_FragColor = color / total;\n\n    }\n  `)\n};\nexport { TriangleBlurShader };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uniforms", "texture", "value", "delta", "Vector2", "vertexShader", "fragmentShader"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\shaders\\TriangleBlurShader.ts"], "sourcesContent": ["import { Vector2 } from 'three'\n\n/**\n * Triangle blur shader\n * based on glfx.js triangle blur shader\n * https://github.com/evanw/glfx.js\n *\n * A basic blur filter, which convolves the image with a\n * pyramid filter. The pyramid filter is separable and is applied as two\n * perpendicular triangle filters.\n */\n\nexport const TriangleBlurShader = {\n  uniforms: {\n    texture: { value: null },\n    delta: { value: /* @__PURE__ */ new Vector2(1, 1) },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #include <common>\n\n    #define ITERATIONS 10.0\n\n    uniform sampler2D texture;\n    uniform vec2 delta;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 color = vec4( 0.0 );\n\n    \tfloat total = 0.0;\n\n    // randomize the lookup values to hide the fixed number of samples\n\n    \tfloat offset = rand( vUv );\n\n    \tfor ( float t = -ITERATIONS; t <= ITERATIONS; t ++ ) {\n\n    \t\tfloat percent = ( t + offset - 0.5 ) / ITERATIONS;\n    \t\tfloat weight = 1.0 - abs( percent );\n\n    \t\tcolor += texture2D( texture, vUv + delta * percent ) * weight;\n    \t\ttotal += weight;\n\n    \t}\n\n    \tgl_FragColor = color / total;\n\n    }\n  `,\n}\n"], "mappings": ";AAYO,MAAMA,kBAAA,GAAqB;EAChCC,QAAA,EAAU;IACRC,OAAA,EAAS;MAAEC,KAAA,EAAO;IAAK;IACvBC,KAAA,EAAO;MAAED,KAAA,qBAA2BE,OAAA,CAAQ,GAAG,CAAC;IAAE;EACpD;EAEAC,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}