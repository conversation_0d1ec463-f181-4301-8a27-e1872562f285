{"ast": null, "code": "import { Color } from \"three\";\nconst ColorifyShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    color: {\n      value: /* @__PURE__ */new Color(16777215)\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform vec3 color;\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tvec3 luma = vec3( 0.299, 0.587, 0.114 );\n    \tfloat v = dot( texel.xyz, luma );\n\n    \tgl_FragColor = vec4( v * color, texel.w );\n\n    }\n  `)\n};\nexport { ColorifyShader };", "map": {"version": 3, "names": ["ColorifyShader", "uniforms", "tDiffuse", "value", "color", "Color", "vertexShader", "fragmentShader"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\shaders\\ColorifyShader.ts"], "sourcesContent": ["import { Color } from 'three'\n\n/**\n * Colorify shader\n */\n\nexport const ColorifyShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    color: { value: /* @__PURE__ */ new Color(0xffffff) },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform vec3 color;\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n\n    \tvec3 luma = vec3( 0.299, 0.587, 0.114 );\n    \tfloat v = dot( texel.xyz, luma );\n\n    \tgl_FragColor = vec4( v * color, texel.w );\n\n    }\n  `,\n}\n"], "mappings": ";AAMO,MAAMA,cAAA,GAAiB;EAC5BC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,KAAA,EAAO;MAAED,KAAA,EAA2B,mBAAAE,KAAA,CAAM,QAAQ;IAAE;EACtD;EAEAC,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}