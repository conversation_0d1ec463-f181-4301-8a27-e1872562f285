{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\nicola-portfolio\\\\nicola-portfolio\\\\src\\\\components\\\\Hero\\\\Hero.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport \"./Hero.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  _s();\n  const [displayText, setDisplayText] = useState(\"\");\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [showCursor, setShowCursor] = useState(true);\n  const [startTyping, setStartTyping] = useState(false);\n  const fullText = \"Junior Developer | Enthusiastic Learner | Driven by Innovation\";\n  useEffect(() => {\n    const startDelay = setTimeout(() => {\n      setStartTyping(true);\n    }, 1500);\n    return () => clearTimeout(startDelay);\n  }, []);\n  useEffect(() => {\n    if (startTyping && currentIndex < fullText.length) {\n      const timeout = setTimeout(() => {\n        setDisplayText(prev => prev + fullText[currentIndex]);\n        setCurrentIndex(prev => prev + 1);\n      }, 80);\n      return () => clearTimeout(timeout);\n    }\n  }, [currentIndex, fullText, startTyping]);\n  useEffect(() => {\n    const cursorInterval = setInterval(() => {\n      setShowCursor(prev => !prev);\n    }, 500);\n    return () => clearInterval(cursorInterval);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"home\",\n    className: \"hero-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Hi, I'm Nicola Fadoul\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"typewriter-text\",\n        children: [displayText, /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `cursor ${showCursor ? \"visible\" : \"hidden\"}`,\n          children: \"|\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-img\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: `${process.env.PUBLIC_URL}/images/profile-picture.png`,\n          alt: \"Profile\",\n          className: \"profile-pic\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tech-icons\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-1\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/react1.png`,\n              alt: \"React\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-2\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/flutter-icon.webp`,\n              alt: \"Flutter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-3\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/html2.png`,\n              alt: \"HTML\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-4\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/css-icon.png`,\n              alt: \"CSS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-5\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/js.png`,\n              alt: \"JavaScript\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon tech-icon-6\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${process.env.PUBLIC_URL}/images/netframework.png`,\n              alt: \"ASP.NET\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Hero, \"He32u4WRLBHepitt8CY7xBTcc+8=\");\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Hero", "_s", "displayText", "setDisplayText", "currentIndex", "setCurrentIndex", "showCursor", "setShowCursor", "startTyping", "setStartTyping", "fullText", "startDelay", "setTimeout", "clearTimeout", "length", "timeout", "prev", "cursorInterval", "setInterval", "clearInterval", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "process", "env", "PUBLIC_URL", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/src/components/Hero/Hero.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport \"./Hero.css\";\r\n\r\nconst Hero = () => {\r\n  const [displayText, setDisplayText] = useState(\"\");\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const [showCursor, setShowCursor] = useState(true);\r\n  const [startTyping, setStartTyping] = useState(false);\r\n\r\n  const fullText =\r\n    \"Junior Developer | Enthusiastic Learner | Driven by Innovation\";\r\n\r\n  useEffect(() => {\r\n    const startDelay = setTimeout(() => {\r\n      setStartTyping(true);\r\n    }, 1500);\r\n\r\n    return () => clearTimeout(startDelay);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (startTyping && currentIndex < fullText.length) {\r\n      const timeout = setTimeout(() => {\r\n        setDisplayText((prev) => prev + fullText[currentIndex]);\r\n        setCurrentIndex((prev) => prev + 1);\r\n      }, 80);\r\n\r\n      return () => clearTimeout(timeout);\r\n    }\r\n  }, [currentIndex, fullText, startTyping]);\r\n\r\n  useEffect(() => {\r\n    const cursorInterval = setInterval(() => {\r\n      setShowCursor((prev) => !prev);\r\n    }, 500);\r\n\r\n    return () => clearInterval(cursorInterval);\r\n  }, []);\r\n\r\n  return (\r\n    <section id=\"home\" className=\"hero-container\">\r\n      <div className=\"hero-content\">\r\n        <h2>Hi, I'm Nicola Fadoul</h2>\r\n        <p className=\"typewriter-text\">\r\n          {displayText}\r\n          <span className={`cursor ${showCursor ? \"visible\" : \"hidden\"}`}>\r\n            |\r\n          </span>\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"hero-img\">\r\n        <div className=\"profile-container\">\r\n          <img\r\n            src={`${process.env.PUBLIC_URL}/images/profile-picture.png`}\r\n            alt=\"Profile\"\r\n            className=\"profile-pic\"\r\n          />\r\n          <div className=\"tech-icons\">\r\n            <div className=\"tech-icon tech-icon-1\">\r\n              <img\r\n                src={`${process.env.PUBLIC_URL}/images/react1.png`}\r\n                alt=\"React\"\r\n              />\r\n            </div>\r\n            <div className=\"tech-icon tech-icon-2\">\r\n              <img\r\n                src={`${process.env.PUBLIC_URL}/images/flutter-icon.webp`}\r\n                alt=\"Flutter\"\r\n              />\r\n            </div>\r\n            <div className=\"tech-icon tech-icon-3\">\r\n              <img\r\n                src={`${process.env.PUBLIC_URL}/images/html2.png`}\r\n                alt=\"HTML\"\r\n              />\r\n            </div>\r\n            <div className=\"tech-icon tech-icon-4\">\r\n              <img\r\n                src={`${process.env.PUBLIC_URL}/images/css-icon.png`}\r\n                alt=\"CSS\"\r\n              />\r\n            </div>\r\n            <div className=\"tech-icon tech-icon-5\">\r\n              <img\r\n                src={`${process.env.PUBLIC_URL}/images/js.png`}\r\n                alt=\"JavaScript\"\r\n              />\r\n            </div>\r\n            <div className=\"tech-icon tech-icon-6\">\r\n              <img\r\n                src={`${process.env.PUBLIC_URL}/images/netframework.png`}\r\n                alt=\"ASP.NET\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Hero;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMc,QAAQ,GACZ,gEAAgE;EAElEb,SAAS,CAAC,MAAM;IACd,MAAMc,UAAU,GAAGC,UAAU,CAAC,MAAM;MAClCH,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMI,YAAY,CAACF,UAAU,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;EAENd,SAAS,CAAC,MAAM;IACd,IAAIW,WAAW,IAAIJ,YAAY,GAAGM,QAAQ,CAACI,MAAM,EAAE;MACjD,MAAMC,OAAO,GAAGH,UAAU,CAAC,MAAM;QAC/BT,cAAc,CAAEa,IAAI,IAAKA,IAAI,GAAGN,QAAQ,CAACN,YAAY,CAAC,CAAC;QACvDC,eAAe,CAAEW,IAAI,IAAKA,IAAI,GAAG,CAAC,CAAC;MACrC,CAAC,EAAE,EAAE,CAAC;MAEN,OAAO,MAAMH,YAAY,CAACE,OAAO,CAAC;IACpC;EACF,CAAC,EAAE,CAACX,YAAY,EAAEM,QAAQ,EAAEF,WAAW,CAAC,CAAC;EAEzCX,SAAS,CAAC,MAAM;IACd,MAAMoB,cAAc,GAAGC,WAAW,CAAC,MAAM;MACvCX,aAAa,CAAES,IAAI,IAAK,CAACA,IAAI,CAAC;IAChC,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMG,aAAa,CAACF,cAAc,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;EAEN,oBACElB,OAAA;IAASqB,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3CvB,OAAA;MAAKsB,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BvB,OAAA;QAAAuB,QAAA,EAAI;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9B3B,OAAA;QAAGsB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,GAC3BpB,WAAW,eACZH,OAAA;UAAMsB,SAAS,EAAE,UAAUf,UAAU,GAAG,SAAS,GAAG,QAAQ,EAAG;UAAAgB,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEN3B,OAAA;MAAKsB,SAAS,EAAC,UAAU;MAAAC,QAAA,eACvBvB,OAAA;QAAKsB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCvB,OAAA;UACE4B,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,6BAA8B;UAC5DC,GAAG,EAAC,SAAS;UACbV,SAAS,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACF3B,OAAA;UAAKsB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvB,OAAA;YAAKsB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCvB,OAAA;cACE4B,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,oBAAqB;cACnDC,GAAG,EAAC;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCvB,OAAA;cACE4B,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,2BAA4B;cAC1DC,GAAG,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCvB,OAAA;cACE4B,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,mBAAoB;cAClDC,GAAG,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCvB,OAAA;cACE4B,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,sBAAuB;cACrDC,GAAG,EAAC;YAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCvB,OAAA;cACE4B,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,gBAAiB;cAC/CC,GAAG,EAAC;YAAY;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3B,OAAA;YAAKsB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCvB,OAAA;cACE4B,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,0BAA2B;cACzDC,GAAG,EAAC;YAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACzB,EAAA,CAjGID,IAAI;AAAAgC,EAAA,GAAJhC,IAAI;AAmGV,eAAeA,IAAI;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}