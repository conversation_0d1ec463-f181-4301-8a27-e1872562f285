{"ast": null, "code": "const BleachBypassShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    opacity: {\n      value: 1\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform float opacity;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 base = texture2D( tDiffuse, vUv );\n\n    \tvec3 lumCoeff = vec3( 0.25, 0.65, 0.1 );\n    \tfloat lum = dot( lumCoeff, base.rgb );\n    \tvec3 blend = vec3( lum );\n\n    \tfloat L = min( 1.0, max( 0.0, 10.0 * ( lum - 0.45 ) ) );\n\n    \tvec3 result1 = 2.0 * base.rgb * blend;\n    \tvec3 result2 = 1.0 - 2.0 * ( 1.0 - blend ) * ( 1.0 - base.rgb );\n\n    \tvec3 newColor = mix( result1, result2, L );\n\n    \tfloat A2 = opacity * base.a;\n    \tvec3 mixRGB = A2 * newColor.rgb;\n    \tmixRGB += ( ( 1.0 - A2 ) * base.rgb );\n\n    \tgl_FragColor = vec4( mixRGB, base.a );\n\n    }\n  `)\n};\nexport { BleachBypassShader };", "map": {"version": 3, "names": ["BleachBypassShader", "uniforms", "tDiffuse", "value", "opacity", "vertexShader", "fragmentShader"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\shaders\\BleachBypassShader.ts"], "sourcesContent": ["/**\n * Bleach bypass shader [http://en.wikipedia.org/wiki/Bleach_bypass]\n * - based on Nvidia example\n * http://developer.download.nvidia.com/shaderlibrary/webpages/shader_library.html#post_bleach_bypass\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type BleachBypassShaderUniforms = {\n  opacity: IUniform<number>\n  tDiffuse: IUniform<Texture | null>\n}\n\nexport interface IBleachBypassShader extends IShader<BleachBypassShaderUniforms> {}\n\nexport const BleachBypassShader: IBleachBypassShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    opacity: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float opacity;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 base = texture2D( tDiffuse, vUv );\n\n    \tvec3 lumCoeff = vec3( 0.25, 0.65, 0.1 );\n    \tfloat lum = dot( lumCoeff, base.rgb );\n    \tvec3 blend = vec3( lum );\n\n    \tfloat L = min( 1.0, max( 0.0, 10.0 * ( lum - 0.45 ) ) );\n\n    \tvec3 result1 = 2.0 * base.rgb * blend;\n    \tvec3 result2 = 1.0 - 2.0 * ( 1.0 - blend ) * ( 1.0 - base.rgb );\n\n    \tvec3 newColor = mix( result1, result2, L );\n\n    \tfloat A2 = opacity * base.a;\n    \tvec3 mixRGB = A2 * newColor.rgb;\n    \tmixRGB += ( ( 1.0 - A2 ) * base.rgb );\n\n    \tgl_FragColor = vec4( mixRGB, base.a );\n\n    }\n  `,\n}\n"], "mappings": "AAgBO,MAAMA,kBAAA,GAA0C;EACrDC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,OAAA,EAAS;MAAED,KAAA,EAAO;IAAI;EACxB;EAEAE,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAWzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8B7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}