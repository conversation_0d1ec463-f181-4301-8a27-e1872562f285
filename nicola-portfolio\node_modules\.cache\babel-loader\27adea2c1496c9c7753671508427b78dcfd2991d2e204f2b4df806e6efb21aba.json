{"ast": null, "code": "import { Box3, Matrix4 } from 'three';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { BOUNDING_DATA_INDEX, COUNT, IS_LEAF, LEFT_NODE, OFFSET, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../../utils/PrimitivePool.js';\nconst _bufferStack1 = new BufferStack.constructor();\nconst _bufferStack2 = new BufferStack.constructor();\nconst _boxPool = new PrimitivePool(() => new Box3());\nconst _leftBox1 = new Box3();\nconst _rightBox1 = new Box3();\nconst _leftBox2 = new Box3();\nconst _rightBox2 = new Box3();\nlet _active = false;\nexport function bvhcast(bvh, otherBvh, matrixToLocal, intersectsRanges) {\n  if (_active) {\n    throw new Error('MeshBVH: Recursive calls to bvhcast not supported.');\n  }\n  _active = true;\n  const roots = bvh._roots;\n  const otherRoots = otherBvh._roots;\n  let result;\n  let offset1 = 0;\n  let offset2 = 0;\n  const invMat = new Matrix4().copy(matrixToLocal).invert();\n\n  // iterate over the first set of roots\n  for (let i = 0, il = roots.length; i < il; i++) {\n    _bufferStack1.setBuffer(roots[i]);\n    offset2 = 0;\n\n    // prep the initial root box\n    const localBox = _boxPool.getPrimitive();\n    arrayToBox(BOUNDING_DATA_INDEX(0), _bufferStack1.float32Array, localBox);\n    localBox.applyMatrix4(invMat);\n\n    // iterate over the second set of roots\n    for (let j = 0, jl = otherRoots.length; j < jl; j++) {\n      _bufferStack2.setBuffer(otherRoots[j]);\n      result = _traverse(0, 0, matrixToLocal, invMat, intersectsRanges, offset1, offset2, 0, 0, localBox);\n      _bufferStack2.clearBuffer();\n      offset2 += otherRoots[j].length;\n      if (result) {\n        break;\n      }\n    }\n\n    // release stack info\n    _boxPool.releasePrimitive(localBox);\n    _bufferStack1.clearBuffer();\n    offset1 += roots[i].length;\n    if (result) {\n      break;\n    }\n  }\n  _active = false;\n  return result;\n}\nfunction _traverse(node1Index32, node2Index32, matrix2to1, matrix1to2, intersectsRangesFunc,\n// offsets for ids\nnode1IndexByteOffset = 0, node2IndexByteOffset = 0,\n// tree depth\ndepth1 = 0, depth2 = 0, currBox = null, reversed = false) {\n  // get the buffer stacks associated with the current indices\n  let bufferStack1, bufferStack2;\n  if (reversed) {\n    bufferStack1 = _bufferStack2;\n    bufferStack2 = _bufferStack1;\n  } else {\n    bufferStack1 = _bufferStack1;\n    bufferStack2 = _bufferStack2;\n  }\n\n  // get the local instances of the typed buffers\n  const float32Array1 = bufferStack1.float32Array,\n    uint32Array1 = bufferStack1.uint32Array,\n    uint16Array1 = bufferStack1.uint16Array,\n    float32Array2 = bufferStack2.float32Array,\n    uint32Array2 = bufferStack2.uint32Array,\n    uint16Array2 = bufferStack2.uint16Array;\n  const node1Index16 = node1Index32 * 2;\n  const node2Index16 = node2Index32 * 2;\n  const isLeaf1 = IS_LEAF(node1Index16, uint16Array1);\n  const isLeaf2 = IS_LEAF(node2Index16, uint16Array2);\n  let result = false;\n  if (isLeaf2 && isLeaf1) {\n    // if both bounds are leaf nodes then fire the callback if the boxes intersect\n    if (reversed) {\n      result = intersectsRangesFunc(OFFSET(node2Index32, uint32Array2), COUNT(node2Index32 * 2, uint16Array2), OFFSET(node1Index32, uint32Array1), COUNT(node1Index32 * 2, uint16Array1), depth2, node2IndexByteOffset + node2Index32, depth1, node1IndexByteOffset + node1Index32);\n    } else {\n      result = intersectsRangesFunc(OFFSET(node1Index32, uint32Array1), COUNT(node1Index32 * 2, uint16Array1), OFFSET(node2Index32, uint32Array2), COUNT(node2Index32 * 2, uint16Array2), depth1, node1IndexByteOffset + node1Index32, depth2, node2IndexByteOffset + node2Index32);\n    }\n  } else if (isLeaf2) {\n    // SWAP\n    // If we've traversed to the leaf node on the other bvh then we need to swap over\n    // to traverse down the first one\n\n    // get the new box to use\n    const newBox = _boxPool.getPrimitive();\n    arrayToBox(BOUNDING_DATA_INDEX(node2Index32), float32Array2, newBox);\n    newBox.applyMatrix4(matrix2to1);\n\n    // get the child bounds to check before traversal\n    const cl1 = LEFT_NODE(node1Index32);\n    const cr1 = RIGHT_NODE(node1Index32, uint32Array1);\n    arrayToBox(BOUNDING_DATA_INDEX(cl1), float32Array1, _leftBox1);\n    arrayToBox(BOUNDING_DATA_INDEX(cr1), float32Array1, _rightBox1);\n\n    // precompute the intersections otherwise the global boxes will be modified during traversal\n    const intersectCl1 = newBox.intersectsBox(_leftBox1);\n    const intersectCr1 = newBox.intersectsBox(_rightBox1);\n    result = intersectCl1 && _traverse(node2Index32, cl1, matrix1to2, matrix2to1, intersectsRangesFunc, node2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1, newBox, !reversed) || intersectCr1 && _traverse(node2Index32, cr1, matrix1to2, matrix2to1, intersectsRangesFunc, node2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1, newBox, !reversed);\n    _boxPool.releasePrimitive(newBox);\n  } else {\n    // if neither are leaves then we should swap if one of the children does not\n    // intersect with the current bounds\n\n    // get the child bounds to check\n    const cl2 = LEFT_NODE(node2Index32);\n    const cr2 = RIGHT_NODE(node2Index32, uint32Array2);\n    arrayToBox(BOUNDING_DATA_INDEX(cl2), float32Array2, _leftBox2);\n    arrayToBox(BOUNDING_DATA_INDEX(cr2), float32Array2, _rightBox2);\n    const leftIntersects = currBox.intersectsBox(_leftBox2);\n    const rightIntersects = currBox.intersectsBox(_rightBox2);\n    if (leftIntersects && rightIntersects) {\n      // continue to traverse both children if they both intersect\n      result = _traverse(node1Index32, cl2, matrix2to1, matrix1to2, intersectsRangesFunc, node1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1, currBox, reversed) || _traverse(node1Index32, cr2, matrix2to1, matrix1to2, intersectsRangesFunc, node1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1, currBox, reversed);\n    } else if (leftIntersects) {\n      if (isLeaf1) {\n        // if the current box is a leaf then just continue\n        result = _traverse(node1Index32, cl2, matrix2to1, matrix1to2, intersectsRangesFunc, node1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1, currBox, reversed);\n      } else {\n        // SWAP\n        // if only one box intersects then we have to swap to the other bvh to continue\n        const newBox = _boxPool.getPrimitive();\n        newBox.copy(_leftBox2).applyMatrix4(matrix2to1);\n        const cl1 = LEFT_NODE(node1Index32);\n        const cr1 = RIGHT_NODE(node1Index32, uint32Array1);\n        arrayToBox(BOUNDING_DATA_INDEX(cl1), float32Array1, _leftBox1);\n        arrayToBox(BOUNDING_DATA_INDEX(cr1), float32Array1, _rightBox1);\n\n        // precompute the intersections otherwise the global boxes will be modified during traversal\n        const intersectCl1 = newBox.intersectsBox(_leftBox1);\n        const intersectCr1 = newBox.intersectsBox(_rightBox1);\n        result = intersectCl1 && _traverse(cl2, cl1, matrix1to2, matrix2to1, intersectsRangesFunc, node2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1, newBox, !reversed) || intersectCr1 && _traverse(cl2, cr1, matrix1to2, matrix2to1, intersectsRangesFunc, node2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1, newBox, !reversed);\n        _boxPool.releasePrimitive(newBox);\n      }\n    } else if (rightIntersects) {\n      if (isLeaf1) {\n        // if the current box is a leaf then just continue\n        result = _traverse(node1Index32, cr2, matrix2to1, matrix1to2, intersectsRangesFunc, node1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1, currBox, reversed);\n      } else {\n        // SWAP\n        // if only one box intersects then we have to swap to the other bvh to continue\n        const newBox = _boxPool.getPrimitive();\n        newBox.copy(_rightBox2).applyMatrix4(matrix2to1);\n        const cl1 = LEFT_NODE(node1Index32);\n        const cr1 = RIGHT_NODE(node1Index32, uint32Array1);\n        arrayToBox(BOUNDING_DATA_INDEX(cl1), float32Array1, _leftBox1);\n        arrayToBox(BOUNDING_DATA_INDEX(cr1), float32Array1, _rightBox1);\n\n        // precompute the intersections otherwise the global boxes will be modified during traversal\n        const intersectCl1 = newBox.intersectsBox(_leftBox1);\n        const intersectCr1 = newBox.intersectsBox(_rightBox1);\n        result = intersectCl1 && _traverse(cr2, cl1, matrix1to2, matrix2to1, intersectsRangesFunc, node2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1, newBox, !reversed) || intersectCr1 && _traverse(cr2, cr1, matrix1to2, matrix2to1, intersectsRangesFunc, node2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1, newBox, !reversed);\n        _boxPool.releasePrimitive(newBox);\n      }\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["Box3", "Matrix4", "<PERSON><PERSON><PERSON><PERSON>ta<PERSON>", "BOUNDING_DATA_INDEX", "COUNT", "IS_LEAF", "LEFT_NODE", "OFFSET", "RIGHT_NODE", "arrayToBox", "PrimitivePool", "_bufferStack1", "constructor", "_bufferStack2", "_boxPool", "_leftBox1", "_rightBox1", "_leftBox2", "_rightBox2", "_active", "bvhcast", "bvh", "otherBvh", "matrixToLocal", "intersectsRanges", "Error", "roots", "_roots", "otherRoots", "result", "offset1", "offset2", "invMat", "copy", "invert", "i", "il", "length", "<PERSON><PERSON><PERSON><PERSON>", "localBox", "getPrimitive", "float32Array", "applyMatrix4", "j", "jl", "_traverse", "<PERSON><PERSON><PERSON><PERSON>", "releasePrimitive", "node1Index32", "node2Index32", "matrix2to1", "matrix1to2", "intersectsRangesFunc", "node1IndexByteOffset", "node2IndexByteOffset", "depth1", "depth2", "currBox", "reversed", "bufferStack1", "bufferStack2", "float32Array1", "uint32Array1", "uint32Array", "uint16Array1", "uint16Array", "float32Array2", "uint32Array2", "uint16Array2", "node1Index16", "node2Index16", "isLeaf1", "isLeaf2", "newBox", "cl1", "cr1", "intersectCl1", "intersectsBox", "intersectCr1", "cl2", "cr2", "leftIntersects", "rightIntersects"], "sources": ["C:/Users/<USER>/Documents/nicola-portfolio/nicola-portfolio/node_modules/three-mesh-bvh/src/core/cast/bvhcast.js"], "sourcesContent": ["import { Box3, Matrix4 } from 'three';\nimport { BufferStack } from '../utils/BufferStack.js';\nimport { BOUNDING_DATA_INDEX, COUNT, IS_LEAF, LEFT_NODE, OFFSET, RIGHT_NODE } from '../utils/nodeBufferUtils.js';\nimport { arrayToBox } from '../../utils/ArrayBoxUtilities.js';\nimport { PrimitivePool } from '../../utils/PrimitivePool.js';\n\nconst _bufferStack1 = new BufferStack.constructor();\nconst _bufferStack2 = new BufferStack.constructor();\nconst _boxPool = new PrimitivePool( () => new Box3() );\nconst _leftBox1 = new Box3();\nconst _rightBox1 = new Box3();\n\nconst _leftBox2 = new Box3();\nconst _rightBox2 = new Box3();\n\nlet _active = false;\n\nexport function bvhcast( bvh, otherBvh, matrixToLocal, intersectsRanges ) {\n\n\tif ( _active ) {\n\n\t\tthrow new Error( 'MeshBVH: Recursive calls to bvhcast not supported.' );\n\n\t}\n\n\t_active = true;\n\n\tconst roots = bvh._roots;\n\tconst otherRoots = otherBvh._roots;\n\tlet result;\n\tlet offset1 = 0;\n\tlet offset2 = 0;\n\tconst invMat = new Matrix4().copy( matrixToLocal ).invert();\n\n\t// iterate over the first set of roots\n\tfor ( let i = 0, il = roots.length; i < il; i ++ ) {\n\n\t\t_bufferStack1.setBuffer( roots[ i ] );\n\t\toffset2 = 0;\n\n\t\t// prep the initial root box\n\t\tconst localBox = _boxPool.getPrimitive();\n\t\tarrayToBox( BOUNDING_DATA_INDEX( 0 ), _bufferStack1.float32Array, localBox );\n\t\tlocalBox.applyMatrix4( invMat );\n\n\t\t// iterate over the second set of roots\n\t\tfor ( let j = 0, jl = otherRoots.length; j < jl; j ++ ) {\n\n\t\t\t_bufferStack2.setBuffer( otherRoots[ j ] );\n\n\t\t\tresult = _traverse(\n\t\t\t\t0, 0, matrixToLocal, invMat, intersectsRanges,\n\t\t\t\toffset1, offset2, 0, 0,\n\t\t\t\tlocalBox,\n\t\t\t);\n\n\t\t\t_bufferStack2.clearBuffer();\n\t\t\toffset2 += otherRoots[ j ].length;\n\n\t\t\tif ( result ) {\n\n\t\t\t\tbreak;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// release stack info\n\t\t_boxPool.releasePrimitive( localBox );\n\t\t_bufferStack1.clearBuffer();\n\t\toffset1 += roots[ i ].length;\n\n\t\tif ( result ) {\n\n\t\t\tbreak;\n\n\t\t}\n\n\t}\n\n\t_active = false;\n\treturn result;\n\n}\n\nfunction _traverse(\n\tnode1Index32,\n\tnode2Index32,\n\tmatrix2to1,\n\tmatrix1to2,\n\tintersectsRangesFunc,\n\n\t// offsets for ids\n\tnode1IndexByteOffset = 0,\n\tnode2IndexByteOffset = 0,\n\n\t// tree depth\n\tdepth1 = 0,\n\tdepth2 = 0,\n\n\tcurrBox = null,\n\treversed = false,\n\n) {\n\n\t// get the buffer stacks associated with the current indices\n\tlet bufferStack1, bufferStack2;\n\tif ( reversed ) {\n\n\t\tbufferStack1 = _bufferStack2;\n\t\tbufferStack2 = _bufferStack1;\n\n\t} else {\n\n\t\tbufferStack1 = _bufferStack1;\n\t\tbufferStack2 = _bufferStack2;\n\n\t}\n\n\t// get the local instances of the typed buffers\n\tconst\n\t\tfloat32Array1 = bufferStack1.float32Array,\n\t\tuint32Array1 = bufferStack1.uint32Array,\n\t\tuint16Array1 = bufferStack1.uint16Array,\n\t\tfloat32Array2 = bufferStack2.float32Array,\n\t\tuint32Array2 = bufferStack2.uint32Array,\n\t\tuint16Array2 = bufferStack2.uint16Array;\n\n\tconst node1Index16 = node1Index32 * 2;\n\tconst node2Index16 = node2Index32 * 2;\n\tconst isLeaf1 = IS_LEAF( node1Index16, uint16Array1 );\n\tconst isLeaf2 = IS_LEAF( node2Index16, uint16Array2 );\n\tlet result = false;\n\tif ( isLeaf2 && isLeaf1 ) {\n\n\t\t// if both bounds are leaf nodes then fire the callback if the boxes intersect\n\t\tif ( reversed ) {\n\n\t\t\tresult = intersectsRangesFunc(\n\t\t\t\tOFFSET( node2Index32, uint32Array2 ), COUNT( node2Index32 * 2, uint16Array2 ),\n\t\t\t\tOFFSET( node1Index32, uint32Array1 ), COUNT( node1Index32 * 2, uint16Array1 ),\n\t\t\t\tdepth2, node2IndexByteOffset + node2Index32,\n\t\t\t\tdepth1, node1IndexByteOffset + node1Index32,\n\t\t\t);\n\n\t\t} else {\n\n\t\t\tresult = intersectsRangesFunc(\n\t\t\t\tOFFSET( node1Index32, uint32Array1 ), COUNT( node1Index32 * 2, uint16Array1 ),\n\t\t\t\tOFFSET( node2Index32, uint32Array2 ), COUNT( node2Index32 * 2, uint16Array2 ),\n\t\t\t\tdepth1, node1IndexByteOffset + node1Index32,\n\t\t\t\tdepth2, node2IndexByteOffset + node2Index32,\n\t\t\t);\n\n\t\t}\n\n\t} else if ( isLeaf2 ) {\n\n\t\t// SWAP\n\t\t// If we've traversed to the leaf node on the other bvh then we need to swap over\n\t\t// to traverse down the first one\n\n\t\t// get the new box to use\n\t\tconst newBox = _boxPool.getPrimitive();\n\t\tarrayToBox( BOUNDING_DATA_INDEX( node2Index32 ), float32Array2, newBox );\n\t\tnewBox.applyMatrix4( matrix2to1 );\n\n\t\t// get the child bounds to check before traversal\n\t\tconst cl1 = LEFT_NODE( node1Index32 );\n\t\tconst cr1 = RIGHT_NODE( node1Index32, uint32Array1 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cl1 ), float32Array1, _leftBox1 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cr1 ), float32Array1, _rightBox1 );\n\n\t\t// precompute the intersections otherwise the global boxes will be modified during traversal\n\t\tconst intersectCl1 = newBox.intersectsBox( _leftBox1 );\n\t\tconst intersectCr1 = newBox.intersectsBox( _rightBox1 );\n\t\tresult = (\n\t\t\tintersectCl1 && _traverse(\n\t\t\t\tnode2Index32, cl1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\tnewBox, ! reversed,\n\t\t\t)\n\t\t) || (\n\t\t\tintersectCr1 && _traverse(\n\t\t\t\tnode2Index32, cr1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\tnewBox, ! reversed,\n\t\t\t)\n\t\t);\n\n\t\t_boxPool.releasePrimitive( newBox );\n\n\t} else {\n\n\t\t// if neither are leaves then we should swap if one of the children does not\n\t\t// intersect with the current bounds\n\n\t\t// get the child bounds to check\n\t\tconst cl2 = LEFT_NODE( node2Index32 );\n\t\tconst cr2 = RIGHT_NODE( node2Index32, uint32Array2 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cl2 ), float32Array2, _leftBox2 );\n\t\tarrayToBox( BOUNDING_DATA_INDEX( cr2 ), float32Array2, _rightBox2 );\n\n\t\tconst leftIntersects = currBox.intersectsBox( _leftBox2 );\n\t\tconst rightIntersects = currBox.intersectsBox( _rightBox2 );\n\t\tif ( leftIntersects && rightIntersects ) {\n\n\t\t\t// continue to traverse both children if they both intersect\n\t\t\tresult = _traverse(\n\t\t\t\tnode1Index32, cl2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\tcurrBox, reversed,\n\t\t\t) || _traverse(\n\t\t\t\tnode1Index32, cr2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\tcurrBox, reversed,\n\t\t\t);\n\n\t\t} else if ( leftIntersects ) {\n\n\t\t\tif ( isLeaf1 ) {\n\n\t\t\t\t// if the current box is a leaf then just continue\n\t\t\t\tresult = _traverse(\n\t\t\t\t\tnode1Index32, cl2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\t\tcurrBox, reversed,\n\t\t\t\t);\n\n\t\t\t} else {\n\n\t\t\t\t// SWAP\n\t\t\t\t// if only one box intersects then we have to swap to the other bvh to continue\n\t\t\t\tconst newBox = _boxPool.getPrimitive();\n\t\t\t\tnewBox.copy( _leftBox2 ).applyMatrix4( matrix2to1 );\n\n\t\t\t\tconst cl1 = LEFT_NODE( node1Index32 );\n\t\t\t\tconst cr1 = RIGHT_NODE( node1Index32, uint32Array1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cl1 ), float32Array1, _leftBox1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cr1 ), float32Array1, _rightBox1 );\n\n\t\t\t\t// precompute the intersections otherwise the global boxes will be modified during traversal\n\t\t\t\tconst intersectCl1 = newBox.intersectsBox( _leftBox1 );\n\t\t\t\tconst intersectCr1 = newBox.intersectsBox( _rightBox1 );\n\t\t\t\tresult = (\n\t\t\t\t\tintersectCl1 && _traverse(\n\t\t\t\t\t\tcl2, cl1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t) || (\n\t\t\t\t\tintersectCr1 && _traverse(\n\t\t\t\t\t\tcl2, cr1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\t_boxPool.releasePrimitive( newBox );\n\n\t\t\t}\n\n\t\t} else if ( rightIntersects ) {\n\n\t\t\tif ( isLeaf1 ) {\n\n\t\t\t\t// if the current box is a leaf then just continue\n\t\t\t\tresult = _traverse(\n\t\t\t\t\tnode1Index32, cr2, matrix2to1, matrix1to2, intersectsRangesFunc,\n\t\t\t\t\tnode1IndexByteOffset, node2IndexByteOffset, depth1, depth2 + 1,\n\t\t\t\t\tcurrBox, reversed,\n\t\t\t\t);\n\n\t\t\t} else {\n\n\t\t\t\t// SWAP\n\t\t\t\t// if only one box intersects then we have to swap to the other bvh to continue\n\t\t\t\tconst newBox = _boxPool.getPrimitive();\n\t\t\t\tnewBox.copy( _rightBox2 ).applyMatrix4( matrix2to1 );\n\n\t\t\t\tconst cl1 = LEFT_NODE( node1Index32 );\n\t\t\t\tconst cr1 = RIGHT_NODE( node1Index32, uint32Array1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cl1 ), float32Array1, _leftBox1 );\n\t\t\t\tarrayToBox( BOUNDING_DATA_INDEX( cr1 ), float32Array1, _rightBox1 );\n\n\t\t\t\t// precompute the intersections otherwise the global boxes will be modified during traversal\n\t\t\t\tconst intersectCl1 = newBox.intersectsBox( _leftBox1 );\n\t\t\t\tconst intersectCr1 = newBox.intersectsBox( _rightBox1 );\n\t\t\t\tresult = (\n\t\t\t\t\tintersectCl1 && _traverse(\n\t\t\t\t\t\tcr2, cl1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t) || (\n\t\t\t\t\tintersectCr1 && _traverse(\n\t\t\t\t\t\tcr2, cr1, matrix1to2, matrix2to1, intersectsRangesFunc,\n\t\t\t\t\t\tnode2IndexByteOffset, node1IndexByteOffset, depth2, depth1 + 1,\n\t\t\t\t\t\tnewBox, ! reversed,\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\t_boxPool.releasePrimitive( newBox );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\treturn result;\n\n}\n\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,OAAO,QAAQ,OAAO;AACrC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,EAAEC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAQ,6BAA6B;AAChH,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,aAAa,QAAQ,8BAA8B;AAE5D,MAAMC,aAAa,GAAG,IAAIT,WAAW,CAACU,WAAW,CAAC,CAAC;AACnD,MAAMC,aAAa,GAAG,IAAIX,WAAW,CAACU,WAAW,CAAC,CAAC;AACnD,MAAME,QAAQ,GAAG,IAAIJ,aAAa,CAAE,MAAM,IAAIV,IAAI,CAAC,CAAE,CAAC;AACtD,MAAMe,SAAS,GAAG,IAAIf,IAAI,CAAC,CAAC;AAC5B,MAAMgB,UAAU,GAAG,IAAIhB,IAAI,CAAC,CAAC;AAE7B,MAAMiB,SAAS,GAAG,IAAIjB,IAAI,CAAC,CAAC;AAC5B,MAAMkB,UAAU,GAAG,IAAIlB,IAAI,CAAC,CAAC;AAE7B,IAAImB,OAAO,GAAG,KAAK;AAEnB,OAAO,SAASC,OAAOA,CAAEC,GAAG,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAG;EAEzE,IAAKL,OAAO,EAAG;IAEd,MAAM,IAAIM,KAAK,CAAE,oDAAqD,CAAC;EAExE;EAEAN,OAAO,GAAG,IAAI;EAEd,MAAMO,KAAK,GAAGL,GAAG,CAACM,MAAM;EACxB,MAAMC,UAAU,GAAGN,QAAQ,CAACK,MAAM;EAClC,IAAIE,MAAM;EACV,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,OAAO,GAAG,CAAC;EACf,MAAMC,MAAM,GAAG,IAAI/B,OAAO,CAAC,CAAC,CAACgC,IAAI,CAAEV,aAAc,CAAC,CAACW,MAAM,CAAC,CAAC;;EAE3D;EACA,KAAM,IAAIC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGV,KAAK,CAACW,MAAM,EAAEF,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAG,EAAG;IAElDxB,aAAa,CAAC2B,SAAS,CAAEZ,KAAK,CAAES,CAAC,CAAG,CAAC;IACrCJ,OAAO,GAAG,CAAC;;IAEX;IACA,MAAMQ,QAAQ,GAAGzB,QAAQ,CAAC0B,YAAY,CAAC,CAAC;IACxC/B,UAAU,CAAEN,mBAAmB,CAAE,CAAE,CAAC,EAAEQ,aAAa,CAAC8B,YAAY,EAAEF,QAAS,CAAC;IAC5EA,QAAQ,CAACG,YAAY,CAAEV,MAAO,CAAC;;IAE/B;IACA,KAAM,IAAIW,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGhB,UAAU,CAACS,MAAM,EAAEM,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAG,EAAG;MAEvD9B,aAAa,CAACyB,SAAS,CAAEV,UAAU,CAAEe,CAAC,CAAG,CAAC;MAE1Cd,MAAM,GAAGgB,SAAS,CACjB,CAAC,EAAE,CAAC,EAAEtB,aAAa,EAAES,MAAM,EAAER,gBAAgB,EAC7CM,OAAO,EAAEC,OAAO,EAAE,CAAC,EAAE,CAAC,EACtBQ,QACD,CAAC;MAED1B,aAAa,CAACiC,WAAW,CAAC,CAAC;MAC3Bf,OAAO,IAAIH,UAAU,CAAEe,CAAC,CAAE,CAACN,MAAM;MAEjC,IAAKR,MAAM,EAAG;QAEb;MAED;IAED;;IAEA;IACAf,QAAQ,CAACiC,gBAAgB,CAAER,QAAS,CAAC;IACrC5B,aAAa,CAACmC,WAAW,CAAC,CAAC;IAC3BhB,OAAO,IAAIJ,KAAK,CAAES,CAAC,CAAE,CAACE,MAAM;IAE5B,IAAKR,MAAM,EAAG;MAEb;IAED;EAED;EAEAV,OAAO,GAAG,KAAK;EACf,OAAOU,MAAM;AAEd;AAEA,SAASgB,SAASA,CACjBG,YAAY,EACZC,YAAY,EACZC,UAAU,EACVC,UAAU,EACVC,oBAAoB;AAEpB;AACAC,oBAAoB,GAAG,CAAC,EACxBC,oBAAoB,GAAG,CAAC;AAExB;AACAC,MAAM,GAAG,CAAC,EACVC,MAAM,GAAG,CAAC,EAEVC,OAAO,GAAG,IAAI,EACdC,QAAQ,GAAG,KAAK,EAEf;EAED;EACA,IAAIC,YAAY,EAAEC,YAAY;EAC9B,IAAKF,QAAQ,EAAG;IAEfC,YAAY,GAAG9C,aAAa;IAC5B+C,YAAY,GAAGjD,aAAa;EAE7B,CAAC,MAAM;IAENgD,YAAY,GAAGhD,aAAa;IAC5BiD,YAAY,GAAG/C,aAAa;EAE7B;;EAEA;EACA,MACCgD,aAAa,GAAGF,YAAY,CAAClB,YAAY;IACzCqB,YAAY,GAAGH,YAAY,CAACI,WAAW;IACvCC,YAAY,GAAGL,YAAY,CAACM,WAAW;IACvCC,aAAa,GAAGN,YAAY,CAACnB,YAAY;IACzC0B,YAAY,GAAGP,YAAY,CAACG,WAAW;IACvCK,YAAY,GAAGR,YAAY,CAACK,WAAW;EAExC,MAAMI,YAAY,GAAGrB,YAAY,GAAG,CAAC;EACrC,MAAMsB,YAAY,GAAGrB,YAAY,GAAG,CAAC;EACrC,MAAMsB,OAAO,GAAGlE,OAAO,CAAEgE,YAAY,EAAEL,YAAa,CAAC;EACrD,MAAMQ,OAAO,GAAGnE,OAAO,CAAEiE,YAAY,EAAEF,YAAa,CAAC;EACrD,IAAIvC,MAAM,GAAG,KAAK;EAClB,IAAK2C,OAAO,IAAID,OAAO,EAAG;IAEzB;IACA,IAAKb,QAAQ,EAAG;MAEf7B,MAAM,GAAGuB,oBAAoB,CAC5B7C,MAAM,CAAE0C,YAAY,EAAEkB,YAAa,CAAC,EAAE/D,KAAK,CAAE6C,YAAY,GAAG,CAAC,EAAEmB,YAAa,CAAC,EAC7E7D,MAAM,CAAEyC,YAAY,EAAEc,YAAa,CAAC,EAAE1D,KAAK,CAAE4C,YAAY,GAAG,CAAC,EAAEgB,YAAa,CAAC,EAC7ER,MAAM,EAAEF,oBAAoB,GAAGL,YAAY,EAC3CM,MAAM,EAAEF,oBAAoB,GAAGL,YAChC,CAAC;IAEF,CAAC,MAAM;MAENnB,MAAM,GAAGuB,oBAAoB,CAC5B7C,MAAM,CAAEyC,YAAY,EAAEc,YAAa,CAAC,EAAE1D,KAAK,CAAE4C,YAAY,GAAG,CAAC,EAAEgB,YAAa,CAAC,EAC7EzD,MAAM,CAAE0C,YAAY,EAAEkB,YAAa,CAAC,EAAE/D,KAAK,CAAE6C,YAAY,GAAG,CAAC,EAAEmB,YAAa,CAAC,EAC7Eb,MAAM,EAAEF,oBAAoB,GAAGL,YAAY,EAC3CQ,MAAM,EAAEF,oBAAoB,GAAGL,YAChC,CAAC;IAEF;EAED,CAAC,MAAM,IAAKuB,OAAO,EAAG;IAErB;IACA;IACA;;IAEA;IACA,MAAMC,MAAM,GAAG3D,QAAQ,CAAC0B,YAAY,CAAC,CAAC;IACtC/B,UAAU,CAAEN,mBAAmB,CAAE8C,YAAa,CAAC,EAAEiB,aAAa,EAAEO,MAAO,CAAC;IACxEA,MAAM,CAAC/B,YAAY,CAAEQ,UAAW,CAAC;;IAEjC;IACA,MAAMwB,GAAG,GAAGpE,SAAS,CAAE0C,YAAa,CAAC;IACrC,MAAM2B,GAAG,GAAGnE,UAAU,CAAEwC,YAAY,EAAEc,YAAa,CAAC;IACpDrD,UAAU,CAAEN,mBAAmB,CAAEuE,GAAI,CAAC,EAAEb,aAAa,EAAE9C,SAAU,CAAC;IAClEN,UAAU,CAAEN,mBAAmB,CAAEwE,GAAI,CAAC,EAAEd,aAAa,EAAE7C,UAAW,CAAC;;IAEnE;IACA,MAAM4D,YAAY,GAAGH,MAAM,CAACI,aAAa,CAAE9D,SAAU,CAAC;IACtD,MAAM+D,YAAY,GAAGL,MAAM,CAACI,aAAa,CAAE7D,UAAW,CAAC;IACvDa,MAAM,GACL+C,YAAY,IAAI/B,SAAS,CACxBI,YAAY,EAAEyB,GAAG,EAAEvB,UAAU,EAAED,UAAU,EAAEE,oBAAoB,EAC/DE,oBAAoB,EAAED,oBAAoB,EAAEG,MAAM,EAAED,MAAM,GAAG,CAAC,EAC9DkB,MAAM,EAAE,CAAEf,QACX,CAAC,IAEDoB,YAAY,IAAIjC,SAAS,CACxBI,YAAY,EAAE0B,GAAG,EAAExB,UAAU,EAAED,UAAU,EAAEE,oBAAoB,EAC/DE,oBAAoB,EAAED,oBAAoB,EAAEG,MAAM,EAAED,MAAM,GAAG,CAAC,EAC9DkB,MAAM,EAAE,CAAEf,QACX,CACA;IAED5C,QAAQ,CAACiC,gBAAgB,CAAE0B,MAAO,CAAC;EAEpC,CAAC,MAAM;IAEN;IACA;;IAEA;IACA,MAAMM,GAAG,GAAGzE,SAAS,CAAE2C,YAAa,CAAC;IACrC,MAAM+B,GAAG,GAAGxE,UAAU,CAAEyC,YAAY,EAAEkB,YAAa,CAAC;IACpD1D,UAAU,CAAEN,mBAAmB,CAAE4E,GAAI,CAAC,EAAEb,aAAa,EAAEjD,SAAU,CAAC;IAClER,UAAU,CAAEN,mBAAmB,CAAE6E,GAAI,CAAC,EAAEd,aAAa,EAAEhD,UAAW,CAAC;IAEnE,MAAM+D,cAAc,GAAGxB,OAAO,CAACoB,aAAa,CAAE5D,SAAU,CAAC;IACzD,MAAMiE,eAAe,GAAGzB,OAAO,CAACoB,aAAa,CAAE3D,UAAW,CAAC;IAC3D,IAAK+D,cAAc,IAAIC,eAAe,EAAG;MAExC;MACArD,MAAM,GAAGgB,SAAS,CACjBG,YAAY,EAAE+B,GAAG,EAAE7B,UAAU,EAAEC,UAAU,EAAEC,oBAAoB,EAC/DC,oBAAoB,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,MAAM,GAAG,CAAC,EAC9DC,OAAO,EAAEC,QACV,CAAC,IAAIb,SAAS,CACbG,YAAY,EAAEgC,GAAG,EAAE9B,UAAU,EAAEC,UAAU,EAAEC,oBAAoB,EAC/DC,oBAAoB,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,MAAM,GAAG,CAAC,EAC9DC,OAAO,EAAEC,QACV,CAAC;IAEF,CAAC,MAAM,IAAKuB,cAAc,EAAG;MAE5B,IAAKV,OAAO,EAAG;QAEd;QACA1C,MAAM,GAAGgB,SAAS,CACjBG,YAAY,EAAE+B,GAAG,EAAE7B,UAAU,EAAEC,UAAU,EAAEC,oBAAoB,EAC/DC,oBAAoB,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,MAAM,GAAG,CAAC,EAC9DC,OAAO,EAAEC,QACV,CAAC;MAEF,CAAC,MAAM;QAEN;QACA;QACA,MAAMe,MAAM,GAAG3D,QAAQ,CAAC0B,YAAY,CAAC,CAAC;QACtCiC,MAAM,CAACxC,IAAI,CAAEhB,SAAU,CAAC,CAACyB,YAAY,CAAEQ,UAAW,CAAC;QAEnD,MAAMwB,GAAG,GAAGpE,SAAS,CAAE0C,YAAa,CAAC;QACrC,MAAM2B,GAAG,GAAGnE,UAAU,CAAEwC,YAAY,EAAEc,YAAa,CAAC;QACpDrD,UAAU,CAAEN,mBAAmB,CAAEuE,GAAI,CAAC,EAAEb,aAAa,EAAE9C,SAAU,CAAC;QAClEN,UAAU,CAAEN,mBAAmB,CAAEwE,GAAI,CAAC,EAAEd,aAAa,EAAE7C,UAAW,CAAC;;QAEnE;QACA,MAAM4D,YAAY,GAAGH,MAAM,CAACI,aAAa,CAAE9D,SAAU,CAAC;QACtD,MAAM+D,YAAY,GAAGL,MAAM,CAACI,aAAa,CAAE7D,UAAW,CAAC;QACvDa,MAAM,GACL+C,YAAY,IAAI/B,SAAS,CACxBkC,GAAG,EAAEL,GAAG,EAAEvB,UAAU,EAAED,UAAU,EAAEE,oBAAoB,EACtDE,oBAAoB,EAAED,oBAAoB,EAAEG,MAAM,EAAED,MAAM,GAAG,CAAC,EAC9DkB,MAAM,EAAE,CAAEf,QACX,CAAC,IAEDoB,YAAY,IAAIjC,SAAS,CACxBkC,GAAG,EAAEJ,GAAG,EAAExB,UAAU,EAAED,UAAU,EAAEE,oBAAoB,EACtDE,oBAAoB,EAAED,oBAAoB,EAAEG,MAAM,EAAED,MAAM,GAAG,CAAC,EAC9DkB,MAAM,EAAE,CAAEf,QACX,CACA;QAED5C,QAAQ,CAACiC,gBAAgB,CAAE0B,MAAO,CAAC;MAEpC;IAED,CAAC,MAAM,IAAKS,eAAe,EAAG;MAE7B,IAAKX,OAAO,EAAG;QAEd;QACA1C,MAAM,GAAGgB,SAAS,CACjBG,YAAY,EAAEgC,GAAG,EAAE9B,UAAU,EAAEC,UAAU,EAAEC,oBAAoB,EAC/DC,oBAAoB,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,MAAM,GAAG,CAAC,EAC9DC,OAAO,EAAEC,QACV,CAAC;MAEF,CAAC,MAAM;QAEN;QACA;QACA,MAAMe,MAAM,GAAG3D,QAAQ,CAAC0B,YAAY,CAAC,CAAC;QACtCiC,MAAM,CAACxC,IAAI,CAAEf,UAAW,CAAC,CAACwB,YAAY,CAAEQ,UAAW,CAAC;QAEpD,MAAMwB,GAAG,GAAGpE,SAAS,CAAE0C,YAAa,CAAC;QACrC,MAAM2B,GAAG,GAAGnE,UAAU,CAAEwC,YAAY,EAAEc,YAAa,CAAC;QACpDrD,UAAU,CAAEN,mBAAmB,CAAEuE,GAAI,CAAC,EAAEb,aAAa,EAAE9C,SAAU,CAAC;QAClEN,UAAU,CAAEN,mBAAmB,CAAEwE,GAAI,CAAC,EAAEd,aAAa,EAAE7C,UAAW,CAAC;;QAEnE;QACA,MAAM4D,YAAY,GAAGH,MAAM,CAACI,aAAa,CAAE9D,SAAU,CAAC;QACtD,MAAM+D,YAAY,GAAGL,MAAM,CAACI,aAAa,CAAE7D,UAAW,CAAC;QACvDa,MAAM,GACL+C,YAAY,IAAI/B,SAAS,CACxBmC,GAAG,EAAEN,GAAG,EAAEvB,UAAU,EAAED,UAAU,EAAEE,oBAAoB,EACtDE,oBAAoB,EAAED,oBAAoB,EAAEG,MAAM,EAAED,MAAM,GAAG,CAAC,EAC9DkB,MAAM,EAAE,CAAEf,QACX,CAAC,IAEDoB,YAAY,IAAIjC,SAAS,CACxBmC,GAAG,EAAEL,GAAG,EAAExB,UAAU,EAAED,UAAU,EAAEE,oBAAoB,EACtDE,oBAAoB,EAAED,oBAAoB,EAAEG,MAAM,EAAED,MAAM,GAAG,CAAC,EAC9DkB,MAAM,EAAE,CAAEf,QACX,CACA;QAED5C,QAAQ,CAACiC,gBAAgB,CAAE0B,MAAO,CAAC;MAEpC;IAED;EAED;EAEA,OAAO5C,MAAM;AAEd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}