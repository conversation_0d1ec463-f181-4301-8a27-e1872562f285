{"ast": null, "code": "const HorizontalBlurShader = {\n  uniforms: {\n    tDiffuse: {\n      value: null\n    },\n    h: {\n      value: 1 / 512\n    }\n  },\n  vertexShader: (/* glsl */\n  `\n      varying vec2 vUv;\n\n      void main() {\n\n        vUv = uv;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n      }\n  `),\n  fragmentShader: (/* glsl */\n  `\n    uniform sampler2D tDiffuse;\n    uniform float h;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 sum = vec4( 0.0 );\n\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 4.0 * h, vUv.y ) ) * 0.051;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 3.0 * h, vUv.y ) ) * 0.0918;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 2.0 * h, vUv.y ) ) * 0.12245;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 1.0 * h, vUv.y ) ) * 0.1531;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x, vUv.y ) ) * 0.1633;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 1.0 * h, vUv.y ) ) * 0.1531;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 2.0 * h, vUv.y ) ) * 0.12245;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 3.0 * h, vUv.y ) ) * 0.0918;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 4.0 * h, vUv.y ) ) * 0.051;\n\n    \tgl_FragColor = sum;\n\n    }\n  `)\n};\nexport { HorizontalBlurShader };", "map": {"version": 3, "names": ["HorizontalBlur<PERSON><PERSON>er", "uniforms", "tDiffuse", "value", "h", "vertexShader", "fragmentShader"], "sources": ["C:\\Users\\<USER>\\Documents\\nicola-portfolio\\nicola-portfolio\\node_modules\\src\\shaders\\HorizontalBlurShader.ts"], "sourcesContent": ["/**\n * Two pass Gaussian blur filter (horizontal and vertical blur shaders)\n * - described in http://www.gamerendering.com/2008/10/11/gaussian-blur-filter-shader/\n *   and used in http://www.cake23.de/traveling-wavefronts-lit-up.html\n *\n * - 9 samples per pass\n * - standard deviation 2.7\n * - \"h\" and \"v\" parameters should be set to \"1 / width\" and \"1 / height\"\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type HorizontalBlurShaderUniforms = {\n  tDiffuse: IUniform<Texture | null>\n  h: IUniform<number>\n}\n\nexport interface IHorizontalBlurShader extends IShader<HorizontalBlurShaderUniforms> {}\n\nexport const HorizontalBlurShader: IHorizontalBlurShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    h: { value: 1.0 / 512.0 },\n  },\n  vertexShader: /* glsl */ `\n      varying vec2 vUv;\n\n      void main() {\n\n        vUv = uv;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n      }\n  `,\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float h;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 sum = vec4( 0.0 );\n\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 4.0 * h, vUv.y ) ) * 0.051;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 3.0 * h, vUv.y ) ) * 0.0918;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 2.0 * h, vUv.y ) ) * 0.12245;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 1.0 * h, vUv.y ) ) * 0.1531;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x, vUv.y ) ) * 0.1633;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 1.0 * h, vUv.y ) ) * 0.1531;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 2.0 * h, vUv.y ) ) * 0.12245;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 3.0 * h, vUv.y ) ) * 0.0918;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 4.0 * h, vUv.y ) ) * 0.051;\n\n    \tgl_FragColor = sum;\n\n    }\n  `,\n}\n"], "mappings": "AAoBO,MAAMA,oBAAA,GAA8C;EACzDC,QAAA,EAAU;IACRC,QAAA,EAAU;MAAEC,KAAA,EAAO;IAAK;IACxBC,CAAA,EAAG;MAAED,KAAA,EAAO,IAAM;IAAM;EAC1B;EACAE,YAAA;EAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAUzBC,cAAA;EAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwB7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}