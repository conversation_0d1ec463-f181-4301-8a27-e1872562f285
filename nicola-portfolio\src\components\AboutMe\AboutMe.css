.about-container {
  margin: 4rem 0;
  position: relative;
}

.about-container::after,
.about-container::before {
  content: " ";
  width: 28rem;
  height: 28rem;
  border-radius: 28.125rem;
  background: var(--primary-color);
  position: absolute;
  z-index: -1;
  filter: blur(200px);
}

.about-container::after {
  top: -3rem;
  right: -5rem;
}

.about-container::before {
  background: var(--secondary-color);
  bottom: 0rem;
  left: 0rem;
}

.about-container h5 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 3.5rem;
  color: var(--text-color);
}

.about-content {
  display: flex;
  gap: 4rem;
  align-items: flex-start;
}

.about-description {
  flex: 1;
  max-width: 50%;
}

.about-text h3 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-text p {
  font-size: 1rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  text-align: justify;
}

.typeracer-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.typeracer-section h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.typeracer-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(22, 17, 47, 0.3);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.typeracer-badge:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border-color: var(--primary-color);
}

.typeracer-link {
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  overflow: hidden;
}

.typeracer-link:hover {
  transform: scale(1.05);
}

.typeracer-img {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.typeracer-description {
  font-size: 0.9rem;
  color: var(--text-color);
  text-align: center;
  margin: 0;
  opacity: 0.8;
  line-height: 1.5;
}

.about-cards {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (max-width: 1025px) {
  .about-content {
    gap: 3rem;
  }
  
  .about-text h3 {
    font-size: 1.8rem;
  }
  
  .about-text p {
    font-size: 0.95rem;
  }

  .typeracer-section h4 {
    font-size: 1.2rem;
  }

  .typeracer-badge {
    padding: 1.2rem;
  }
}

@media (max-width: 768px) {
  .about-content {
    flex-direction: column;
    gap: 3rem;
  }
  
  .about-description {
    max-width: 100%;
  }
  
  .about-container h5 {
    font-size: 1.3rem;
    margin-bottom: 2rem;
  }
  
  .about-text h3 {
    font-size: 1.6rem;
    text-align: center;
  }
  
  .about-text p {
    text-align: left;
  }

  .typeracer-section {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
  }

  .typeracer-section h4 {
    font-size: 1.1rem;
    text-align: center;
  }

  .typeracer-badge {
    padding: 1rem;
  }

  .typeracer-description {
    font-size: 0.85rem;
  }
  
  .about-container::after,
  .about-container::before {
    width: 18rem;
    height: 18rem;
  }
}

@media (max-width: 480px) {
  .about-container {
    margin: 2rem 0;
  }

  .about-content {
    gap: 2rem;
    align-items: center;
  }

  .about-cards {
    align-items: center;
  }
  
  .about-text h3 {
    font-size: 1.4rem;
  }
  
  .about-text p {
    font-size: 0.9rem;
    line-height: 1.6;
  }

  .typeracer-section {
    margin-top: 1rem;
    padding-top: 1rem;
  }

  .typeracer-section h4 {
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  .typeracer-badge {
    padding: 0.8rem;
    gap: 0.8rem;
  }

  .typeracer-description {
    font-size: 0.8rem;
  }
}
