.about-container {
  margin: 4rem 0;
  position: relative;
}

.about-container::after,
.about-container::before {
  content: " ";
  width: 28rem;
  height: 28rem;
  border-radius: 28.125rem;
  background: var(--primary-color);
  position: absolute;
  z-index: -1;
  filter: blur(200px);
}

.about-container::after {
  top: -3rem;
  right: -5rem;
}

.about-container::before {
  background: var(--secondary-color);
  bottom: 0rem;
  left: 0rem;
}

.about-container h5 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 3.5rem;
  color: var(--text-color);
}

.about-content {
  display: flex;
  gap: 4rem;
  align-items: flex-start;
}

.about-description {
  flex: 1;
  max-width: 50%;
}

.about-text h3 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  background: var(--gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-text p {
  font-size: 1rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  text-align: justify;
}

.about-cards {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (max-width: 1025px) {
  .about-content {
    gap: 3rem;
  }
  
  .about-text h3 {
    font-size: 1.8rem;
  }
  
  .about-text p {
    font-size: 0.95rem;
  }
}

@media (max-width: 768px) {
  .about-content {
    flex-direction: column;
    gap: 3rem;
  }
  
  .about-description {
    max-width: 100%;
  }
  
  .about-container h5 {
    font-size: 1.3rem;
    margin-bottom: 2rem;
  }
  
  .about-text h3 {
    font-size: 1.6rem;
    text-align: center;
  }
  
  .about-text p {
    text-align: left;
  }
  
  .about-container::after,
  .about-container::before {
    width: 18rem;
    height: 18rem;
  }
}

@media (max-width: 480px) {
  .about-container {
    margin: 2rem 0;
  }
  
  .about-content {
    gap: 2rem;
  }
  
  .about-text h3 {
    font-size: 1.4rem;
  }
  
  .about-text p {
    font-size: 0.9rem;
    line-height: 1.6;
  }
}
